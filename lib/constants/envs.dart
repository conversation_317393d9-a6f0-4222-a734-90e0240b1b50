import 'dart:convert' show base64, utf8;
import 'dart:io' as io show File;

import 'package:path/path.dart' as p show join;
import 'package:path_provider/path_provider.dart' show getApplicationCacheDirectory;

import 'env.dart' show Env, EnvProd;
import 'release.dart' show Release;

// TODO(EVERYONE)
// The field will be the environment of packaging at first,
// then gets updated by fetching the latest version.
bool isAuditing = false;

bool get envIsProd => envActive is EnvProd;

bool get isSealed => !EnvOverrides.sealedOverrode && Release.sealed;

abstract final class EnvOverrides {
  static bool _initialized = false;

  // static late final io.File _fileEnv;
  static late final io.File _fileSealed;
  static late final io.File _fileJWT;

  static Future<void> initialize() async {
    if (_initialized) {
      return;
    }
    final cacheDir = await getApplicationCacheDirectory();
    // _fileEnv = io.File(p.join(cacheDir.path, 'erornvdoerv'));
    _fileSealed = io.File(p.join(cacheDir.path, 'sreoredardvl'));
    _fileJWT = io.File(p.join(cacheDir.path, 'treowedajdva'));
    _initialized = true;
  }

  static void _write(io.File file, String? value) {
    if (value == null) {
      file.deleteSync();
      return;
    }
    final bytes = utf8.encode(base64.encode(utf8.encode(value)));
    file.writeAsBytesSync(bytes);
  }

  // static Env? get localOverrode {
  //   try {
  //     if (!_fileEnv.existsSync()) {
  //       return null;
  //     }
  //     final env = utf8.decode(
  //       base64.decode(utf8.decode(_fileEnv.readAsBytesSync())),
  //     );
  //     return Env.values.firstWhereOrNull((e) => e.env == env);
  //   } catch (e) {
  //     return null;
  //   }
  // }
  //
  // static void overrideLocal(Env? value) {
  //   _write(_fileEnv, value?.env);
  // }

  static bool get sealedOverrode {
    try {
      if (!_fileSealed.existsSync()) {
        return false;
      }
      final sealed = utf8.decode(
        base64.decode(utf8.decode(_fileSealed.readAsBytesSync())),
      );
      return sealed == 'true';
    } catch (e) {
      return false;
    }
  }

  static void overrideSealed(bool? value) {
    _write(_fileSealed, value?.toString());
  }

  static String? get jwtOverrode {
    // return 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************.z8Iov7li8BWHwtmgWykzFe82ZLygxNy46z83RbgZI_0';
    try {
      if (!_fileJWT.existsSync()) {
        return null;
      }
      final jwt = utf8.decode(
        base64.decode(utf8.decode(_fileJWT.readAsBytesSync())),
      );
      return jwt;
    } catch (e) {
      return null;
    }
  }

  static void overrideJWT(String? value) {
    if (value?.isEmpty == true) {
      value = null;
    }
    _write(_fileJWT, value?.toString());
  }
}

Env get envActive => Env.$active;

bool get envOverrode => Env.overrode;

String get envBotUtilUrl => envActive.BOT_UTIL_URL;

String get envBotUtilKey => envActive.BOT_UTIL_KEY;

String get envBotUtilPingUrl => envActive.BOT_UTIL_PING_URL;

String get envBotUtilPingKey => envActive.BOT_UTIL_PING_KEY;

String get envBotUtilFCMUrl => envActive.BOT_UTIL_FCM_URL;

String get envBotUtilFCMKey => envActive.BOT_UTIL_FCM_KEY;

String get envApiKeyAstroxOp => envActive.API_KEY_ASTROX_OP;

String get envApiUrlAstroxOp => envActive.API_URL_ASTROX_OP;

String get envApiUrlAstroxGw => envActive.API_URL_ASTROX_GW;

String get envApiUrlService => envActive.API_URL_SERVICE;

String get envApiUrlFE => envActive.API_URL_FE;

String get envApiIdPrivy => envActive.API_ID_PRIVY;

String get envApiIdPrivyClient => envActive.API_ID_PRIVY_CLIENT;

String get envUrlEULA => envActive.URL_EULA;

String get envUrlPP => envActive.URL_PP;

String get envUrlConfig => envActive.URL_CONFIG;

String get envUrlCard3 => envActive.URL_CARD3;

String get envUrlSocial => envActive.URL_SOCIAL;

String get envUrlShort => envActive.URL_SHORT;

String get envUrlWebsite => envActive.URL_WEBSITE;
