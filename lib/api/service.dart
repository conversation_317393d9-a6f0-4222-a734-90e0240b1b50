import 'dart:io' show HttpHeaders;

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:me_extensions/me_extensions.dart';
import 'package:me_misc/me_misc.dart';

import '/constants/api_path.dart';
import '/constants/envs.dart' show envApiUrlService;
import '/internals/box.dart' show BoxService;
import '/models/business.dart';
import '/models/card.dart';
import '/models/user.dart';
import '/provider/api.dart' hide apiServiceProvider;
import '/routes/card3_routes.dart' show Routes;

part 'service.freezed.dart';

part 'service.g.dart';

final class ServiceApi {
  ServiceApi(this.ref);

  final Ref ref;

  late final http = ref.read(httpProvider).clone(options: BaseOptions(baseUrl: envApiUrlService));

  final errorCodes = const ApiServiceErrorCode._();

  Future<String> getDemoJWT() async {
    final res = await http
        .get(
          '/demo/login',
          options: Options(
            headers: <String, dynamic>{
              HttpHeaders.authorizationHeader: '', // Anonymous.
            },
          ),
        )
        .retry();
    final rep = Rep.fromJson(res.data, (v) => v as String);
    return rep.data;
  }

  Future<String> getRefreshJWT({CancelToken? cancelToken}) async {
    final res = await http
        .get(
          card3Api.user.refreshToken,
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(res.data, (v) => v as String);
    return rep.data;
  }

  Future<String> login({
    required String token,
    required String wallet,
    String? referral,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .post(
          card3Api.login,
          data: <String, dynamic>{
            'token': token,
            'wallet': wallet,
            'referral': referral,
          },
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(res.data, (v) => v as String);
    return rep.data;
  }

  Future<UserInfo> getSelfUserInfo({
    String? token,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .get(
          card3Api.user.info,
          cancelToken: cancelToken,
          options: Options(
            headers: {
              if (token != null) HttpHeaders.authorizationHeader: 'Bearer $token',
            },
          ),
        )
        .retry();
    final rep = Rep.fromJson(
      res.data,
      (v) => UserInfo.fromJson(v.asJson()),
    );
    return rep.data;
  }

  Future<Map<String, dynamic>> updateUserInfo({
    String? redirectUrl,
    int? currentType,
    String? avatar,
    String? name,
    String? title,
    String? company,
    String? profileMode,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .put(
          card3Api.user.info,
          data: <String, Object>{
            'redirectUrl': ?redirectUrl,
            'currentType': ?currentType,
            'avatar': ?avatar,
            'name': ?name,
            'title': ?title,
            'company': ?company,
            'profileMode': ?profileMode,
          },
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(res.data, (v) => v as Map<String, dynamic>);
    return rep.data;
  }

  Future<String> deleteUser({
    CancelToken? cancelToken,
  }) async {
    final res = await http.delete(
      card3Api.user.delete,
      cancelToken: cancelToken,
    );
    final rep = Rep.fromJson(res.data, (v) => v as String);
    return rep.data;
  }

  Future<UserSettingsRequest> getUserSettings({
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .get(
          card3Api.user.settings,
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(res.data, (v) => UserSettingsRequest.fromJson(v.asJson()));
    return rep.data;
  }

  Future<UserSettingsRequest> updateSettings(
    UserSettingsRequest request, {
    CancelToken? cancelToken,
  }) async {
    final res = await retryWith(
      () => http.put(
        card3Api.user.settings,
        data: request.toJson(),
        cancelToken: cancelToken,
      ),
    );
    final rep = Rep.fromJson(res.data, (v) => UserSettingsRequest.fromJson(v.asJson()));
    return rep.data;
  }

  Future<List<CardInfo>> getMyCards({
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .get(
          card3Api.user.cards,
          cancelToken: cancelToken,
        )
        .retry();
    final rep = ListRep.fromJson(
      res.data,
      (v) => CardInfo.fromJson(v.asJson()),
    );
    return rep.list;
  }

  Future<CardInfo> getCard({
    required String cardCode,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .post(
          card3Api.wallet.query,
          data: <String, dynamic>{
            'cardCode': cardCode,
          },
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(res.data, (v) => CardInfo.fromJson(v.asJson()));
    return rep.data;
  }

  Future<String> activeCardByNfc({
    required String uid,
    String? ctr,
    String? cmac,
    String? activeCode,
    CancelToken? cancelToken,
  }) async {
    final data = <String, dynamic>{};

    // 如果有ctr和cmac，使用NFC方式激活
    if (ctr != null && cmac != null && ctr.isNotEmpty && cmac.isNotEmpty) {
      data['uid'] = uid;
      data['ctr'] = ctr;
      data['cmac'] = cmac;
    }
    // 如果有activeCode，使用激活码方式激活
    else if (activeCode != null && activeCode.isNotEmpty) {
      data['cardCode'] = uid;
      data['activeCode'] = activeCode;
    }
    // 如果都没有，默认使用uid
    else {
      data['uid'] = uid;
    }

    final res = await http
        .post(
          card3Api.user.activate,
          data: data,
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(res.data, (v) => v as String);
    return rep.data;
  }

  Future<List<Social>> socialQuery({
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .get(
          card3Api.user.socialGet,
          cancelToken: cancelToken,
        )
        .retry();
    final rep = ListRep.fromJson(res.data, (v) => Social.fromJson(v.asJson()));
    return rep.list;
  }

  Future<Social> socialAdd({
    required String handleName,
    required String platformUrl,
    required String platformName,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .post(
          card3Api.user.socialAdd,
          data: <String, dynamic>{
            'handleName': handleName,
            'platformUrl': platformUrl,
            'platformName': platformName,
          },
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(res.data, (v) => Social.fromJson(v.asJson()));
    return rep.data;
  }

  Future<Social> socialUpdate({
    required String socialId,
    required String platformName,
    required String handleName,
    required String platformUrl,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .put(
          card3Api.user.socialUpdate(socialId: socialId),
          data: <String, dynamic>{
            'platformName': platformName,
            'handleName': handleName,
            'platformUrl': platformUrl,
          },
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(res.data, (v) => Social.fromJson(v.asJson()));
    return rep.data;
  }

  Future<String> socialDelete({
    required int socialId,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .delete(
          card3Api.user.socialDel(socialId: socialId.toString()),
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(res.data, (v) => v as String);
    return rep.data;
  }

  Future<String> socialsReorder({
    required Map<String, int> idInOrders,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .put(
          card3Api.user.socialSort,
          data: <String, dynamic>{
            'sorts': idInOrders,
          },
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(res.data, (v) => v as String);
    return rep.data;
  }

  Future<Paged<Message>> listMessages({
    required int page,
    int size = 20,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .get(
          card3Api.user.listMessages,
          queryParameters: <String, dynamic>{
            'pageNum': page,
            'pageSize': 20,
          },
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(
      res.data,
      (v) => Paged.fromJson(
        v.asJson(),
        (v) => Message.fromJson(v.asJson()),
      ),
    );
    return rep.data;
  }

  Future<Paged<Point>> getPoints({
    required int pageNum,
    required int pageSize,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .get(
          card3Api.user.points,
          queryParameters: <String, dynamic>{
            'pageNum': pageNum,
            'pageSize': pageSize,
          },
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(
      res.data,
      (v) => Paged.fromJson(
        v.asJson(),
        (v) => Point.fromJson(v.asJson()),
      ),
    );
    return rep.data;
  }

  Future<CoverInfo> getCoverInfo({
    required String code,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .get(
          card3Api.cover.getInfo,
          queryParameters: <String, dynamic>{
            'code': code,
          },
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(res.data, (v) => CoverInfo.fromJson(v.asJson()));
    return rep.data;
  }

  Future<CreateCardCoverResponse> createCardCover({
    required String code,
    required String username,
    String? fileContent,
    String? title,
    String? company,
    String? eventId,
    String? previewContent,
    CancelToken? cancelToken,
  }) async {
    final res = await http.post(
      card3Api.cover.create,
      data: <String, dynamic>{
        'code': code,
        'username': username,
        'fileContent': fileContent,
        'title': title,
        'company': company,
        'eventId': eventId,
        'previewContent': previewContent,
      },
      cancelToken: cancelToken,
    );
    final rep = Rep.fromJson(res.data, (v) => CreateCardCoverResponse.fromJson(v.asJson()));
    return rep.data;
  }

  Future<Paged<Connection>> getConnections({
    required int pageNum,
    required int pageSize,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .get(
          card3Api.user.connections,
          queryParameters: <String, dynamic>{
            'pageNum': pageNum,
            'pageSize': pageSize,
          },
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(
      res.data,
      (v) => Paged.fromJson(
        v.asJson(),
        (v) => Connection.fromJson(v.asJson()),
      ),
    );
    return rep.data;
  }

  Future<Paged<ReferralLog>> getReferralLogs({
    required int pageNum,
    required int pageSize,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .get(
          card3Api.user.referralLogs,
          queryParameters: <String, dynamic>{
            'pageNum': pageNum,
            'pageSize': pageSize,
          },
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(
      res.data,
      (v) => Paged.fromJson(
        v.asJson(),
        (v) => ReferralLog.fromJson(v.asJson()),
      ),
    );
    return rep.data;
  }

  Future<String> uploadAvatar({
    required String fileContent,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .post(
          card3Api.user.uploadAvatar,
          data: <String, dynamic>{
            'fileContent': fileContent,
          },
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(res.data, (v) => v as String);
    return rep.data;
  }

  Future<UserInfo> getPublicProfile({
    required String code,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .get(
          card3Api.profile.queryPub(cardCode: code),
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(res.data, (v) => UserInfo.fromJson(v.asJson()));
    return rep.data;
  }

  Future<List<Social>> getPublicSocials({
    required String code,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .get(
          card3Api.profile.socialGetPub(cardCode: code),
          cancelToken: cancelToken,
        )
        .retry();
    final rep = ListRep.fromJson(res.data, (v) => Social.fromJson(v.asJson()));
    return rep.list;
  }

  // ETHCC相关API方法
  Future<List<String>> getEthccTopics({
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .get(
          card3Api.ethcc.topics,
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(
      res.data,
      (v) => (v as List).map((item) => item.toString()).toList(),
    );
    return rep.data;
  }

  Future<List<String>> getEthccRoles({
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .get(
          card3Api.ethcc.roles,
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(
      res.data,
      (v) => (v as List).map((item) => item.toString()).toList(),
    );
    return rep.data;
  }

  Future<EthccProfile?> getEthccProfile({
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .get(
          card3Api.ethcc.profile,
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(
      res.data,
      (v) => v != null ? EthccProfile.fromJson(v.asJson()) : null,
    );
    return rep.data;
  }

  Future<EthccProfile?> getEthccPublicProfile({
    required String code,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .get(
          card3Api.ethcc.queryPub(code: code),
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(
      res.data,
      (v) => v != null ? EthccProfile.fromJson(v.asJson()) : null,
    );
    return rep.data;
  }

  Future<void> updateEthccTopics({
    required String topics,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .post(
          card3Api.ethcc.profileTopics,
          data: <String, dynamic>{
            'topics': topics,
          },
          cancelToken: cancelToken,
        )
        .retry();
    Rep.fromJson(res.data, (v) => v);
  }

  Future<void> updateEthccRole({
    required String role,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .post(
          card3Api.ethcc.profileRoles,
          data: switch (role.trim()) {
            final r when r.isNotEmpty => {'role': r},
            _ => null,
          },
          cancelToken: cancelToken,
        )
        .retry();
    Rep.fromJson(res.data, (v) => v);
  }

  Future<void> updateEthccGithubHandle({
    required String githubHandle,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .post(
          card3Api.ethcc.profileGithubHandle,
          data: <String, dynamic>{
            'githubHandle': githubHandle,
          },
          cancelToken: cancelToken,
        )
        .retry();
    Rep.fromJson(res.data, (v) => v);
  }

  Future<UserRelation> toggleUserFollow({
    required String code,
    required bool follow,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .post(
          follow ? card3Api.user.follow(code: code) : card3Api.user.unfollow(code: code),
          cancelToken: cancelToken,
        )
        .retry();
    UserRelation? relation;
    final rep = Rep.fromJson(
      res.data,
      (v) => v == null ? null : UserRelation.fromJson(v.asJson()),
    );
    relation = rep.data;
    relation ??= await getUserRelation(code: code, cancelToken: cancelToken);
    return relation;
  }

  Future<UserRelation> getUserRelation({
    required String code,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .get(
          card3Api.user.relation(referralCode: code),
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(res.data, (v) => UserRelation.fromJson(v.asJson()));
    return rep.data;
  }

  Future<Paged<UserFromRelation>> getFollowingList({
    required int page,
    int size = 20,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .get(
          card3Api.user.followingList,
          queryParameters: <String, dynamic>{
            'pageNum': page,
            'pageSize': size,
          },
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(
      res.data,
      (v) => Paged.fromJson(
        v.asJson(),
        (v) => UserFromRelation.fromJson(v.asJson()),
      ),
    );
    return rep.data;
  }

  Future<Paged<UserFromRelation>> getFollowerList({
    required int page,
    int size = 20,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .get(
          card3Api.user.followerList,
          queryParameters: <String, dynamic>{
            'pageNum': page,
            'pageSize': size,
          },
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(
      res.data,
      (v) => Paged.fromJson(
        v.asJson(),
        (v) => UserFromRelation.fromJson(v.asJson()),
      ),
    );
    return rep.data;
  }
}

class ApiServiceInterceptor extends QueuedInterceptor {
  @override
  void onResponse(response, handler) {
    final options = response.requestOptions;
    // Skip when the request is not a service api request.
    if (!options.uri.toString().startsWith(envApiUrlService)) {
      handler.next(response);
      return;
    }

    if (response.data case {
      'code': final int code,
    } when code == 401) {
      BoxService.clearUserBox();
      meNavigator.removeNamedAndPushAndRemoveUntil(Routes.login.name);
    }

    handler.next(response);
  }
}

/// Edition 20250318.
final class ApiServiceErrorCode {
  const ApiServiceErrorCode._();

  int get roomClosed => 3002;
}

@freezed
sealed class ApiServiceUserToken with _$ApiServiceUserToken {
  const factory ApiServiceUserToken({
    @JsonKey(name: 'tokenAddress') required String address,
    @JsonKey(name: 'totalQuantity') required Decimal totalQuantity,
    @JsonKey(name: 'averageCost') required Decimal costAverage,
    @JsonKey(name: 'totalCost') required Decimal costTotal,
    @JsonKey(name: 'accuCost') required Decimal costAccumulate,
    @JsonKey(name: 'accuQuoteCost') required Decimal costQuoteAccumulate,
    @JsonKey(name: 'realizedPnl') required Decimal pnlRealized,
    @JsonKey(name: 'unrealizedPnl') required Decimal pnlUnrealized,
    @JsonKey(name: 'totalPnl') required Decimal pnlTotal,
    @JsonKey(name: 'quoteTotalPnl') required Decimal pnlQuoteTotal,
  }) = _ApiServiceUserToken;

  factory ApiServiceUserToken.fromJson(Map<String, Object?> json) => _$ApiServiceUserTokenFromJson(json);
}
