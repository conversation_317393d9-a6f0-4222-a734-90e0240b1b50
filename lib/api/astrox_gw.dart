import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:me_extensions/me_extensions.dart';

import '/constants/envs.dart' show envApiUrlAstroxGw;
import '/provider/api.dart' hide apiServiceProvider;

part 'astrox_gw.freezed.dart';

part 'astrox_gw.g.dart';

final class AstroxGwApi {
  AstroxGwApi(this.ref);

  final Ref ref;

  late final http = ref.read(httpProvider).clone(options: BaseOptions(baseUrl: envApiUrlAstroxGw));

  Future<AstroxTicker> getPrice({
    required String symbol,
  }) async {
    final res = await http
        .post(
          '/api/price/latestPrice',
          data: {'symbol': '$symbol/USDT'},
        )
        .retry();
    final rep = Rep.fromJson(res.data, (v) => AstroxTicker.fromJson(v.asJson()));
    return rep.data;
  }
}

@freezed
sealed class AstroxTicker with _$AstroxTicker {
  const factory AstroxTicker({
    @JsonKey(name: 'price') @Default(0) double price,
    @JsonKey(name: 'symbol') required String symbol,
    @JsonKey(name: 'timestamp') required int timestamp,
  }) = _AstroxTicker;

  factory AstroxTicker.fromJson(Map<String, Object?> json) => _$AstroxTickerFromJson(json);
}
