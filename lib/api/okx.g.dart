// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'okx.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_OKXWalletProfile _$OKXWalletProfileFromJson(Map json) => _OKXWalletProfile(
  summary: json['walletAssetSummary'] == null
      ? null
      : OKXWalletProfileSummary.fromJson(
          Map<String, dynamic>.from(json['walletAssetSummary'] as Map),
        ),
  tokens: OKXWalletProfileTokens.fromJson(
    Map<String, dynamic>.from(json['tokens'] as Map),
  ),
);

Map<String, dynamic> _$OKXWalletProfileToJson(_OKXWalletProfile instance) =>
    <String, dynamic>{
      'walletAssetSummary': instance.summary?.toJson(),
      'tokens': instance.tokens.toJson(),
    };

_OKXWalletProfileSummary _$OKXWalletProfileSummaryFromJson(Map json) =>
    _OKXWalletProfileSummary(
      tokenTotalCurrencyAmount: const NumberDecimalRequiredConverter().fromJson(
        json['tokenTotalCurrencyAmount'] as Object,
      ),
    );

Map<String, dynamic> _$OKXWalletProfileSummaryToJson(
  _OKXWalletProfileSummary instance,
) => <String, dynamic>{
  'tokenTotalCurrencyAmount': const NumberDecimalRequiredConverter().toJson(
    instance.tokenTotalCurrencyAmount,
  ),
};

_OKXWalletProfileTokens _$OKXWalletProfileTokensFromJson(Map json) =>
    _OKXWalletProfileTokens(
      total: (json['total'] as num).toInt(),
      pageSize: (json['pageSize'] as num).toInt(),
      currentPage: (json['currentPage'] as num).toInt(),
      isOverExplorerLimit: json['isOverExplorerLimit'] as bool,
      tokenlist: (json['tokenlist'] as List<dynamic>)
          .map(
            (e) => OKXWalletProfileToken.fromJson(
              Map<String, dynamic>.from(e as Map),
            ),
          )
          .toList(),
    );

Map<String, dynamic> _$OKXWalletProfileTokensToJson(
  _OKXWalletProfileTokens instance,
) => <String, dynamic>{
  'total': instance.total,
  'pageSize': instance.pageSize,
  'currentPage': instance.currentPage,
  'isOverExplorerLimit': instance.isOverExplorerLimit,
  'tokenlist': instance.tokenlist.map((e) => e.toJson()).toList(),
};

_OKXWalletProfileToken _$OKXWalletProfileTokenFromJson(Map json) =>
    _OKXWalletProfileToken(
      realBalance: const NumberDecimalRequiredConverter().fromJson(
        json['coinAmount'] as Object,
      ),
      balance: const NumberDecimalRequiredConverter().fromJson(
        json['coinAmountOrigin'] as Object,
      ),
      valueUsd: const NumberDecimalRequiredConverter().fromJson(
        json['currencyAmount'] as Object,
      ),
      symbol: json['symbol'] as String,
      logo: json['imageUrl'] as String,
      name: json['name'] as String,
      coinBalanceDetails: (json['coinBalanceDetails'] as List<dynamic>)
          .map(
            (e) => OKXWalletProfileTokenDetail.fromJson(
              Map<String, dynamic>.from(e as Map),
            ),
          )
          .toList(),
      coinPriceInfo: OKXWalletProfileTokenPrice.fromJson(
        Map<String, dynamic>.from(json['coinPriceInfo'] as Map),
      ),
    );

Map<String, dynamic> _$OKXWalletProfileTokenToJson(
  _OKXWalletProfileToken instance,
) => <String, dynamic>{
  'coinAmount': const NumberDecimalRequiredConverter().toJson(
    instance.realBalance,
  ),
  'coinAmountOrigin': const NumberDecimalRequiredConverter().toJson(
    instance.balance,
  ),
  'currencyAmount': const NumberDecimalRequiredConverter().toJson(
    instance.valueUsd,
  ),
  'symbol': instance.symbol,
  'imageUrl': instance.logo,
  'name': instance.name,
  'coinBalanceDetails': instance.coinBalanceDetails
      .map((e) => e.toJson())
      .toList(),
  'coinPriceInfo': instance.coinPriceInfo.toJson(),
};

_OKXWalletProfileTokenDetail _$OKXWalletProfileTokenDetailFromJson(Map json) =>
    _OKXWalletProfileTokenDetail(
      name: json['name'] as String,
      symbol: json['symbol'] as String,
      address: json['address'] as String?,
      decimals: (json['decimalNum'] as num).toInt(),
      logo: json['imageUrl'] as String,
      chainName: json['chainName'] as String,
      chainId: (json['chainId'] as num).toInt(),
      chainIndex: (json['chainIndex'] as num).toInt(),
      systemToken: json['systemToken'] as bool,
      chainImageUrl: json['chainImageUrl'] as String,
      coinPriceVo: OKXWalletProfileTokenPrice.fromJson(
        Map<String, dynamic>.from(json['coinPriceVo'] as Map),
      ),
      isRiskType: json['isRiskType'] as bool,
      userAddress: json['userAddress'] as String,
      realBalance: const NumberDecimalRequiredConverter().fromJson(
        json['coinAmount'] as Object,
      ),
      balance: const NumberDecimalRequiredConverter().fromJson(
        json['coinAmountOrigin'] as Object,
      ),
      valueUsd: const NumberDecimalRequiredConverter().fromJson(
        json['currencyAmount'] as Object,
      ),
    );

Map<String, dynamic> _$OKXWalletProfileTokenDetailToJson(
  _OKXWalletProfileTokenDetail instance,
) => <String, dynamic>{
  'name': instance.name,
  'symbol': instance.symbol,
  'address': instance.address,
  'decimalNum': instance.decimals,
  'imageUrl': instance.logo,
  'chainName': instance.chainName,
  'chainId': instance.chainId,
  'chainIndex': instance.chainIndex,
  'systemToken': instance.systemToken,
  'chainImageUrl': instance.chainImageUrl,
  'coinPriceVo': instance.coinPriceVo.toJson(),
  'isRiskType': instance.isRiskType,
  'userAddress': instance.userAddress,
  'coinAmount': const NumberDecimalRequiredConverter().toJson(
    instance.realBalance,
  ),
  'coinAmountOrigin': const NumberDecimalRequiredConverter().toJson(
    instance.balance,
  ),
  'currencyAmount': const NumberDecimalRequiredConverter().toJson(
    instance.valueUsd,
  ),
};

_OKXWalletProfileTokenPrice _$OKXWalletProfileTokenPriceFromJson(Map json) =>
    _OKXWalletProfileTokenPrice(
      address: json['address'] as String?,
      hasPrice: json['hasPrice'] as bool,
      price: const NumberDecimalRequiredConverter().fromJson(
        json['price'] as Object,
      ),
      hasPercent: json['hasPercent'] as bool,
      priceChangePercent24h: const NumberDecimalRequiredConverter().fromJson(
        json['priceChangePercent24h'] as Object,
      ),
      chainId: (json['chainId'] as num).toInt(),
    );

Map<String, dynamic> _$OKXWalletProfileTokenPriceToJson(
  _OKXWalletProfileTokenPrice instance,
) => <String, dynamic>{
  'address': instance.address,
  'hasPrice': instance.hasPrice,
  'price': const NumberDecimalRequiredConverter().toJson(instance.price),
  'hasPercent': instance.hasPercent,
  'priceChangePercent24h': const NumberDecimalRequiredConverter().toJson(
    instance.priceChangePercent24h,
  ),
  'chainId': instance.chainId,
};

_WalletPortfolioOKX2 _$WalletPortfolioOKX2FromJson(Map json) =>
    _WalletPortfolioOKX2(
      summary: OKXWalletProfileSummary.fromJson(
        Map<String, dynamic>.from(json['summary'] as Map),
      ),
      tokens: (json['tokens'] as List<dynamic>)
          .map(
            (e) => OKXWalletProfileToken.fromJson(
              Map<String, dynamic>.from(e as Map),
            ),
          )
          .toList(),
    );

Map<String, dynamic> _$WalletPortfolioOKX2ToJson(
  _WalletPortfolioOKX2 instance,
) => <String, dynamic>{
  'summary': instance.summary.toJson(),
  'tokens': instance.tokens.map((e) => e.toJson()).toList(),
};
