import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:me_extensions/me_extensions.dart';

import '/constants/envs.dart' show envApiUrlAstroxOp;
import '/models/business.dart';
import '/provider/api.dart' hide apiServiceProvider;

final class AstroxApi {
  AstroxApi(this.ref);

  final Ref ref;

  late final http = ref.read(httpProvider).clone(options: BaseOptions(baseUrl: envApiUrlAstroxOp));

  Future<List<Network>> getNetworks({
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .get(
          '/multichain/networkConfig',
          cancelToken: cancelToken,
        )
        .retry();
    final rep = ListRep.fromJson(res.data, (v) => Network.fromJson(v.asJson()));
    return rep.list;
  }
}
