// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'okx.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

/// @nodoc
mixin _$OKXWalletProfile {
  @JsonKey(name: 'walletAssetSummary')
  OKXWalletProfileSummary? get summary;
  @JsonKey(name: 'tokens')
  OKXWalletProfileTokens get tokens;

  /// Create a copy of OKXWalletProfile
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $OKXWalletProfileCopyWith<OKXWalletProfile> get copyWith =>
      _$OKXWalletProfileCopyWithImpl<OKXWalletProfile>(
        this as OKXWalletProfile,
        _$identity,
      );

  /// Serializes this OKXWalletProfile to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is OKXWalletProfile &&
            (identical(other.summary, summary) || other.summary == summary) &&
            (identical(other.tokens, tokens) || other.tokens == tokens));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, summary, tokens);

  @override
  String toString() {
    return 'OKXWalletProfile(summary: $summary, tokens: $tokens)';
  }
}

/// @nodoc
abstract mixin class $OKXWalletProfileCopyWith<$Res> {
  factory $OKXWalletProfileCopyWith(
    OKXWalletProfile value,
    $Res Function(OKXWalletProfile) _then,
  ) = _$OKXWalletProfileCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'walletAssetSummary') OKXWalletProfileSummary? summary,
    @JsonKey(name: 'tokens') OKXWalletProfileTokens tokens,
  });

  $OKXWalletProfileSummaryCopyWith<$Res>? get summary;
  $OKXWalletProfileTokensCopyWith<$Res> get tokens;
}

/// @nodoc
class _$OKXWalletProfileCopyWithImpl<$Res>
    implements $OKXWalletProfileCopyWith<$Res> {
  _$OKXWalletProfileCopyWithImpl(this._self, this._then);

  final OKXWalletProfile _self;
  final $Res Function(OKXWalletProfile) _then;

  /// Create a copy of OKXWalletProfile
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? summary = freezed, Object? tokens = null}) {
    return _then(
      _self.copyWith(
        summary: freezed == summary
            ? _self.summary
            : summary // ignore: cast_nullable_to_non_nullable
                  as OKXWalletProfileSummary?,
        tokens: null == tokens
            ? _self.tokens
            : tokens // ignore: cast_nullable_to_non_nullable
                  as OKXWalletProfileTokens,
      ),
    );
  }

  /// Create a copy of OKXWalletProfile
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $OKXWalletProfileSummaryCopyWith<$Res>? get summary {
    if (_self.summary == null) {
      return null;
    }

    return $OKXWalletProfileSummaryCopyWith<$Res>(_self.summary!, (value) {
      return _then(_self.copyWith(summary: value));
    });
  }

  /// Create a copy of OKXWalletProfile
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $OKXWalletProfileTokensCopyWith<$Res> get tokens {
    return $OKXWalletProfileTokensCopyWith<$Res>(_self.tokens, (value) {
      return _then(_self.copyWith(tokens: value));
    });
  }
}

/// @nodoc
@JsonSerializable()
class _OKXWalletProfile implements OKXWalletProfile {
  const _OKXWalletProfile({
    @JsonKey(name: 'walletAssetSummary') this.summary,
    @JsonKey(name: 'tokens') required this.tokens,
  });
  factory _OKXWalletProfile.fromJson(Map<String, dynamic> json) =>
      _$OKXWalletProfileFromJson(json);

  @override
  @JsonKey(name: 'walletAssetSummary')
  final OKXWalletProfileSummary? summary;
  @override
  @JsonKey(name: 'tokens')
  final OKXWalletProfileTokens tokens;

  /// Create a copy of OKXWalletProfile
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$OKXWalletProfileCopyWith<_OKXWalletProfile> get copyWith =>
      __$OKXWalletProfileCopyWithImpl<_OKXWalletProfile>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$OKXWalletProfileToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _OKXWalletProfile &&
            (identical(other.summary, summary) || other.summary == summary) &&
            (identical(other.tokens, tokens) || other.tokens == tokens));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, summary, tokens);

  @override
  String toString() {
    return 'OKXWalletProfile(summary: $summary, tokens: $tokens)';
  }
}

/// @nodoc
abstract mixin class _$OKXWalletProfileCopyWith<$Res>
    implements $OKXWalletProfileCopyWith<$Res> {
  factory _$OKXWalletProfileCopyWith(
    _OKXWalletProfile value,
    $Res Function(_OKXWalletProfile) _then,
  ) = __$OKXWalletProfileCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'walletAssetSummary') OKXWalletProfileSummary? summary,
    @JsonKey(name: 'tokens') OKXWalletProfileTokens tokens,
  });

  @override
  $OKXWalletProfileSummaryCopyWith<$Res>? get summary;
  @override
  $OKXWalletProfileTokensCopyWith<$Res> get tokens;
}

/// @nodoc
class __$OKXWalletProfileCopyWithImpl<$Res>
    implements _$OKXWalletProfileCopyWith<$Res> {
  __$OKXWalletProfileCopyWithImpl(this._self, this._then);

  final _OKXWalletProfile _self;
  final $Res Function(_OKXWalletProfile) _then;

  /// Create a copy of OKXWalletProfile
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({Object? summary = freezed, Object? tokens = null}) {
    return _then(
      _OKXWalletProfile(
        summary: freezed == summary
            ? _self.summary
            : summary // ignore: cast_nullable_to_non_nullable
                  as OKXWalletProfileSummary?,
        tokens: null == tokens
            ? _self.tokens
            : tokens // ignore: cast_nullable_to_non_nullable
                  as OKXWalletProfileTokens,
      ),
    );
  }

  /// Create a copy of OKXWalletProfile
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $OKXWalletProfileSummaryCopyWith<$Res>? get summary {
    if (_self.summary == null) {
      return null;
    }

    return $OKXWalletProfileSummaryCopyWith<$Res>(_self.summary!, (value) {
      return _then(_self.copyWith(summary: value));
    });
  }

  /// Create a copy of OKXWalletProfile
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $OKXWalletProfileTokensCopyWith<$Res> get tokens {
    return $OKXWalletProfileTokensCopyWith<$Res>(_self.tokens, (value) {
      return _then(_self.copyWith(tokens: value));
    });
  }
}

/// @nodoc
mixin _$OKXWalletProfileSummary {
  @NumberDecimalRequiredConverter()
  @JsonKey(name: 'tokenTotalCurrencyAmount')
  Decimal get tokenTotalCurrencyAmount;

  /// Create a copy of OKXWalletProfileSummary
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $OKXWalletProfileSummaryCopyWith<OKXWalletProfileSummary> get copyWith =>
      _$OKXWalletProfileSummaryCopyWithImpl<OKXWalletProfileSummary>(
        this as OKXWalletProfileSummary,
        _$identity,
      );

  /// Serializes this OKXWalletProfileSummary to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is OKXWalletProfileSummary &&
            (identical(
                  other.tokenTotalCurrencyAmount,
                  tokenTotalCurrencyAmount,
                ) ||
                other.tokenTotalCurrencyAmount == tokenTotalCurrencyAmount));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, tokenTotalCurrencyAmount);

  @override
  String toString() {
    return 'OKXWalletProfileSummary(tokenTotalCurrencyAmount: $tokenTotalCurrencyAmount)';
  }
}

/// @nodoc
abstract mixin class $OKXWalletProfileSummaryCopyWith<$Res> {
  factory $OKXWalletProfileSummaryCopyWith(
    OKXWalletProfileSummary value,
    $Res Function(OKXWalletProfileSummary) _then,
  ) = _$OKXWalletProfileSummaryCopyWithImpl;
  @useResult
  $Res call({
    @NumberDecimalRequiredConverter()
    @JsonKey(name: 'tokenTotalCurrencyAmount')
    Decimal tokenTotalCurrencyAmount,
  });
}

/// @nodoc
class _$OKXWalletProfileSummaryCopyWithImpl<$Res>
    implements $OKXWalletProfileSummaryCopyWith<$Res> {
  _$OKXWalletProfileSummaryCopyWithImpl(this._self, this._then);

  final OKXWalletProfileSummary _self;
  final $Res Function(OKXWalletProfileSummary) _then;

  /// Create a copy of OKXWalletProfileSummary
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? tokenTotalCurrencyAmount = null}) {
    return _then(
      _self.copyWith(
        tokenTotalCurrencyAmount: null == tokenTotalCurrencyAmount
            ? _self.tokenTotalCurrencyAmount
            : tokenTotalCurrencyAmount // ignore: cast_nullable_to_non_nullable
                  as Decimal,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _OKXWalletProfileSummary implements OKXWalletProfileSummary {
  const _OKXWalletProfileSummary({
    @NumberDecimalRequiredConverter()
    @JsonKey(name: 'tokenTotalCurrencyAmount')
    required this.tokenTotalCurrencyAmount,
  });
  factory _OKXWalletProfileSummary.fromJson(Map<String, dynamic> json) =>
      _$OKXWalletProfileSummaryFromJson(json);

  @override
  @NumberDecimalRequiredConverter()
  @JsonKey(name: 'tokenTotalCurrencyAmount')
  final Decimal tokenTotalCurrencyAmount;

  /// Create a copy of OKXWalletProfileSummary
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$OKXWalletProfileSummaryCopyWith<_OKXWalletProfileSummary> get copyWith =>
      __$OKXWalletProfileSummaryCopyWithImpl<_OKXWalletProfileSummary>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$OKXWalletProfileSummaryToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _OKXWalletProfileSummary &&
            (identical(
                  other.tokenTotalCurrencyAmount,
                  tokenTotalCurrencyAmount,
                ) ||
                other.tokenTotalCurrencyAmount == tokenTotalCurrencyAmount));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, tokenTotalCurrencyAmount);

  @override
  String toString() {
    return 'OKXWalletProfileSummary(tokenTotalCurrencyAmount: $tokenTotalCurrencyAmount)';
  }
}

/// @nodoc
abstract mixin class _$OKXWalletProfileSummaryCopyWith<$Res>
    implements $OKXWalletProfileSummaryCopyWith<$Res> {
  factory _$OKXWalletProfileSummaryCopyWith(
    _OKXWalletProfileSummary value,
    $Res Function(_OKXWalletProfileSummary) _then,
  ) = __$OKXWalletProfileSummaryCopyWithImpl;
  @override
  @useResult
  $Res call({
    @NumberDecimalRequiredConverter()
    @JsonKey(name: 'tokenTotalCurrencyAmount')
    Decimal tokenTotalCurrencyAmount,
  });
}

/// @nodoc
class __$OKXWalletProfileSummaryCopyWithImpl<$Res>
    implements _$OKXWalletProfileSummaryCopyWith<$Res> {
  __$OKXWalletProfileSummaryCopyWithImpl(this._self, this._then);

  final _OKXWalletProfileSummary _self;
  final $Res Function(_OKXWalletProfileSummary) _then;

  /// Create a copy of OKXWalletProfileSummary
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({Object? tokenTotalCurrencyAmount = null}) {
    return _then(
      _OKXWalletProfileSummary(
        tokenTotalCurrencyAmount: null == tokenTotalCurrencyAmount
            ? _self.tokenTotalCurrencyAmount
            : tokenTotalCurrencyAmount // ignore: cast_nullable_to_non_nullable
                  as Decimal,
      ),
    );
  }
}

/// @nodoc
mixin _$OKXWalletProfileTokens {
  @JsonKey(name: 'total')
  int get total;
  @JsonKey(name: 'pageSize')
  int get pageSize;
  @JsonKey(name: 'currentPage')
  int get currentPage;
  @JsonKey(name: 'isOverExplorerLimit')
  bool get isOverExplorerLimit;
  @JsonKey(name: 'tokenlist')
  List<OKXWalletProfileToken> get tokenlist;

  /// Create a copy of OKXWalletProfileTokens
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $OKXWalletProfileTokensCopyWith<OKXWalletProfileTokens> get copyWith =>
      _$OKXWalletProfileTokensCopyWithImpl<OKXWalletProfileTokens>(
        this as OKXWalletProfileTokens,
        _$identity,
      );

  /// Serializes this OKXWalletProfileTokens to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is OKXWalletProfileTokens &&
            (identical(other.total, total) || other.total == total) &&
            (identical(other.pageSize, pageSize) ||
                other.pageSize == pageSize) &&
            (identical(other.currentPage, currentPage) ||
                other.currentPage == currentPage) &&
            (identical(other.isOverExplorerLimit, isOverExplorerLimit) ||
                other.isOverExplorerLimit == isOverExplorerLimit) &&
            const DeepCollectionEquality().equals(other.tokenlist, tokenlist));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    total,
    pageSize,
    currentPage,
    isOverExplorerLimit,
    const DeepCollectionEquality().hash(tokenlist),
  );

  @override
  String toString() {
    return 'OKXWalletProfileTokens(total: $total, pageSize: $pageSize, currentPage: $currentPage, isOverExplorerLimit: $isOverExplorerLimit, tokenlist: $tokenlist)';
  }
}

/// @nodoc
abstract mixin class $OKXWalletProfileTokensCopyWith<$Res> {
  factory $OKXWalletProfileTokensCopyWith(
    OKXWalletProfileTokens value,
    $Res Function(OKXWalletProfileTokens) _then,
  ) = _$OKXWalletProfileTokensCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'total') int total,
    @JsonKey(name: 'pageSize') int pageSize,
    @JsonKey(name: 'currentPage') int currentPage,
    @JsonKey(name: 'isOverExplorerLimit') bool isOverExplorerLimit,
    @JsonKey(name: 'tokenlist') List<OKXWalletProfileToken> tokenlist,
  });
}

/// @nodoc
class _$OKXWalletProfileTokensCopyWithImpl<$Res>
    implements $OKXWalletProfileTokensCopyWith<$Res> {
  _$OKXWalletProfileTokensCopyWithImpl(this._self, this._then);

  final OKXWalletProfileTokens _self;
  final $Res Function(OKXWalletProfileTokens) _then;

  /// Create a copy of OKXWalletProfileTokens
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? total = null,
    Object? pageSize = null,
    Object? currentPage = null,
    Object? isOverExplorerLimit = null,
    Object? tokenlist = null,
  }) {
    return _then(
      _self.copyWith(
        total: null == total
            ? _self.total
            : total // ignore: cast_nullable_to_non_nullable
                  as int,
        pageSize: null == pageSize
            ? _self.pageSize
            : pageSize // ignore: cast_nullable_to_non_nullable
                  as int,
        currentPage: null == currentPage
            ? _self.currentPage
            : currentPage // ignore: cast_nullable_to_non_nullable
                  as int,
        isOverExplorerLimit: null == isOverExplorerLimit
            ? _self.isOverExplorerLimit
            : isOverExplorerLimit // ignore: cast_nullable_to_non_nullable
                  as bool,
        tokenlist: null == tokenlist
            ? _self.tokenlist
            : tokenlist // ignore: cast_nullable_to_non_nullable
                  as List<OKXWalletProfileToken>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _OKXWalletProfileTokens implements OKXWalletProfileTokens {
  const _OKXWalletProfileTokens({
    @JsonKey(name: 'total') required this.total,
    @JsonKey(name: 'pageSize') required this.pageSize,
    @JsonKey(name: 'currentPage') required this.currentPage,
    @JsonKey(name: 'isOverExplorerLimit') required this.isOverExplorerLimit,
    @JsonKey(name: 'tokenlist')
    required final List<OKXWalletProfileToken> tokenlist,
  }) : _tokenlist = tokenlist;
  factory _OKXWalletProfileTokens.fromJson(Map<String, dynamic> json) =>
      _$OKXWalletProfileTokensFromJson(json);

  @override
  @JsonKey(name: 'total')
  final int total;
  @override
  @JsonKey(name: 'pageSize')
  final int pageSize;
  @override
  @JsonKey(name: 'currentPage')
  final int currentPage;
  @override
  @JsonKey(name: 'isOverExplorerLimit')
  final bool isOverExplorerLimit;
  final List<OKXWalletProfileToken> _tokenlist;
  @override
  @JsonKey(name: 'tokenlist')
  List<OKXWalletProfileToken> get tokenlist {
    if (_tokenlist is EqualUnmodifiableListView) return _tokenlist;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_tokenlist);
  }

  /// Create a copy of OKXWalletProfileTokens
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$OKXWalletProfileTokensCopyWith<_OKXWalletProfileTokens> get copyWith =>
      __$OKXWalletProfileTokensCopyWithImpl<_OKXWalletProfileTokens>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$OKXWalletProfileTokensToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _OKXWalletProfileTokens &&
            (identical(other.total, total) || other.total == total) &&
            (identical(other.pageSize, pageSize) ||
                other.pageSize == pageSize) &&
            (identical(other.currentPage, currentPage) ||
                other.currentPage == currentPage) &&
            (identical(other.isOverExplorerLimit, isOverExplorerLimit) ||
                other.isOverExplorerLimit == isOverExplorerLimit) &&
            const DeepCollectionEquality().equals(
              other._tokenlist,
              _tokenlist,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    total,
    pageSize,
    currentPage,
    isOverExplorerLimit,
    const DeepCollectionEquality().hash(_tokenlist),
  );

  @override
  String toString() {
    return 'OKXWalletProfileTokens(total: $total, pageSize: $pageSize, currentPage: $currentPage, isOverExplorerLimit: $isOverExplorerLimit, tokenlist: $tokenlist)';
  }
}

/// @nodoc
abstract mixin class _$OKXWalletProfileTokensCopyWith<$Res>
    implements $OKXWalletProfileTokensCopyWith<$Res> {
  factory _$OKXWalletProfileTokensCopyWith(
    _OKXWalletProfileTokens value,
    $Res Function(_OKXWalletProfileTokens) _then,
  ) = __$OKXWalletProfileTokensCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'total') int total,
    @JsonKey(name: 'pageSize') int pageSize,
    @JsonKey(name: 'currentPage') int currentPage,
    @JsonKey(name: 'isOverExplorerLimit') bool isOverExplorerLimit,
    @JsonKey(name: 'tokenlist') List<OKXWalletProfileToken> tokenlist,
  });
}

/// @nodoc
class __$OKXWalletProfileTokensCopyWithImpl<$Res>
    implements _$OKXWalletProfileTokensCopyWith<$Res> {
  __$OKXWalletProfileTokensCopyWithImpl(this._self, this._then);

  final _OKXWalletProfileTokens _self;
  final $Res Function(_OKXWalletProfileTokens) _then;

  /// Create a copy of OKXWalletProfileTokens
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? total = null,
    Object? pageSize = null,
    Object? currentPage = null,
    Object? isOverExplorerLimit = null,
    Object? tokenlist = null,
  }) {
    return _then(
      _OKXWalletProfileTokens(
        total: null == total
            ? _self.total
            : total // ignore: cast_nullable_to_non_nullable
                  as int,
        pageSize: null == pageSize
            ? _self.pageSize
            : pageSize // ignore: cast_nullable_to_non_nullable
                  as int,
        currentPage: null == currentPage
            ? _self.currentPage
            : currentPage // ignore: cast_nullable_to_non_nullable
                  as int,
        isOverExplorerLimit: null == isOverExplorerLimit
            ? _self.isOverExplorerLimit
            : isOverExplorerLimit // ignore: cast_nullable_to_non_nullable
                  as bool,
        tokenlist: null == tokenlist
            ? _self._tokenlist
            : tokenlist // ignore: cast_nullable_to_non_nullable
                  as List<OKXWalletProfileToken>,
      ),
    );
  }
}

/// @nodoc
mixin _$OKXWalletProfileToken {
  @NumberDecimalRequiredConverter()
  @JsonKey(name: 'coinAmount')
  Decimal get realBalance;
  @NumberDecimalRequiredConverter()
  @JsonKey(name: 'coinAmountOrigin')
  Decimal get balance;
  @NumberDecimalRequiredConverter()
  @JsonKey(name: 'currencyAmount')
  Decimal get valueUsd;
  @JsonKey(name: 'symbol')
  String get symbol;
  @JsonKey(name: 'imageUrl')
  String get logo;
  @JsonKey(name: 'name')
  String get name;
  @JsonKey(name: 'coinBalanceDetails')
  List<OKXWalletProfileTokenDetail> get coinBalanceDetails;
  @JsonKey(name: 'coinPriceInfo')
  OKXWalletProfileTokenPrice get coinPriceInfo;

  /// Create a copy of OKXWalletProfileToken
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $OKXWalletProfileTokenCopyWith<OKXWalletProfileToken> get copyWith =>
      _$OKXWalletProfileTokenCopyWithImpl<OKXWalletProfileToken>(
        this as OKXWalletProfileToken,
        _$identity,
      );

  /// Serializes this OKXWalletProfileToken to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is OKXWalletProfileToken &&
            (identical(other.realBalance, realBalance) ||
                other.realBalance == realBalance) &&
            (identical(other.balance, balance) || other.balance == balance) &&
            (identical(other.valueUsd, valueUsd) ||
                other.valueUsd == valueUsd) &&
            (identical(other.symbol, symbol) || other.symbol == symbol) &&
            (identical(other.logo, logo) || other.logo == logo) &&
            (identical(other.name, name) || other.name == name) &&
            const DeepCollectionEquality().equals(
              other.coinBalanceDetails,
              coinBalanceDetails,
            ) &&
            (identical(other.coinPriceInfo, coinPriceInfo) ||
                other.coinPriceInfo == coinPriceInfo));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    realBalance,
    balance,
    valueUsd,
    symbol,
    logo,
    name,
    const DeepCollectionEquality().hash(coinBalanceDetails),
    coinPriceInfo,
  );

  @override
  String toString() {
    return 'OKXWalletProfileToken(realBalance: $realBalance, balance: $balance, valueUsd: $valueUsd, symbol: $symbol, logo: $logo, name: $name, coinBalanceDetails: $coinBalanceDetails, coinPriceInfo: $coinPriceInfo)';
  }
}

/// @nodoc
abstract mixin class $OKXWalletProfileTokenCopyWith<$Res> {
  factory $OKXWalletProfileTokenCopyWith(
    OKXWalletProfileToken value,
    $Res Function(OKXWalletProfileToken) _then,
  ) = _$OKXWalletProfileTokenCopyWithImpl;
  @useResult
  $Res call({
    @NumberDecimalRequiredConverter()
    @JsonKey(name: 'coinAmount')
    Decimal realBalance,
    @NumberDecimalRequiredConverter()
    @JsonKey(name: 'coinAmountOrigin')
    Decimal balance,
    @NumberDecimalRequiredConverter()
    @JsonKey(name: 'currencyAmount')
    Decimal valueUsd,
    @JsonKey(name: 'symbol') String symbol,
    @JsonKey(name: 'imageUrl') String logo,
    @JsonKey(name: 'name') String name,
    @JsonKey(name: 'coinBalanceDetails')
    List<OKXWalletProfileTokenDetail> coinBalanceDetails,
    @JsonKey(name: 'coinPriceInfo') OKXWalletProfileTokenPrice coinPriceInfo,
  });

  $OKXWalletProfileTokenPriceCopyWith<$Res> get coinPriceInfo;
}

/// @nodoc
class _$OKXWalletProfileTokenCopyWithImpl<$Res>
    implements $OKXWalletProfileTokenCopyWith<$Res> {
  _$OKXWalletProfileTokenCopyWithImpl(this._self, this._then);

  final OKXWalletProfileToken _self;
  final $Res Function(OKXWalletProfileToken) _then;

  /// Create a copy of OKXWalletProfileToken
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? realBalance = null,
    Object? balance = null,
    Object? valueUsd = null,
    Object? symbol = null,
    Object? logo = null,
    Object? name = null,
    Object? coinBalanceDetails = null,
    Object? coinPriceInfo = null,
  }) {
    return _then(
      _self.copyWith(
        realBalance: null == realBalance
            ? _self.realBalance
            : realBalance // ignore: cast_nullable_to_non_nullable
                  as Decimal,
        balance: null == balance
            ? _self.balance
            : balance // ignore: cast_nullable_to_non_nullable
                  as Decimal,
        valueUsd: null == valueUsd
            ? _self.valueUsd
            : valueUsd // ignore: cast_nullable_to_non_nullable
                  as Decimal,
        symbol: null == symbol
            ? _self.symbol
            : symbol // ignore: cast_nullable_to_non_nullable
                  as String,
        logo: null == logo
            ? _self.logo
            : logo // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _self.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        coinBalanceDetails: null == coinBalanceDetails
            ? _self.coinBalanceDetails
            : coinBalanceDetails // ignore: cast_nullable_to_non_nullable
                  as List<OKXWalletProfileTokenDetail>,
        coinPriceInfo: null == coinPriceInfo
            ? _self.coinPriceInfo
            : coinPriceInfo // ignore: cast_nullable_to_non_nullable
                  as OKXWalletProfileTokenPrice,
      ),
    );
  }

  /// Create a copy of OKXWalletProfileToken
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $OKXWalletProfileTokenPriceCopyWith<$Res> get coinPriceInfo {
    return $OKXWalletProfileTokenPriceCopyWith<$Res>(_self.coinPriceInfo, (
      value,
    ) {
      return _then(_self.copyWith(coinPriceInfo: value));
    });
  }
}

/// @nodoc
@JsonSerializable()
class _OKXWalletProfileToken extends OKXWalletProfileToken {
  const _OKXWalletProfileToken({
    @NumberDecimalRequiredConverter()
    @JsonKey(name: 'coinAmount')
    required this.realBalance,
    @NumberDecimalRequiredConverter()
    @JsonKey(name: 'coinAmountOrigin')
    required this.balance,
    @NumberDecimalRequiredConverter()
    @JsonKey(name: 'currencyAmount')
    required this.valueUsd,
    @JsonKey(name: 'symbol') required this.symbol,
    @JsonKey(name: 'imageUrl') required this.logo,
    @JsonKey(name: 'name') required this.name,
    @JsonKey(name: 'coinBalanceDetails')
    required final List<OKXWalletProfileTokenDetail> coinBalanceDetails,
    @JsonKey(name: 'coinPriceInfo') required this.coinPriceInfo,
  }) : _coinBalanceDetails = coinBalanceDetails,
       super._();
  factory _OKXWalletProfileToken.fromJson(Map<String, dynamic> json) =>
      _$OKXWalletProfileTokenFromJson(json);

  @override
  @NumberDecimalRequiredConverter()
  @JsonKey(name: 'coinAmount')
  final Decimal realBalance;
  @override
  @NumberDecimalRequiredConverter()
  @JsonKey(name: 'coinAmountOrigin')
  final Decimal balance;
  @override
  @NumberDecimalRequiredConverter()
  @JsonKey(name: 'currencyAmount')
  final Decimal valueUsd;
  @override
  @JsonKey(name: 'symbol')
  final String symbol;
  @override
  @JsonKey(name: 'imageUrl')
  final String logo;
  @override
  @JsonKey(name: 'name')
  final String name;
  final List<OKXWalletProfileTokenDetail> _coinBalanceDetails;
  @override
  @JsonKey(name: 'coinBalanceDetails')
  List<OKXWalletProfileTokenDetail> get coinBalanceDetails {
    if (_coinBalanceDetails is EqualUnmodifiableListView)
      return _coinBalanceDetails;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_coinBalanceDetails);
  }

  @override
  @JsonKey(name: 'coinPriceInfo')
  final OKXWalletProfileTokenPrice coinPriceInfo;

  /// Create a copy of OKXWalletProfileToken
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$OKXWalletProfileTokenCopyWith<_OKXWalletProfileToken> get copyWith =>
      __$OKXWalletProfileTokenCopyWithImpl<_OKXWalletProfileToken>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$OKXWalletProfileTokenToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _OKXWalletProfileToken &&
            (identical(other.realBalance, realBalance) ||
                other.realBalance == realBalance) &&
            (identical(other.balance, balance) || other.balance == balance) &&
            (identical(other.valueUsd, valueUsd) ||
                other.valueUsd == valueUsd) &&
            (identical(other.symbol, symbol) || other.symbol == symbol) &&
            (identical(other.logo, logo) || other.logo == logo) &&
            (identical(other.name, name) || other.name == name) &&
            const DeepCollectionEquality().equals(
              other._coinBalanceDetails,
              _coinBalanceDetails,
            ) &&
            (identical(other.coinPriceInfo, coinPriceInfo) ||
                other.coinPriceInfo == coinPriceInfo));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    realBalance,
    balance,
    valueUsd,
    symbol,
    logo,
    name,
    const DeepCollectionEquality().hash(_coinBalanceDetails),
    coinPriceInfo,
  );

  @override
  String toString() {
    return 'OKXWalletProfileToken(realBalance: $realBalance, balance: $balance, valueUsd: $valueUsd, symbol: $symbol, logo: $logo, name: $name, coinBalanceDetails: $coinBalanceDetails, coinPriceInfo: $coinPriceInfo)';
  }
}

/// @nodoc
abstract mixin class _$OKXWalletProfileTokenCopyWith<$Res>
    implements $OKXWalletProfileTokenCopyWith<$Res> {
  factory _$OKXWalletProfileTokenCopyWith(
    _OKXWalletProfileToken value,
    $Res Function(_OKXWalletProfileToken) _then,
  ) = __$OKXWalletProfileTokenCopyWithImpl;
  @override
  @useResult
  $Res call({
    @NumberDecimalRequiredConverter()
    @JsonKey(name: 'coinAmount')
    Decimal realBalance,
    @NumberDecimalRequiredConverter()
    @JsonKey(name: 'coinAmountOrigin')
    Decimal balance,
    @NumberDecimalRequiredConverter()
    @JsonKey(name: 'currencyAmount')
    Decimal valueUsd,
    @JsonKey(name: 'symbol') String symbol,
    @JsonKey(name: 'imageUrl') String logo,
    @JsonKey(name: 'name') String name,
    @JsonKey(name: 'coinBalanceDetails')
    List<OKXWalletProfileTokenDetail> coinBalanceDetails,
    @JsonKey(name: 'coinPriceInfo') OKXWalletProfileTokenPrice coinPriceInfo,
  });

  @override
  $OKXWalletProfileTokenPriceCopyWith<$Res> get coinPriceInfo;
}

/// @nodoc
class __$OKXWalletProfileTokenCopyWithImpl<$Res>
    implements _$OKXWalletProfileTokenCopyWith<$Res> {
  __$OKXWalletProfileTokenCopyWithImpl(this._self, this._then);

  final _OKXWalletProfileToken _self;
  final $Res Function(_OKXWalletProfileToken) _then;

  /// Create a copy of OKXWalletProfileToken
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? realBalance = null,
    Object? balance = null,
    Object? valueUsd = null,
    Object? symbol = null,
    Object? logo = null,
    Object? name = null,
    Object? coinBalanceDetails = null,
    Object? coinPriceInfo = null,
  }) {
    return _then(
      _OKXWalletProfileToken(
        realBalance: null == realBalance
            ? _self.realBalance
            : realBalance // ignore: cast_nullable_to_non_nullable
                  as Decimal,
        balance: null == balance
            ? _self.balance
            : balance // ignore: cast_nullable_to_non_nullable
                  as Decimal,
        valueUsd: null == valueUsd
            ? _self.valueUsd
            : valueUsd // ignore: cast_nullable_to_non_nullable
                  as Decimal,
        symbol: null == symbol
            ? _self.symbol
            : symbol // ignore: cast_nullable_to_non_nullable
                  as String,
        logo: null == logo
            ? _self.logo
            : logo // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _self.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        coinBalanceDetails: null == coinBalanceDetails
            ? _self._coinBalanceDetails
            : coinBalanceDetails // ignore: cast_nullable_to_non_nullable
                  as List<OKXWalletProfileTokenDetail>,
        coinPriceInfo: null == coinPriceInfo
            ? _self.coinPriceInfo
            : coinPriceInfo // ignore: cast_nullable_to_non_nullable
                  as OKXWalletProfileTokenPrice,
      ),
    );
  }

  /// Create a copy of OKXWalletProfileToken
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $OKXWalletProfileTokenPriceCopyWith<$Res> get coinPriceInfo {
    return $OKXWalletProfileTokenPriceCopyWith<$Res>(_self.coinPriceInfo, (
      value,
    ) {
      return _then(_self.copyWith(coinPriceInfo: value));
    });
  }
}

/// @nodoc
mixin _$OKXWalletProfileTokenDetail {
  @JsonKey(name: 'name')
  String get name;
  @JsonKey(name: 'symbol')
  String get symbol;
  @JsonKey(name: 'address')
  String? get address;
  @JsonKey(name: 'decimalNum')
  int get decimals;
  @JsonKey(name: 'imageUrl')
  String get logo;
  @JsonKey(name: 'chainName')
  String get chainName;
  @JsonKey(name: 'chainId')
  int get chainId;
  @JsonKey(name: 'chainIndex')
  int get chainIndex;
  @JsonKey(name: 'systemToken')
  bool get systemToken;
  @JsonKey(name: 'chainImageUrl')
  String get chainImageUrl;
  @JsonKey(name: 'coinPriceVo')
  OKXWalletProfileTokenPrice get coinPriceVo;
  @JsonKey(name: 'isRiskType')
  bool get isRiskType;
  @JsonKey(name: 'userAddress')
  String get userAddress;
  @NumberDecimalRequiredConverter()
  @JsonKey(name: 'coinAmount')
  Decimal get realBalance;
  @NumberDecimalRequiredConverter()
  @JsonKey(name: 'coinAmountOrigin')
  Decimal get balance;
  @NumberDecimalRequiredConverter()
  @JsonKey(name: 'currencyAmount')
  Decimal get valueUsd;

  /// Create a copy of OKXWalletProfileTokenDetail
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $OKXWalletProfileTokenDetailCopyWith<OKXWalletProfileTokenDetail>
  get copyWith =>
      _$OKXWalletProfileTokenDetailCopyWithImpl<OKXWalletProfileTokenDetail>(
        this as OKXWalletProfileTokenDetail,
        _$identity,
      );

  /// Serializes this OKXWalletProfileTokenDetail to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is OKXWalletProfileTokenDetail &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.symbol, symbol) || other.symbol == symbol) &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.decimals, decimals) ||
                other.decimals == decimals) &&
            (identical(other.logo, logo) || other.logo == logo) &&
            (identical(other.chainName, chainName) ||
                other.chainName == chainName) &&
            (identical(other.chainId, chainId) || other.chainId == chainId) &&
            (identical(other.chainIndex, chainIndex) ||
                other.chainIndex == chainIndex) &&
            (identical(other.systemToken, systemToken) ||
                other.systemToken == systemToken) &&
            (identical(other.chainImageUrl, chainImageUrl) ||
                other.chainImageUrl == chainImageUrl) &&
            (identical(other.coinPriceVo, coinPriceVo) ||
                other.coinPriceVo == coinPriceVo) &&
            (identical(other.isRiskType, isRiskType) ||
                other.isRiskType == isRiskType) &&
            (identical(other.userAddress, userAddress) ||
                other.userAddress == userAddress) &&
            (identical(other.realBalance, realBalance) ||
                other.realBalance == realBalance) &&
            (identical(other.balance, balance) || other.balance == balance) &&
            (identical(other.valueUsd, valueUsd) ||
                other.valueUsd == valueUsd));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    name,
    symbol,
    address,
    decimals,
    logo,
    chainName,
    chainId,
    chainIndex,
    systemToken,
    chainImageUrl,
    coinPriceVo,
    isRiskType,
    userAddress,
    realBalance,
    balance,
    valueUsd,
  );

  @override
  String toString() {
    return 'OKXWalletProfileTokenDetail(name: $name, symbol: $symbol, address: $address, decimals: $decimals, logo: $logo, chainName: $chainName, chainId: $chainId, chainIndex: $chainIndex, systemToken: $systemToken, chainImageUrl: $chainImageUrl, coinPriceVo: $coinPriceVo, isRiskType: $isRiskType, userAddress: $userAddress, realBalance: $realBalance, balance: $balance, valueUsd: $valueUsd)';
  }
}

/// @nodoc
abstract mixin class $OKXWalletProfileTokenDetailCopyWith<$Res> {
  factory $OKXWalletProfileTokenDetailCopyWith(
    OKXWalletProfileTokenDetail value,
    $Res Function(OKXWalletProfileTokenDetail) _then,
  ) = _$OKXWalletProfileTokenDetailCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'name') String name,
    @JsonKey(name: 'symbol') String symbol,
    @JsonKey(name: 'address') String? address,
    @JsonKey(name: 'decimalNum') int decimals,
    @JsonKey(name: 'imageUrl') String logo,
    @JsonKey(name: 'chainName') String chainName,
    @JsonKey(name: 'chainId') int chainId,
    @JsonKey(name: 'chainIndex') int chainIndex,
    @JsonKey(name: 'systemToken') bool systemToken,
    @JsonKey(name: 'chainImageUrl') String chainImageUrl,
    @JsonKey(name: 'coinPriceVo') OKXWalletProfileTokenPrice coinPriceVo,
    @JsonKey(name: 'isRiskType') bool isRiskType,
    @JsonKey(name: 'userAddress') String userAddress,
    @NumberDecimalRequiredConverter()
    @JsonKey(name: 'coinAmount')
    Decimal realBalance,
    @NumberDecimalRequiredConverter()
    @JsonKey(name: 'coinAmountOrigin')
    Decimal balance,
    @NumberDecimalRequiredConverter()
    @JsonKey(name: 'currencyAmount')
    Decimal valueUsd,
  });

  $OKXWalletProfileTokenPriceCopyWith<$Res> get coinPriceVo;
}

/// @nodoc
class _$OKXWalletProfileTokenDetailCopyWithImpl<$Res>
    implements $OKXWalletProfileTokenDetailCopyWith<$Res> {
  _$OKXWalletProfileTokenDetailCopyWithImpl(this._self, this._then);

  final OKXWalletProfileTokenDetail _self;
  final $Res Function(OKXWalletProfileTokenDetail) _then;

  /// Create a copy of OKXWalletProfileTokenDetail
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? symbol = null,
    Object? address = freezed,
    Object? decimals = null,
    Object? logo = null,
    Object? chainName = null,
    Object? chainId = null,
    Object? chainIndex = null,
    Object? systemToken = null,
    Object? chainImageUrl = null,
    Object? coinPriceVo = null,
    Object? isRiskType = null,
    Object? userAddress = null,
    Object? realBalance = null,
    Object? balance = null,
    Object? valueUsd = null,
  }) {
    return _then(
      _self.copyWith(
        name: null == name
            ? _self.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        symbol: null == symbol
            ? _self.symbol
            : symbol // ignore: cast_nullable_to_non_nullable
                  as String,
        address: freezed == address
            ? _self.address
            : address // ignore: cast_nullable_to_non_nullable
                  as String?,
        decimals: null == decimals
            ? _self.decimals
            : decimals // ignore: cast_nullable_to_non_nullable
                  as int,
        logo: null == logo
            ? _self.logo
            : logo // ignore: cast_nullable_to_non_nullable
                  as String,
        chainName: null == chainName
            ? _self.chainName
            : chainName // ignore: cast_nullable_to_non_nullable
                  as String,
        chainId: null == chainId
            ? _self.chainId
            : chainId // ignore: cast_nullable_to_non_nullable
                  as int,
        chainIndex: null == chainIndex
            ? _self.chainIndex
            : chainIndex // ignore: cast_nullable_to_non_nullable
                  as int,
        systemToken: null == systemToken
            ? _self.systemToken
            : systemToken // ignore: cast_nullable_to_non_nullable
                  as bool,
        chainImageUrl: null == chainImageUrl
            ? _self.chainImageUrl
            : chainImageUrl // ignore: cast_nullable_to_non_nullable
                  as String,
        coinPriceVo: null == coinPriceVo
            ? _self.coinPriceVo
            : coinPriceVo // ignore: cast_nullable_to_non_nullable
                  as OKXWalletProfileTokenPrice,
        isRiskType: null == isRiskType
            ? _self.isRiskType
            : isRiskType // ignore: cast_nullable_to_non_nullable
                  as bool,
        userAddress: null == userAddress
            ? _self.userAddress
            : userAddress // ignore: cast_nullable_to_non_nullable
                  as String,
        realBalance: null == realBalance
            ? _self.realBalance
            : realBalance // ignore: cast_nullable_to_non_nullable
                  as Decimal,
        balance: null == balance
            ? _self.balance
            : balance // ignore: cast_nullable_to_non_nullable
                  as Decimal,
        valueUsd: null == valueUsd
            ? _self.valueUsd
            : valueUsd // ignore: cast_nullable_to_non_nullable
                  as Decimal,
      ),
    );
  }

  /// Create a copy of OKXWalletProfileTokenDetail
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $OKXWalletProfileTokenPriceCopyWith<$Res> get coinPriceVo {
    return $OKXWalletProfileTokenPriceCopyWith<$Res>(_self.coinPriceVo, (
      value,
    ) {
      return _then(_self.copyWith(coinPriceVo: value));
    });
  }
}

/// @nodoc
@JsonSerializable()
class _OKXWalletProfileTokenDetail implements OKXWalletProfileTokenDetail {
  const _OKXWalletProfileTokenDetail({
    @JsonKey(name: 'name') required this.name,
    @JsonKey(name: 'symbol') required this.symbol,
    @JsonKey(name: 'address') this.address,
    @JsonKey(name: 'decimalNum') required this.decimals,
    @JsonKey(name: 'imageUrl') required this.logo,
    @JsonKey(name: 'chainName') required this.chainName,
    @JsonKey(name: 'chainId') required this.chainId,
    @JsonKey(name: 'chainIndex') required this.chainIndex,
    @JsonKey(name: 'systemToken') required this.systemToken,
    @JsonKey(name: 'chainImageUrl') required this.chainImageUrl,
    @JsonKey(name: 'coinPriceVo') required this.coinPriceVo,
    @JsonKey(name: 'isRiskType') required this.isRiskType,
    @JsonKey(name: 'userAddress') required this.userAddress,
    @NumberDecimalRequiredConverter()
    @JsonKey(name: 'coinAmount')
    required this.realBalance,
    @NumberDecimalRequiredConverter()
    @JsonKey(name: 'coinAmountOrigin')
    required this.balance,
    @NumberDecimalRequiredConverter()
    @JsonKey(name: 'currencyAmount')
    required this.valueUsd,
  });
  factory _OKXWalletProfileTokenDetail.fromJson(Map<String, dynamic> json) =>
      _$OKXWalletProfileTokenDetailFromJson(json);

  @override
  @JsonKey(name: 'name')
  final String name;
  @override
  @JsonKey(name: 'symbol')
  final String symbol;
  @override
  @JsonKey(name: 'address')
  final String? address;
  @override
  @JsonKey(name: 'decimalNum')
  final int decimals;
  @override
  @JsonKey(name: 'imageUrl')
  final String logo;
  @override
  @JsonKey(name: 'chainName')
  final String chainName;
  @override
  @JsonKey(name: 'chainId')
  final int chainId;
  @override
  @JsonKey(name: 'chainIndex')
  final int chainIndex;
  @override
  @JsonKey(name: 'systemToken')
  final bool systemToken;
  @override
  @JsonKey(name: 'chainImageUrl')
  final String chainImageUrl;
  @override
  @JsonKey(name: 'coinPriceVo')
  final OKXWalletProfileTokenPrice coinPriceVo;
  @override
  @JsonKey(name: 'isRiskType')
  final bool isRiskType;
  @override
  @JsonKey(name: 'userAddress')
  final String userAddress;
  @override
  @NumberDecimalRequiredConverter()
  @JsonKey(name: 'coinAmount')
  final Decimal realBalance;
  @override
  @NumberDecimalRequiredConverter()
  @JsonKey(name: 'coinAmountOrigin')
  final Decimal balance;
  @override
  @NumberDecimalRequiredConverter()
  @JsonKey(name: 'currencyAmount')
  final Decimal valueUsd;

  /// Create a copy of OKXWalletProfileTokenDetail
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$OKXWalletProfileTokenDetailCopyWith<_OKXWalletProfileTokenDetail>
  get copyWith =>
      __$OKXWalletProfileTokenDetailCopyWithImpl<_OKXWalletProfileTokenDetail>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$OKXWalletProfileTokenDetailToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _OKXWalletProfileTokenDetail &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.symbol, symbol) || other.symbol == symbol) &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.decimals, decimals) ||
                other.decimals == decimals) &&
            (identical(other.logo, logo) || other.logo == logo) &&
            (identical(other.chainName, chainName) ||
                other.chainName == chainName) &&
            (identical(other.chainId, chainId) || other.chainId == chainId) &&
            (identical(other.chainIndex, chainIndex) ||
                other.chainIndex == chainIndex) &&
            (identical(other.systemToken, systemToken) ||
                other.systemToken == systemToken) &&
            (identical(other.chainImageUrl, chainImageUrl) ||
                other.chainImageUrl == chainImageUrl) &&
            (identical(other.coinPriceVo, coinPriceVo) ||
                other.coinPriceVo == coinPriceVo) &&
            (identical(other.isRiskType, isRiskType) ||
                other.isRiskType == isRiskType) &&
            (identical(other.userAddress, userAddress) ||
                other.userAddress == userAddress) &&
            (identical(other.realBalance, realBalance) ||
                other.realBalance == realBalance) &&
            (identical(other.balance, balance) || other.balance == balance) &&
            (identical(other.valueUsd, valueUsd) ||
                other.valueUsd == valueUsd));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    name,
    symbol,
    address,
    decimals,
    logo,
    chainName,
    chainId,
    chainIndex,
    systemToken,
    chainImageUrl,
    coinPriceVo,
    isRiskType,
    userAddress,
    realBalance,
    balance,
    valueUsd,
  );

  @override
  String toString() {
    return 'OKXWalletProfileTokenDetail(name: $name, symbol: $symbol, address: $address, decimals: $decimals, logo: $logo, chainName: $chainName, chainId: $chainId, chainIndex: $chainIndex, systemToken: $systemToken, chainImageUrl: $chainImageUrl, coinPriceVo: $coinPriceVo, isRiskType: $isRiskType, userAddress: $userAddress, realBalance: $realBalance, balance: $balance, valueUsd: $valueUsd)';
  }
}

/// @nodoc
abstract mixin class _$OKXWalletProfileTokenDetailCopyWith<$Res>
    implements $OKXWalletProfileTokenDetailCopyWith<$Res> {
  factory _$OKXWalletProfileTokenDetailCopyWith(
    _OKXWalletProfileTokenDetail value,
    $Res Function(_OKXWalletProfileTokenDetail) _then,
  ) = __$OKXWalletProfileTokenDetailCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'name') String name,
    @JsonKey(name: 'symbol') String symbol,
    @JsonKey(name: 'address') String? address,
    @JsonKey(name: 'decimalNum') int decimals,
    @JsonKey(name: 'imageUrl') String logo,
    @JsonKey(name: 'chainName') String chainName,
    @JsonKey(name: 'chainId') int chainId,
    @JsonKey(name: 'chainIndex') int chainIndex,
    @JsonKey(name: 'systemToken') bool systemToken,
    @JsonKey(name: 'chainImageUrl') String chainImageUrl,
    @JsonKey(name: 'coinPriceVo') OKXWalletProfileTokenPrice coinPriceVo,
    @JsonKey(name: 'isRiskType') bool isRiskType,
    @JsonKey(name: 'userAddress') String userAddress,
    @NumberDecimalRequiredConverter()
    @JsonKey(name: 'coinAmount')
    Decimal realBalance,
    @NumberDecimalRequiredConverter()
    @JsonKey(name: 'coinAmountOrigin')
    Decimal balance,
    @NumberDecimalRequiredConverter()
    @JsonKey(name: 'currencyAmount')
    Decimal valueUsd,
  });

  @override
  $OKXWalletProfileTokenPriceCopyWith<$Res> get coinPriceVo;
}

/// @nodoc
class __$OKXWalletProfileTokenDetailCopyWithImpl<$Res>
    implements _$OKXWalletProfileTokenDetailCopyWith<$Res> {
  __$OKXWalletProfileTokenDetailCopyWithImpl(this._self, this._then);

  final _OKXWalletProfileTokenDetail _self;
  final $Res Function(_OKXWalletProfileTokenDetail) _then;

  /// Create a copy of OKXWalletProfileTokenDetail
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? name = null,
    Object? symbol = null,
    Object? address = freezed,
    Object? decimals = null,
    Object? logo = null,
    Object? chainName = null,
    Object? chainId = null,
    Object? chainIndex = null,
    Object? systemToken = null,
    Object? chainImageUrl = null,
    Object? coinPriceVo = null,
    Object? isRiskType = null,
    Object? userAddress = null,
    Object? realBalance = null,
    Object? balance = null,
    Object? valueUsd = null,
  }) {
    return _then(
      _OKXWalletProfileTokenDetail(
        name: null == name
            ? _self.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        symbol: null == symbol
            ? _self.symbol
            : symbol // ignore: cast_nullable_to_non_nullable
                  as String,
        address: freezed == address
            ? _self.address
            : address // ignore: cast_nullable_to_non_nullable
                  as String?,
        decimals: null == decimals
            ? _self.decimals
            : decimals // ignore: cast_nullable_to_non_nullable
                  as int,
        logo: null == logo
            ? _self.logo
            : logo // ignore: cast_nullable_to_non_nullable
                  as String,
        chainName: null == chainName
            ? _self.chainName
            : chainName // ignore: cast_nullable_to_non_nullable
                  as String,
        chainId: null == chainId
            ? _self.chainId
            : chainId // ignore: cast_nullable_to_non_nullable
                  as int,
        chainIndex: null == chainIndex
            ? _self.chainIndex
            : chainIndex // ignore: cast_nullable_to_non_nullable
                  as int,
        systemToken: null == systemToken
            ? _self.systemToken
            : systemToken // ignore: cast_nullable_to_non_nullable
                  as bool,
        chainImageUrl: null == chainImageUrl
            ? _self.chainImageUrl
            : chainImageUrl // ignore: cast_nullable_to_non_nullable
                  as String,
        coinPriceVo: null == coinPriceVo
            ? _self.coinPriceVo
            : coinPriceVo // ignore: cast_nullable_to_non_nullable
                  as OKXWalletProfileTokenPrice,
        isRiskType: null == isRiskType
            ? _self.isRiskType
            : isRiskType // ignore: cast_nullable_to_non_nullable
                  as bool,
        userAddress: null == userAddress
            ? _self.userAddress
            : userAddress // ignore: cast_nullable_to_non_nullable
                  as String,
        realBalance: null == realBalance
            ? _self.realBalance
            : realBalance // ignore: cast_nullable_to_non_nullable
                  as Decimal,
        balance: null == balance
            ? _self.balance
            : balance // ignore: cast_nullable_to_non_nullable
                  as Decimal,
        valueUsd: null == valueUsd
            ? _self.valueUsd
            : valueUsd // ignore: cast_nullable_to_non_nullable
                  as Decimal,
      ),
    );
  }

  /// Create a copy of OKXWalletProfileTokenDetail
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $OKXWalletProfileTokenPriceCopyWith<$Res> get coinPriceVo {
    return $OKXWalletProfileTokenPriceCopyWith<$Res>(_self.coinPriceVo, (
      value,
    ) {
      return _then(_self.copyWith(coinPriceVo: value));
    });
  }
}

/// @nodoc
mixin _$OKXWalletProfileTokenPrice {
  @JsonKey(name: 'address')
  String? get address;
  @JsonKey(name: 'hasPrice')
  bool get hasPrice;
  @NumberDecimalRequiredConverter()
  @JsonKey(name: 'price')
  Decimal get price;
  @JsonKey(name: 'hasPercent')
  bool get hasPercent;
  @NumberDecimalRequiredConverter()
  @JsonKey(name: 'priceChangePercent24h')
  Decimal get priceChangePercent24h;
  @JsonKey(name: 'chainId')
  int get chainId;

  /// Create a copy of OKXWalletProfileTokenPrice
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $OKXWalletProfileTokenPriceCopyWith<OKXWalletProfileTokenPrice>
  get copyWith =>
      _$OKXWalletProfileTokenPriceCopyWithImpl<OKXWalletProfileTokenPrice>(
        this as OKXWalletProfileTokenPrice,
        _$identity,
      );

  /// Serializes this OKXWalletProfileTokenPrice to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is OKXWalletProfileTokenPrice &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.hasPrice, hasPrice) ||
                other.hasPrice == hasPrice) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.hasPercent, hasPercent) ||
                other.hasPercent == hasPercent) &&
            (identical(other.priceChangePercent24h, priceChangePercent24h) ||
                other.priceChangePercent24h == priceChangePercent24h) &&
            (identical(other.chainId, chainId) || other.chainId == chainId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    address,
    hasPrice,
    price,
    hasPercent,
    priceChangePercent24h,
    chainId,
  );

  @override
  String toString() {
    return 'OKXWalletProfileTokenPrice(address: $address, hasPrice: $hasPrice, price: $price, hasPercent: $hasPercent, priceChangePercent24h: $priceChangePercent24h, chainId: $chainId)';
  }
}

/// @nodoc
abstract mixin class $OKXWalletProfileTokenPriceCopyWith<$Res> {
  factory $OKXWalletProfileTokenPriceCopyWith(
    OKXWalletProfileTokenPrice value,
    $Res Function(OKXWalletProfileTokenPrice) _then,
  ) = _$OKXWalletProfileTokenPriceCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'address') String? address,
    @JsonKey(name: 'hasPrice') bool hasPrice,
    @NumberDecimalRequiredConverter() @JsonKey(name: 'price') Decimal price,
    @JsonKey(name: 'hasPercent') bool hasPercent,
    @NumberDecimalRequiredConverter()
    @JsonKey(name: 'priceChangePercent24h')
    Decimal priceChangePercent24h,
    @JsonKey(name: 'chainId') int chainId,
  });
}

/// @nodoc
class _$OKXWalletProfileTokenPriceCopyWithImpl<$Res>
    implements $OKXWalletProfileTokenPriceCopyWith<$Res> {
  _$OKXWalletProfileTokenPriceCopyWithImpl(this._self, this._then);

  final OKXWalletProfileTokenPrice _self;
  final $Res Function(OKXWalletProfileTokenPrice) _then;

  /// Create a copy of OKXWalletProfileTokenPrice
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? address = freezed,
    Object? hasPrice = null,
    Object? price = null,
    Object? hasPercent = null,
    Object? priceChangePercent24h = null,
    Object? chainId = null,
  }) {
    return _then(
      _self.copyWith(
        address: freezed == address
            ? _self.address
            : address // ignore: cast_nullable_to_non_nullable
                  as String?,
        hasPrice: null == hasPrice
            ? _self.hasPrice
            : hasPrice // ignore: cast_nullable_to_non_nullable
                  as bool,
        price: null == price
            ? _self.price
            : price // ignore: cast_nullable_to_non_nullable
                  as Decimal,
        hasPercent: null == hasPercent
            ? _self.hasPercent
            : hasPercent // ignore: cast_nullable_to_non_nullable
                  as bool,
        priceChangePercent24h: null == priceChangePercent24h
            ? _self.priceChangePercent24h
            : priceChangePercent24h // ignore: cast_nullable_to_non_nullable
                  as Decimal,
        chainId: null == chainId
            ? _self.chainId
            : chainId // ignore: cast_nullable_to_non_nullable
                  as int,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _OKXWalletProfileTokenPrice implements OKXWalletProfileTokenPrice {
  const _OKXWalletProfileTokenPrice({
    @JsonKey(name: 'address') this.address,
    @JsonKey(name: 'hasPrice') required this.hasPrice,
    @NumberDecimalRequiredConverter()
    @JsonKey(name: 'price')
    required this.price,
    @JsonKey(name: 'hasPercent') required this.hasPercent,
    @NumberDecimalRequiredConverter()
    @JsonKey(name: 'priceChangePercent24h')
    required this.priceChangePercent24h,
    @JsonKey(name: 'chainId') required this.chainId,
  });
  factory _OKXWalletProfileTokenPrice.fromJson(Map<String, dynamic> json) =>
      _$OKXWalletProfileTokenPriceFromJson(json);

  @override
  @JsonKey(name: 'address')
  final String? address;
  @override
  @JsonKey(name: 'hasPrice')
  final bool hasPrice;
  @override
  @NumberDecimalRequiredConverter()
  @JsonKey(name: 'price')
  final Decimal price;
  @override
  @JsonKey(name: 'hasPercent')
  final bool hasPercent;
  @override
  @NumberDecimalRequiredConverter()
  @JsonKey(name: 'priceChangePercent24h')
  final Decimal priceChangePercent24h;
  @override
  @JsonKey(name: 'chainId')
  final int chainId;

  /// Create a copy of OKXWalletProfileTokenPrice
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$OKXWalletProfileTokenPriceCopyWith<_OKXWalletProfileTokenPrice>
  get copyWith =>
      __$OKXWalletProfileTokenPriceCopyWithImpl<_OKXWalletProfileTokenPrice>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$OKXWalletProfileTokenPriceToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _OKXWalletProfileTokenPrice &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.hasPrice, hasPrice) ||
                other.hasPrice == hasPrice) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.hasPercent, hasPercent) ||
                other.hasPercent == hasPercent) &&
            (identical(other.priceChangePercent24h, priceChangePercent24h) ||
                other.priceChangePercent24h == priceChangePercent24h) &&
            (identical(other.chainId, chainId) || other.chainId == chainId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    address,
    hasPrice,
    price,
    hasPercent,
    priceChangePercent24h,
    chainId,
  );

  @override
  String toString() {
    return 'OKXWalletProfileTokenPrice(address: $address, hasPrice: $hasPrice, price: $price, hasPercent: $hasPercent, priceChangePercent24h: $priceChangePercent24h, chainId: $chainId)';
  }
}

/// @nodoc
abstract mixin class _$OKXWalletProfileTokenPriceCopyWith<$Res>
    implements $OKXWalletProfileTokenPriceCopyWith<$Res> {
  factory _$OKXWalletProfileTokenPriceCopyWith(
    _OKXWalletProfileTokenPrice value,
    $Res Function(_OKXWalletProfileTokenPrice) _then,
  ) = __$OKXWalletProfileTokenPriceCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'address') String? address,
    @JsonKey(name: 'hasPrice') bool hasPrice,
    @NumberDecimalRequiredConverter() @JsonKey(name: 'price') Decimal price,
    @JsonKey(name: 'hasPercent') bool hasPercent,
    @NumberDecimalRequiredConverter()
    @JsonKey(name: 'priceChangePercent24h')
    Decimal priceChangePercent24h,
    @JsonKey(name: 'chainId') int chainId,
  });
}

/// @nodoc
class __$OKXWalletProfileTokenPriceCopyWithImpl<$Res>
    implements _$OKXWalletProfileTokenPriceCopyWith<$Res> {
  __$OKXWalletProfileTokenPriceCopyWithImpl(this._self, this._then);

  final _OKXWalletProfileTokenPrice _self;
  final $Res Function(_OKXWalletProfileTokenPrice) _then;

  /// Create a copy of OKXWalletProfileTokenPrice
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? address = freezed,
    Object? hasPrice = null,
    Object? price = null,
    Object? hasPercent = null,
    Object? priceChangePercent24h = null,
    Object? chainId = null,
  }) {
    return _then(
      _OKXWalletProfileTokenPrice(
        address: freezed == address
            ? _self.address
            : address // ignore: cast_nullable_to_non_nullable
                  as String?,
        hasPrice: null == hasPrice
            ? _self.hasPrice
            : hasPrice // ignore: cast_nullable_to_non_nullable
                  as bool,
        price: null == price
            ? _self.price
            : price // ignore: cast_nullable_to_non_nullable
                  as Decimal,
        hasPercent: null == hasPercent
            ? _self.hasPercent
            : hasPercent // ignore: cast_nullable_to_non_nullable
                  as bool,
        priceChangePercent24h: null == priceChangePercent24h
            ? _self.priceChangePercent24h
            : priceChangePercent24h // ignore: cast_nullable_to_non_nullable
                  as Decimal,
        chainId: null == chainId
            ? _self.chainId
            : chainId // ignore: cast_nullable_to_non_nullable
                  as int,
      ),
    );
  }
}

/// @nodoc
mixin _$WalletPortfolioOKX2 {
  OKXWalletProfileSummary get summary;
  List<OKXWalletProfileToken> get tokens;

  /// Create a copy of WalletPortfolioOKX2
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $WalletPortfolioOKX2CopyWith<WalletPortfolioOKX2> get copyWith =>
      _$WalletPortfolioOKX2CopyWithImpl<WalletPortfolioOKX2>(
        this as WalletPortfolioOKX2,
        _$identity,
      );

  /// Serializes this WalletPortfolioOKX2 to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is WalletPortfolioOKX2 &&
            (identical(other.summary, summary) || other.summary == summary) &&
            const DeepCollectionEquality().equals(other.tokens, tokens));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    summary,
    const DeepCollectionEquality().hash(tokens),
  );

  @override
  String toString() {
    return 'WalletPortfolioOKX2(summary: $summary, tokens: $tokens)';
  }
}

/// @nodoc
abstract mixin class $WalletPortfolioOKX2CopyWith<$Res> {
  factory $WalletPortfolioOKX2CopyWith(
    WalletPortfolioOKX2 value,
    $Res Function(WalletPortfolioOKX2) _then,
  ) = _$WalletPortfolioOKX2CopyWithImpl;
  @useResult
  $Res call({
    OKXWalletProfileSummary summary,
    List<OKXWalletProfileToken> tokens,
  });

  $OKXWalletProfileSummaryCopyWith<$Res> get summary;
}

/// @nodoc
class _$WalletPortfolioOKX2CopyWithImpl<$Res>
    implements $WalletPortfolioOKX2CopyWith<$Res> {
  _$WalletPortfolioOKX2CopyWithImpl(this._self, this._then);

  final WalletPortfolioOKX2 _self;
  final $Res Function(WalletPortfolioOKX2) _then;

  /// Create a copy of WalletPortfolioOKX2
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? summary = null, Object? tokens = null}) {
    return _then(
      _self.copyWith(
        summary: null == summary
            ? _self.summary
            : summary // ignore: cast_nullable_to_non_nullable
                  as OKXWalletProfileSummary,
        tokens: null == tokens
            ? _self.tokens
            : tokens // ignore: cast_nullable_to_non_nullable
                  as List<OKXWalletProfileToken>,
      ),
    );
  }

  /// Create a copy of WalletPortfolioOKX2
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $OKXWalletProfileSummaryCopyWith<$Res> get summary {
    return $OKXWalletProfileSummaryCopyWith<$Res>(_self.summary, (value) {
      return _then(_self.copyWith(summary: value));
    });
  }
}

/// @nodoc
@JsonSerializable()
class _WalletPortfolioOKX2 extends WalletPortfolioOKX2 {
  const _WalletPortfolioOKX2({
    required this.summary,
    required final List<OKXWalletProfileToken> tokens,
  }) : _tokens = tokens,
       super._();
  factory _WalletPortfolioOKX2.fromJson(Map<String, dynamic> json) =>
      _$WalletPortfolioOKX2FromJson(json);

  @override
  final OKXWalletProfileSummary summary;
  final List<OKXWalletProfileToken> _tokens;
  @override
  List<OKXWalletProfileToken> get tokens {
    if (_tokens is EqualUnmodifiableListView) return _tokens;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_tokens);
  }

  /// Create a copy of WalletPortfolioOKX2
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$WalletPortfolioOKX2CopyWith<_WalletPortfolioOKX2> get copyWith =>
      __$WalletPortfolioOKX2CopyWithImpl<_WalletPortfolioOKX2>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$WalletPortfolioOKX2ToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _WalletPortfolioOKX2 &&
            (identical(other.summary, summary) || other.summary == summary) &&
            const DeepCollectionEquality().equals(other._tokens, _tokens));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    summary,
    const DeepCollectionEquality().hash(_tokens),
  );

  @override
  String toString() {
    return 'WalletPortfolioOKX2(summary: $summary, tokens: $tokens)';
  }
}

/// @nodoc
abstract mixin class _$WalletPortfolioOKX2CopyWith<$Res>
    implements $WalletPortfolioOKX2CopyWith<$Res> {
  factory _$WalletPortfolioOKX2CopyWith(
    _WalletPortfolioOKX2 value,
    $Res Function(_WalletPortfolioOKX2) _then,
  ) = __$WalletPortfolioOKX2CopyWithImpl;
  @override
  @useResult
  $Res call({
    OKXWalletProfileSummary summary,
    List<OKXWalletProfileToken> tokens,
  });

  @override
  $OKXWalletProfileSummaryCopyWith<$Res> get summary;
}

/// @nodoc
class __$WalletPortfolioOKX2CopyWithImpl<$Res>
    implements _$WalletPortfolioOKX2CopyWith<$Res> {
  __$WalletPortfolioOKX2CopyWithImpl(this._self, this._then);

  final _WalletPortfolioOKX2 _self;
  final $Res Function(_WalletPortfolioOKX2) _then;

  /// Create a copy of WalletPortfolioOKX2
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({Object? summary = null, Object? tokens = null}) {
    return _then(
      _WalletPortfolioOKX2(
        summary: null == summary
            ? _self.summary
            : summary // ignore: cast_nullable_to_non_nullable
                  as OKXWalletProfileSummary,
        tokens: null == tokens
            ? _self._tokens
            : tokens // ignore: cast_nullable_to_non_nullable
                  as List<OKXWalletProfileToken>,
      ),
    );
  }

  /// Create a copy of WalletPortfolioOKX2
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $OKXWalletProfileSummaryCopyWith<$Res> get summary {
    return $OKXWalletProfileSummaryCopyWith<$Res>(_self.summary, (value) {
      return _then(_self.copyWith(summary: value));
    });
  }
}
