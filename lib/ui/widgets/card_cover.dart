import 'package:card3/exports.dart';
import 'package:flutter/material.dart';

import '/models/card.dart';

class CardCover extends StatelessWidget {
  const CardCover({
    super.key,
    required this.card,
    this.hasNumber = true,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.borderRadius = 12,
  });

  final CardInfo card;
  final bool hasNumber;
  final double? width;
  final double? height;
  final BoxFit fit;
  final double borderRadius;

  @override
  Widget build(BuildContext context) {
    final paddedId = card.id.toString().padLeft(8, '0');

    return Column(
      children: [
        // 卡片图像容器
        SizedBox(
          width: width,
          height: height,
          child: card.backCover.isNotEmpty
              ? ClipRRect(
                  borderRadius: BorderRadius.circular(borderRadius),
                  child: Center(
                    child: MEImage(
                      card.backCover,
                      fit: fit,
                    ),
                  ),
                )
              : ClipRRect(
                  borderRadius: BorderRadius.circular(borderRadius),
                  child: Center(
                    child: card.cardType == CardType.STICKER
                        ? Assets.icons.images.stickerCover.image(
                            fit: BoxFit.contain,
                          )
                        : card.cardType == CardType.WRISTBAND
                        ? Assets.icons.images.wristbandCover.image(
                            fit: BoxFit.contain,
                          )
                        : Assets.icons.images.normalBackcover.image(
                            fit: BoxFit.contain,
                          ),
                  ),
                ),
        ),
        // 卡片编号
        if (hasNumber)
          Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Text(
              '${paddedId.substring(0, 4)} ${paddedId.substring(paddedId.length - 4)}',
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.white,
              ),
            ),
          ),
      ],
    );
  }
}
