import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:me_extensions/me_extensions.dart';

import '/res/assets.gen.dart';
import '/res/colors.gen.dart';

class AppLoading extends StatelessWidget {
  const AppLoading({
    super.key,
    this.size = 100,
    this.alignment = Alignment.center,
  });

  final double size;
  final AlignmentGeometry? alignment;

  static Future<void> show({
    Color? backgroundColor,
    double size = 100,
    AlignmentGeometry? alignment = Alignment.center,
  }) async {
    if (SmartDialog.checkExist(dialogTypes: {SmartAllDialogType.loading})) {
      return;
    }
    final Color bgColor = backgroundColor ?? ColorName.themeColorDark;
    return SmartDialog.showLoading(
      builder: (_) => AppLoading(size: size),
      backType: SmartBackType.block,
      clickMaskDismiss: false,
      maskColor: bgColor.withValues(alpha: 0.64),
    );
  }

  static Future<void> dismiss() {
    if (SmartDialog.checkExist(dialogTypes: {SmartAllDialogType.loading})) {
      return SmartDialog.dismiss(status: SmartStatus.loading);
    }
    return SynchronousFuture<void>(null);
  }

  static Future<T> run<T>(
    Future<T> Function() callback, [
    Duration? duration,
  ]) async {
    await AppLoading.show();
    try {
      if (duration != null) {
        return await callback().atLeast(duration);
      }
      return await callback();
    } finally {
      await AppLoading.dismiss();
    }
  }

  @override
  Widget build(BuildContext context) {
    Widget body = GestureDetector(
      onLongPress: () {
        Feedback.forLongPress(context);
        AppLoading.dismiss();
      },
      child: Assets.lottie.loading.lottie(
        width: size,
      ),
    );
    if (alignment != null) {
      body = Align(alignment: alignment!, child: body);
    }
    return body;
  }
}
