import 'dart:async';

import 'package:card3/exports.dart';
import 'package:flutter/material.dart';

import '/feat/link/helper.dart';
import '/feat/nfc/handle.dart';
import '/feat/nfc/helper.dart';
import '/internals/methods.dart' show isNetworkError;
import '/models/card.dart';
import '/models/user.dart' show UserInfo;
import '/provider/api.dart';
import '/provider/card.dart';
import '/provider/user.dart';
import '/ui/widgets/card_cover.dart';
import '/ui/widgets/social/data.dart' show SocialSvgIcon;
import '/ui/widgets/social/profile/avatar.dart';
import '/ui/widgets/social/profile/description.dart';
import '/ui/widgets/social/social_grid.dart';
import '/ui/widgets/topic.dart';

final _nfcHandlerKeyProvider = Provider.autoDispose<GlobalKey<NfcHandlerState>>((ref) {
  return GlobalKey<NfcHandlerState>();
});

class Home extends ConsumerStatefulWidget {
  const Home({super.key});

  @override
  ConsumerState<Home> createState() => _HomeState();
}

class _HomeState extends ConsumerState<Home> {
  bool _hasShownActivationGuide = false;

  @override
  void initState() {
    super.initState();
    // 设置全局回调函数，处理卡片激活
    AppLinkHelper.setCardActivationCallback((cardCode, activeCode) {
      _handleCardActivation(cardCode, activeCode);
    });
  }

  // 检查是否需要显示激活引导
  void _checkActivationGuide() {
    if (_hasShownActivationGuide || !mounted) {
      return;
    }

    final cardsResult = ref.watch(fetchMyCardsProvider);
    final cards = cardsResult.valueOrNull;
    final shouldShowGuide = ref.watch(watchIsActiveGuideProvider);

    // 只有当cards数据加载完成且需要显示引导时才显示
    if (shouldShowGuide == true && cards != null && !_hasShownActivationGuide) {
      _hasShownActivationGuide = true;
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          _showActivationGuideSheet();
        }
      });
    }
  }

  @override
  void dispose() {
    // 清除全局回调函数
    AppLinkHelper.clearCardActivationCallback();
    super.dispose();
  }

  // 处理卡片激活事件
  Future<void> _handleCardActivation(String cardCode, String activeCode) async {
    final card = await ref.read(apiServiceProvider).getCard(cardCode: cardCode);
    if (card.isActive) {
      Card3ToastUtil.showToast(
        message: ToastMessages.cardAlreadyActivated,
        duration: const Duration(seconds: 3),
      );
    } else {
      // 显示激活弹窗
      _showActivateCardSheet(
        context: context,
        ref: ref,
        params: NfcCardParams(uid: cardCode, ctr: '', cmac: '', activeCode: activeCode),
        card: card,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    // 每次build时检查是否需要显示激活引导
    _checkActivationGuide();

    return RefreshIndicator(
      onRefresh: () => _refreshData(ref),
      displacement: MediaQuery.paddingOf(context).top + 48.0,
      child: const Column(
        children: [
          _Header(),
          Expanded(child: _MainBody()),
        ],
      ),
    );
  }

  // 显示激活引导弹窗
  void _showActivationGuideSheet() {
    if (!mounted) {
      return;
    }

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 标题和关闭按钮
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Got a Card3 NFC item?',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  color: Colors.black,
                  onPressed: () => Navigator.pop(context),
                ),
              ],
            ),
            const SizedBox(height: 60),

            // 卡片图像
            SizedBox(
              width: 200,
              height: 212,
              child: Assets.icons.aguide.svg(
                width: 200,
                height: 212,
              ),
            ),

            const SizedBox(height: 60),
            // 激活按钮
            Column(
              children: [
                ElevatedButton(
                  onPressed: () {
                    final nfcHandler = ref.read(_nfcHandlerKeyProvider).currentState;
                    Navigator.pop(context); // 关闭底部弹窗

                    // 触发主NFC扫描器
                    Future.delayed(const Duration(milliseconds: 300), () {
                      nfcHandler?.startScan();
                    });
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: ColorName.themeColorDark,
                    minimumSize: const Size(double.infinity, 56),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                  ),
                  child: const Text(
                    'Activate Now',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(height: 20),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

Future<void> _refreshData(WidgetRef ref) async {
  ref.invalidate(fetchUserInfoProvider);
  ref.invalidate(fetchMyCardsProvider);
  ref.invalidate(fetchEthccProfileProvider);
  ref.invalidate(fetchSocialsProvider);
  await Future.wait([
    ref.read(fetchUserInfoProvider().future),
    ref.read(fetchMyCardsProvider.future),
  ]);
}

class _Header extends ConsumerWidget {
  const _Header();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final localUser = ref.watch(userRepoProvider);
    final userInfoResult = ref.watch(fetchUserInfoProvider());
    final userInfo = userInfoResult.valueOrNull ?? localUser;
    final cardsResult = ref.watch(fetchMyCardsProvider);
    final cards = cardsResult.valueOrNull;
    final filteredList = cards?.where((card) => !card.virtualCard).toList();
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16).copyWith(
        top: MediaQuery.paddingOf(context).top,
        bottom: 16,
      ),
      child: Row(
        children: [
          Stack(
            clipBehavior: Clip.none,
            children: [
              Tapper(
                onTap: () {
                  meNavigator.pushNamed(Routes.notification.name);
                },
                child: Assets.icons.message.svg(
                  width: 36,
                  height: 36,
                  colorFilter: context.meTheme.primaryTextColor.filter,
                ),
              ),
              if (userInfo case final user? when user.lastMessageId != user.latestMessageId)
                Positioned(
                  right: -2,
                  top: -2,
                  child: Container(
                    width: 12,
                    height: 12,
                    decoration: BoxDecoration(
                      color: Colors.red,
                      shape: BoxShape.circle,
                      border: Border.all(color: Colors.white, width: 2),
                    ),
                  ),
                ),
            ],
          ),
          Expanded(
            child: Center(
              child: Tapper(
                onTap: () {
                  // 显示卡片选择底部弹窗
                  showModalBottomSheet(
                    context: context,
                    backgroundColor: context.meTheme.primaryTextColor,
                    clipBehavior: Clip.antiAlias,
                    scrollControlDisabledMaxHeightRatio: 0.8,
                    builder: (context) => _CardSheet(cards: filteredList ?? []),
                  );
                },
                child: Container(
                  height: 44,
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(50),
                    border: Border.all(color: Colors.grey),
                  ),
                  child: Row(
                    spacing: 4.0,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        margin: const EdgeInsetsDirectional.only(end: 4.0),
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          color: context.themeColor,
                        ),
                        child: const Text(
                          'CARDS',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      Text(
                        '× ${filteredList?.length ?? 0}',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Icon(
                        Icons.keyboard_arrow_down,
                        color: context.textTheme.bodyMedium?.color,
                        size: 20,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),

          // NFC扫描按钮
          NfcHandler(
            key: ref.watch(_nfcHandlerKeyProvider),
            onResult: (result, success) async {
              if (NfcHelper.isCard3Format(result)) {
                final params = NfcHelper.extractIdentifier(result);
                if (params != null) {
                  final card = await ref.read(apiServiceProvider).getCard(cardCode: params.uid);
                  if (card.isActive) {
                    Card3ToastUtil.showToast(
                      message: ToastMessages.cardAlreadyActivated,
                      duration: const Duration(seconds: 3),
                    );
                  } else {
                    // 显示激活弹窗
                    _showActivateCardSheet(
                      context: context,
                      ref: ref,
                      params: params,
                      card: card,
                    );
                  }
                }
              } else {
                Card3ToastUtil.showToast(
                  message: ToastMessages.invalidCardFormat,
                  duration: const Duration(seconds: 3),
                );
              }
            },
            onStateChanged: (state) {
              LogUtil.d('state: $state');
            },
            child: Assets.icons.homeCardNfc.svg(
              width: 36,
              height: 36,
              colorFilter: context.meTheme.primaryTextColor.filter,
            ),
          ),
        ],
      ),
    );
  }
}

void _showActivateCardSheet({
  required BuildContext context,
  required WidgetRef ref,
  required NfcCardParams params,
  required dynamic card,
}) {
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    builder: (context) => Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 标题和关闭按钮
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'New item detected',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              IconButton(
                icon: const Icon(Icons.close),
                color: Colors.black,
                onPressed: () => Navigator.pop(context),
              ),
            ],
          ),
          const SizedBox(height: 20),

          // 卡片图像
          SizedBox(
            width: 150,
            height: 220,
            child: CardCover(
              card: card,
              width: 120,
              height: 180,
            ),
          ),

          const SizedBox(height: 10),
          const Text(
            'To be activated',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 30),

          // 激活按钮
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context); // 关闭底部弹窗

              // 使用OverlayEntry替代Dialog，避免导航栈问题
              final overlayState = Overlay.of(context);
              final overlayEntry = OverlayEntry(
                builder: (context) => Container(
                  color: Colors.black54,
                  alignment: Alignment.center,
                  child: const CircularProgressIndicator(),
                ),
              );

              // 显示加载指示器
              overlayState.insert(overlayEntry);

              try {
                // 调用API激活卡片
                await ref
                    .read(apiServiceProvider)
                    .activeCardByNfc(
                      uid: params.uid,
                      ctr: params.ctr ?? '',
                      cmac: params.cmac ?? '',
                      activeCode: params.activeCode ?? '',
                    );
                // 刷新卡片列表
                ref.invalidate(fetchMyCardsProvider);
                Card3ToastUtil.showToast(message: ToastMessages.cardActivatedSuccessfully);
              } catch (e) {
                Card3ToastUtil.showToast(message: ToastMessages.activationError(e.toString()));
              } finally {
                // 安全移除加载指示器
                overlayEntry.remove();
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: ColorName.themeColorDark,
              minimumSize: const Size(double.infinity, 56),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
            ),
            child: const Text(
              'Activate Now',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ),
          const SizedBox(height: 20),
        ],
      ),
    ),
  );
}

class _MainBody extends ConsumerWidget {
  const _MainBody();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final localUser = ref.watch(userRepoProvider);
    final userInfoResult = ref.watch(fetchUserInfoProvider());
    final userInfo = userInfoResult.valueOrNull ?? localUser;
    final cardsResult = ref.watch(fetchMyCardsProvider);
    final cards = cardsResult.valueOrNull;

    final isETHCCMode = ref.watch(validateETHCCProfileProvider(validateProfile: true));

    return ListView(
      padding: const EdgeInsets.only(bottom: 100.0),
      children: [
        // 个人资料卡片
        const _ProfileCard(),
        if (isETHCCMode == true) const TopicWidget() else const SizedBox(height: 8),
        // 社交链接部分
        const _Socials(),
        // 添加预览和分享按钮
        Container(
          margin: const EdgeInsets.only(top: 24).copyWith(bottom: 24),
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            children: [
              // 预览按钮
              Expanded(
                child: Tapper(
                  onTap: () async {
                    // ignore: prefer_final_locals
                    UserInfo? user = userInfo;
                    if (user == null) {
                      return;
                    }
                    if (userInfo?.currentType == 1) {
                      // Launch direct link profile.
                      launchUrlString(userInfo?.redirectUrl ?? '');
                      return;
                    }
                    // ignore: prefer_final_locals
                    String code = user.referralCode;
                    final previewUrl =
                        '$envUrlSocial/profile'
                        '?card_code=$code'
                        '&action=href'
                        '&preview=1';
                    LogUtil.d('previewUrl: $previewUrl');
                    context.navigator.pushNamed(
                      Routes.socialProfile.name,
                      arguments: Routes.socialProfile.d(
                        code: code,
                        profile: user == userInfo ? user : null,
                      ),
                    );
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 14),
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: ColorName.themeColorDark,
                        width: 1,
                      ),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: const Center(
                      child: Text(
                        'Preview',
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                          color: ColorName.themeColorDark,
                        ),
                      ),
                    ),
                  ),
                ),
              ),

              const SizedBox(width: 12),

              // 分享按钮
              Expanded(
                child: Tapper(
                  onTap: () {
                    final cardCode = cards
                        ?.firstWhere(
                          (o) => o.virtualCard,
                          orElse: () => cards.last,
                        )
                        .cardCode;
                    if (cardCode == null) {
                      return;
                    }
                    meNavigator.pushNamed(
                      Routes.share.name,
                      arguments: Routes.share.d(cardCode: cardCode),
                    );
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 14),
                    decoration: BoxDecoration(
                      color: ColorName.themeColorDark,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          'Share',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            color: Colors.white,
                          ),
                        ),
                        SizedBox(width: 4),
                        Icon(
                          Icons.arrow_outward,
                          color: Colors.white,
                          size: 16,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

class _ProfileCard extends ConsumerWidget {
  const _ProfileCard();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final localUser = ref.watch(userRepoProvider);
    final userInfoResult = ref.watch(fetchUserInfoProvider());
    final userInfo = userInfoResult.valueOrNull ?? localUser;
    final isDirectLink = userInfo?.currentType == 1;

    return Opacity(
      opacity: isDirectLink ? 0.4 : 1.0,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 16),
        child: Stack(
          clipBehavior: Clip.none,
          children: [
            // 卡片主体
            Container(
              margin: const EdgeInsets.only(top: 40),
              padding: const EdgeInsets.only(top: 50, bottom: 16),
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withValues(alpha: 0.1),
                    spreadRadius: 1,
                    blurRadius: 5,
                  ),
                ],
              ),
              child: GestureDetector(
                onTap: () {
                  showModalBottomSheet(
                    context: context,
                    scrollControlDisabledMaxHeightRatio: 0.8,
                    builder: (context) => const DescriptionActionSheet(),
                  );
                },
                child: Column(
                  children: [
                    // 姓名
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Text(
                        switch (userInfo?.name) {
                          final n? when n.isNotEmpty => n,
                          _ => 'Name',
                        },
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: switch (userInfo?.name) {
                            final n? when n.isNotEmpty => Colors.black,
                            _ => Colors.grey,
                          },
                        ),
                        textAlign: TextAlign.center,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    // 职位
                    Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 4,
                      ),
                      child: Text(
                        (userInfo?.title ?? '') != '' ? userInfo!.title : 'Title',
                        style: TextStyle(
                          fontSize: 16,
                          color: (userInfo?.title ?? '') != '' ? Colors.black87 : Colors.grey,
                        ),
                        textAlign: TextAlign.center,
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                    ),
                    // 公司
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Text(
                        (userInfo?.company ?? '') != '' ? userInfo!.company : 'Company',
                        style: TextStyle(
                          fontSize: 16,
                          color: (userInfo?.company ?? '') != '' ? Colors.black87 : Colors.grey,
                        ),
                        textAlign: TextAlign.center,
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            // 头像（绝对定位在卡片顶部）
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              child: Center(
                child: GestureDetector(
                  onTap: () {
                    // 实际应打开头像编辑弹窗
                    showModalBottomSheet(
                      context: context,
                      scrollControlDisabledMaxHeightRatio: 0.7,
                      builder: (context) => const Avatar(),
                    );
                  },
                  child: Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.withValues(alpha: 0.2),
                          spreadRadius: 1,
                          blurRadius: 3,
                        ),
                      ],
                    ),
                    child: ClipOval(
                      child: userInfo?.avatar != null && userInfo!.avatar != ''
                          ? MEImage(
                              userInfo.avatar,
                              width: 80,
                              height: 80,
                              fit: BoxFit.cover,
                            )
                          : Icon(
                              Icons.account_circle,
                              size: 80,
                              color: Colors.grey.shade400,
                            ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

final _socialsReorderingListProvider = StateProvider.autoDispose<List<Social>?>((ref) => null);

class _Socials extends ConsumerStatefulWidget {
  const _Socials();

  @override
  ConsumerState<_Socials> createState() => _SocialsState();
}

class _SocialsState extends ConsumerState<_Socials> {
  CancelToken? _cancelToken;

  @override
  Widget build(BuildContext context) {
    final localUser = ref.watch(userRepoProvider);
    final userInfoResult = ref.watch(fetchUserInfoProvider());
    final userInfo = userInfoResult.valueOrNull ?? localUser;
    final isDirectLink = userInfo?.currentType == 1;
    final socialsResult = ref.watch(fetchSocialsProvider());
    final socials = socialsResult.valueOrNull;
    final socialsReordering = ref.watch(_socialsReorderingListProvider);

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        children: [
          // 标题和添加按钮
          Row(
            spacing: 8.0,

            children: [
              const Expanded(
                child: Text(
                  'Social Links',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Tapper(
                onTap: () {
                  if (socialsResult.hasError && !socialsResult.isLoading) {
                    ref.invalidate(fetchSocialsProvider);
                    return;
                  }
                  showModalBottomSheet(
                    context: context,
                    scrollControlDisabledMaxHeightRatio: 0.7,
                    builder: (context) => const _SocialDialog(),
                  );
                },
                child: Container(
                  width: 36,
                  height: 36,
                  padding: const EdgeInsets.all(6.0),
                  decoration: BoxDecoration(
                    color: ColorName.primaryTextColorLight,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: FittedBox(
                    fit: BoxFit.scaleDown,
                    child: switch (socialsResult.isLoading) {
                      true => const AppLoading(),
                      false => Icon(
                        socialsResult.hasError ? Icons.refresh_rounded : Icons.add,
                        color: Colors.white,
                      ),
                    },
                  ),
                ),
              ),
            ],
          ),

          // 根据社交数据的加载状态显示不同的内容
          socialsResult.when(
            loading: () => const Padding(
              padding: EdgeInsets.symmetric(vertical: 24.0),
              child: Center(child: CircularProgressIndicator()),
            ),
            error: (error, stack) => Padding(
              padding: const EdgeInsets.symmetric(vertical: 16.0),
              child: Text(
                'load social links failed: '
                '${isNetworkError(error) ? context.l10nME.networkError : error}',
                style: const TextStyle(color: Colors.red),
              ),
            ),
            data: (data) {
              if (data.isEmpty) {
                return _buildSocialsPlaceholder(context);
              }
              final List<Social> list;
              if (socialsReordering != null) {
                list = socialsReordering;
              } else {
                list = data;
              }
              return ReorderableList(
                padding: const EdgeInsets.only(top: 8.0),
                physics: const NeverScrollableScrollPhysics(),
                shrinkWrap: true,
                itemExtent: 64.0,
                itemCount: list.length,
                itemBuilder: (context, index) {
                  final social = list[index];
                  return ReorderableDelayedDragStartListener(
                    enabled: list.length > 1 && !isDirectLink,
                    index: index,
                    key: ValueKey('social-reorderable-${social.id}'),
                    child: Padding(
                      padding: const EdgeInsets.only(bottom: 8.0),
                      child: ProviderScope(
                        overrides: [
                          _socialItemProvider.overrideWithValue(social),
                        ],
                        child: const _SocialItem(),
                      ),
                    ),
                  );
                },
                proxyDecorator: (child, index, animation) {
                  final social = list[index];
                  return ScaleTransition(
                    scale: animation.drive(Tween(begin: 1.0, end: 1.06)),
                    child: ProviderScope(
                      overrides: [
                        _socialItemProvider.overrideWithValue(social),
                      ],
                      child: const _SocialItem(),
                    ),
                  );
                },
                onReorder: (int oldIndex, int newIndex) async {
                  if (oldIndex == newIndex) {
                    return;
                  }

                  final newList = list.toList();
                  final item = newList.removeAt(oldIndex);
                  newList.insert(
                    newIndex - oldIndex > 0 ? newIndex - 1 : newIndex,
                    item,
                  );
                  ref.read(_socialsReorderingListProvider.notifier).state = newList;
                  final reorderedMap = Map.fromEntries(
                    newList.mapIndexed(
                      (i, e) => MapEntry<String, int>(e.id.toString(), newList.length - i),
                    ),
                  );
                  // Cancel the previous sort request.
                  _cancelToken?.cancel();
                  final cancelToken = _cancelToken = CancelToken();
                  await ref
                      .read(apiServiceProvider)
                      .socialsReorder(
                        idInOrders: reorderedMap,
                        cancelToken: cancelToken,
                      );
                  if (mounted) {
                    final _ = await ref.refresh(fetchSocialsProvider().future);
                  }
                  if (mounted) {
                    ref.read(_socialsReorderingListProvider.notifier).state = null;
                  }
                },
              );
            },
          ),

          // 直接链接开关
          if (socials case final list? when list.isNotEmpty)
            Column(
              children: [
                const Divider(height: 32),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Direct Link',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          'with one single link',
                          style: TextStyle(
                            color: Color(0xFF666666),
                          ),
                        ),
                      ],
                    ),
                    Switch(
                      value: userInfo?.currentType == 1,
                      activeColor: ColorName.themeColorDark,
                      onChanged: (value) => AppLoading.run(() async {
                        // 实现切换单链接逻辑
                        if (value) {
                          final filteredSocials = socials
                              .where((o) => o.platformName != 'Phone' && o.platformName != 'Email')
                              .toList();
                          final redirectUrl = filteredSocials.isNotEmpty
                              ? filteredSocials.first.platformUrl + filteredSocials.first.handleName
                              : '';
                          await ref
                              .read(apiServiceProvider)
                              .updateUserInfo(
                                redirectUrl: redirectUrl,
                                currentType: 1,
                              );
                          ref.invalidate(fetchUserInfoProvider);
                        } else {
                          await ref.read(apiServiceProvider).updateUserInfo(currentType: 3);
                          ref.invalidate(fetchUserInfoProvider);
                        }
                      }),
                    ),
                  ],
                ),
              ],
            ),
        ],
      ),
    );
  }

  Widget _buildSocialsPlaceholder(BuildContext context) {
    final platforms = SocialPlatform.values.where((o) => o.event == null);
    return Container(
      margin: const EdgeInsets.only(top: 16),
      height: 40,
      child: Stack(
        children: List.generate(
          platforms.length.min(8),
          (index) {
            final platform = platforms.elementAt(index);
            return Positioned(
              left: index * 28.0,
              child: Container(
                height: 40,
                width: 40,
                decoration: BoxDecoration(
                  color: platform.backgroundColor,
                  borderRadius: BorderRadius.circular(16),
                  gradient: platform.gradient,
                ),
                child: Center(
                  child: Transform.scale(
                    scale: 0.5,
                    child: SocialSvgIcon(platform: platform),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}

final _socialItemProvider = Provider.autoDispose<Social>(
  (ref) => throw UnimplementedError(),
);

class _SocialItem extends ConsumerWidget {
  const _SocialItem();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final social = ref.watch(_socialItemProvider);
    final currentPlatform = SocialPlatform.values.firstWhere(
      (option) => option.name.displayName == social.platformName,
      orElse: () => SocialPlatform.values.first,
    );

    final localUser = ref.watch(userRepoProvider);
    final userInfoResult = ref.watch(fetchUserInfoProvider());
    final userInfo = userInfoResult.valueOrNull ?? localUser;
    final isDirectLink = userInfo?.currentType == 1;
    final socialsResult = ref.watch(fetchSocialsProvider());
    final socials = socialsResult.valueOrNull ?? <Social>[];

    final isHighlighted = switch ((isDirectLink, userInfo?.redirectUrl)) {
      (true, final s?) => s == '${social.platformUrl}${social.handleName}',
      _ => true,
    };

    return Tapper(
      onTap: () {
        final isSingleLink = switch ((userInfo, socials)) {
          (final u?, final s) when s.isNotEmpty =>
            u.currentType == 1 && u.redirectUrl == '${s.first.platformUrl}${s.first.handleName}',
          _ => false,
        };
        meNavigator.pushNamed(
          Routes.social.name,
          arguments: Routes.social.d(
            social: social,
            platform: currentPlatform,
            isSingleLink: isSingleLink,
          ),
        );
      },
      child: AnimatedOpacity(
        duration: kThemeAnimationDuration,
        opacity: isHighlighted ? 1.0 : 0.4,
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 2),
          height: 56,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: userInfo?.currentType == 1 && userInfo?.redirectUrl == '${social.platformUrl}${social.handleName}'
                  ? ColorName.themeColorDark
                  : Colors.white,
              width: 3,
            ),
            color: ColorName.primaryTextColorLight,
          ),
          child: Row(
            children: [
              // 单链接指示器
              if (userInfo?.currentType == 1)
                Container(
                  width: 40,
                  padding: const EdgeInsetsDirectional.only(start: 8),
                  alignment: Alignment.center,
                  child: _TopSort(
                    social: social,
                    currentPlatform: currentPlatform,
                    socialsData: socials,
                  ),
                )
              else if (socials.length > 1)
                Container(
                  width: 20.0,
                  padding: const EdgeInsetsDirectional.only(start: 10.0),
                  child: Assets.icons.dragHandle.svg(colorFilter: Colors.grey.filter),
                ),
              Container(
                width: 32,
                height: 32,
                margin: const EdgeInsets.symmetric(
                  horizontal: 8,
                  vertical: 4,
                ),
                decoration: BoxDecoration(
                  color: currentPlatform.backgroundColor,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Transform.scale(
                  scale: 0.6,
                  child: SocialSvgIcon(platform: currentPlatform),
                ),
              ),
              Expanded(
                child: Text(
                  social.platformName == 'Whatsapp' || social.platformName == 'MemeX'
                      ? social.platformName
                      : social.handleName.replaceAll('tel:+', '').replaceAll('mailto:', ''),
                  style: const TextStyle(
                    fontSize: 18,
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              if (social.isVerify == true)
                Padding(
                  padding: const EdgeInsetsDirectional.only(end: 12),
                  child: Assets.icons.verified.svg(width: 24, height: 24),
                ),
            ],
          ),
        ),
      ),
    );
  }
}

class _SocialDialog extends ConsumerWidget {
  const _SocialDialog();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: const EdgeInsets.all(16.0).copyWith(bottom: 0.0),
      child: Column(
        mainAxisSize: MainAxisSize.min, // 不要占用全部空间
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Add Links',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              IconButton(
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
                icon: const Icon(Icons.close, color: Colors.black, size: 24),
                onPressed: () => Navigator.pop(context),
              ),
            ],
          ),
          const SizedBox(height: 24),
          const Expanded(child: SocialGrid()),
        ],
      ),
    );
  }
}

class _TopSort extends ConsumerWidget {
  const _TopSort({
    required this.social,
    required this.currentPlatform,
    required this.socialsData,
  });

  final Social social;
  final SocialPlatform currentPlatform;
  final List<Social> socialsData;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final localUser = ref.watch(userRepoProvider);
    final userInfoResult = ref.watch(fetchUserInfoProvider());
    final userInfo = userInfoResult.valueOrNull ?? localUser;

    return Center(
      child: switch (userInfo?.redirectUrl) {
        final s? when s == '${social.platformUrl}${social.handleName}' => const Icon(
          Icons.check_circle,
          color: Colors.white,
          size: 24,
        ),
        _ => Tapper(
          onTap: () async {
            if (socialsData.isEmpty) {
              return;
            }

            final int currentIndex = socialsData.indexWhere((s) => s.id == social.id);
            if (currentIndex < 0) {
              return;
            }

            AppLoading.run(() async {
              // 实现置顶移动
              List<Social> moveItemToFirst(List<Social> list, int index) {
                final newList = List<Social>.from(list);
                if (index >= 0 && index < newList.length) {
                  final item = newList.removeAt(index);
                  newList.insert(0, item);
                }
                return newList;
              }

              final newList = moveItemToFirst(socialsData, currentIndex);
              final reorderedMap = Map.fromEntries(
                newList.mapIndexed(
                  (i, e) => MapEntry<String, int>(e.id.toString(), newList.length - i),
                ),
              );
              await ref.read(apiServiceProvider).socialsReorder(idInOrders: reorderedMap);
              await ref
                  .read(apiServiceProvider)
                  .updateUserInfo(redirectUrl: '${social.platformUrl}${social.handleName}');
              await Future.wait(
                [
                  ref.refresh(fetchUserInfoProvider().future),
                  ref.refresh(fetchSocialsProvider().future),
                ],
              );
            });
          },
          child: Assets.icons.verticalTop.svg(
            width: 24,
            height: 24,
            colorFilter: const Color(0xFF9C9CA4).filter,
          ),
        ),
      },
    );
  }
}

class _CardSheet extends ConsumerWidget {
  const _CardSheet({required this.cards});

  final List<CardInfo> cards;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Theme(
      data: themeBy(
        meTheme: defaultMEThemeDark,
        locale: Localizations.localeOf(context),
      ),
      child: _CardSheetBody(cards: cards),
    );
  }
}

class _CardSheetBody extends StatelessWidget {
  const _CardSheetBody({required this.cards});

  final List<CardInfo> cards;

  @override
  Widget build(BuildContext context) {
    // 按nfcType分组卡片，并使NFC424组在前面
    final type424 = <CardInfo>[];
    final type215 = <CardInfo>[];
    for (final card in cards) {
      if (card.nfcType == NfcType.NFC424) {
        type424.add(card);
      } else if (card.nfcType == NfcType.NFC215) {
        type215.add(card);
      }
    }

    return Padding(
      padding: const EdgeInsets.all(16.0).copyWith(bottom: 0.0),
      child: Column(
        spacing: 24.0,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'My Card3 Collections',
                style: context.textTheme.bodyMedium?.copyWith(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              IconButton(
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
                icon: const Icon(Icons.close, size: 28),
                onPressed: () => Navigator.pop(context),
              ),
            ],
          ),
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                // NFC424卡片组 - Earn Points with Every Use
                if (type424.isNotEmpty) ...[
                  _buildTitle(context, 'V2.0'),
                  const SizedBox(height: 16),
                  _buildCardGrid(type424),
                  if (type215.isNotEmpty)
                    const Divider(
                      height: 32,
                      thickness: 1,
                      color: Color(0xFF4A4A59),
                    ),
                ],

                // NFC215卡片组 - Non-Reward
                if (type215.isNotEmpty) ...[
                  _buildTitle(context, 'V1.0'),
                  const SizedBox(height: 16),
                  _buildCardGrid(type215),
                ],

                Gap.bottomPadding(context, 50.0),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTitle(BuildContext context, String title) {
    return Text(
      title,
      style: context.textTheme.headlineSmall,
    );
  }

  // 构建卡片网格
  Widget _buildCardGrid(List<CardInfo> cards) {
    return GridView.builder(
      padding: EdgeInsets.zero,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        childAspectRatio: 0.56, // 调整卡片比例更接近参考图
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: cards.length,
      itemBuilder: (context, index) {
        final card = cards[index];
        final paddedId = card.id.toString().padLeft(8, '0');

        return Column(
          spacing: 8.0,
          children: [
            // 卡片图像容器
            Expanded(
              child: Center(
                child: MEImage(
                  card.backCover,
                  fit: BoxFit.cover,
                  borderRadius: 12.0.rCircular,
                  emptyBuilder: (context) => switch (card.cardType) {
                    CardType.STICKER => Assets.icons.images.stickerCover.image(
                      fit: BoxFit.cover,
                    ),
                    CardType.WRISTBAND => Assets.icons.images.wristbandCover.image(
                      fit: BoxFit.cover,
                    ),
                    _ => Assets.icons.images.normalBackcover.image(
                      fit: BoxFit.cover,
                    ),
                  },
                ),
              ),
            ),
            // 卡片编号
            Text(
              '${paddedId.substring(0, 4)} ${paddedId.substring(paddedId.length - 4)}',
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.white,
              ),
            ),
          ],
        );
      },
    );
  }
}
