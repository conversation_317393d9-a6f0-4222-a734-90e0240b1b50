import 'package:card3/exports.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:qr_flutter/qr_flutter.dart';

import '/api/okx.dart' show OKXWalletProfileToken;
import '/models/business.dart';
import '/provider/chain.dart' show networkProvider, walletAddressProvider;
import '/provider/token.dart';
import '/services/select_token.dart';
import '/ui/biz/wallet/select_network.dart';

class Wallet extends ConsumerStatefulWidget {
  const Wallet({super.key});

  @override
  ConsumerState<Wallet> createState() => _WalletState();
}

class _WalletState extends ConsumerState<Wallet> {
  static Future<void> _onRefresh(WidgetRef ref) async {
    final network = ref.read(networkProvider);
    final address = ref.read(walletAddressProvider);
    await writeWalletPortfolioOKXCache(address, null);
    ref.invalidate(
      fetchRawWalletPortfolioOKXProvider(network: network, address: address),
    );
    await ref.read(fetchWalletPortfolioOKXProvider(network: network).future);
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: RefreshIndicator(
        onRefresh: () => _onRefresh(ref),
        displacement: MediaQuery.paddingOf(context).top,
        child: CustomScrollView(
          slivers: [
            SliverGap.topPadding(context, 4.0),
            SliverList(
              delegate: SliverChildListDelegate([
                const _TopActions(),
                const _WalletInfo(),
                const SizedBox(height: 20),
                const _Buttons(),
              ]),
            ),
            const SliverGap.v(20.0),
            const _TokenList(),
            const SliverGap.v(50.0),
          ],
        ),
      ),
    );
  }
}

class _TopActions extends ConsumerWidget {
  const _TopActions();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentNetwork = ref.watch(networkProvider);
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        GestureDetector(
          onTap: () {
            showModalBottomSheet(
              context: context,
              scrollControlDisabledMaxHeightRatio: 0.7,
              backgroundColor: Colors.white,
              clipBehavior: Clip.antiAlias,
              shape: RoundedRectangleBorder(
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(20.0),
                ),
                side: BorderSide(
                  color: context.theme.dividerColor,
                ),
              ),
              builder: (context) => const SelectNetwork(),
            );
          },
          child: Container(
            width: 100,
            height: 40,
            padding: const EdgeInsets.symmetric(horizontal: 8),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(24),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // 网络图标
                if (currentNetwork.iconUrl.isNotEmpty)
                  Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: MEImage(
                      currentNetwork.iconUrl,
                      clipOval: true,
                      fit: BoxFit.cover,
                      alternativeSVG: true,
                    ),
                  )
                else
                  Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      color: Colors.deepPurple[200],
                      borderRadius: BorderRadius.circular(6),
                      border: Border.all(
                        color: Colors.black,
                        width: 2,
                      ),
                    ),
                    child: const Center(
                      child: Text(
                        'E',
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                        ),
                      ),
                    ),
                  ),
                const SizedBox(width: 4),
                Expanded(
                  child: Text(
                    currentNetwork.shortName,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                const Icon(
                  Icons.keyboard_arrow_down,
                  color: Colors.black54,
                  size: 24,
                ),
              ],
            ),
          ),
        ),
        GestureDetector(
          onTap: () {
            meNavigator.pushNamed(Routes.setting.name);
          },
          child: Assets.icons.setting.index.svg(
            width: 30,
            height: 30,
            colorFilter: Colors.black.filter,
          ),
        ),
      ],
    );
  }
}

class _WalletInfo extends ConsumerWidget {
  const _WalletInfo();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final network = ref.watch(networkProvider);
    final address = ref.watch(walletAddressProvider);
    final result = ref.watch(fetchWalletPortfolioOKXProvider(network: network));

    // 格式化钱包地址显示：前6位 + ... + 后4位
    String? displayAddress;
    if (address != null && address.isNotEmpty) {
      displayAddress = address.length > 10
          ? '${address.substring(0, 6)}...${address.substring(address.length - 4)}'
          : address;
    }

    return Container(
      margin: const EdgeInsets.only(top: 20),
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 30, horizontal: 20),
      child: Column(
        spacing: 16.0,
        mainAxisSize: MainAxisSize.min,
        children: [
          // 余额标题
          const Text(
            'Balance',
            style: TextStyle(
              color: Colors.grey,
              fontSize: 18,
            ),
          ),

          // 余额金额
          Text(
            result.valueOrNull?.summary.tokenTotalCurrencyAmount.toNumerical() ?? '-',
            style: const TextStyle(
              fontSize: 36,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),

          // 钱包地址
          GestureDetector(
            onTap: () {
              if (address == null || address.isEmpty) {
                return;
              }
              // 复制钱包地址
              Clipboard.setData(ClipboardData(text: address));
              Card3ToastUtil.showToast(message: ToastMessages.copied);
            },
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  displayAddress ?? '--',
                  style: const TextStyle(
                    color: ColorName.themeColorDark,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(width: 5),
                const Icon(
                  Icons.copy_outlined,
                  color: ColorName.themeColorDark,
                  size: 20,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class _Buttons extends ConsumerWidget {
  const _Buttons();

  // 处理发送按钮点击事件
  void _handleSendButtonTap(
    BuildContext context,
    Network? currentNetwork,
    List<IToken> tokens,
  ) {
    showTokenSelection(context, tokens, (token) {
      Navigator.of(context).pushNamed(
        Routes.walletSend.name,
        arguments: Routes.walletSend.d(token: token),
      );
    });
  }

  // 显示地址接收二维码对话框
  void _showReceiveDialog(BuildContext context, String address) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Column(
        spacing: 30.0,
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Receive',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  icon: const Icon(Icons.close, color: Colors.black, size: 28),
                  onPressed: () => Navigator.pop(context),
                ),
              ],
            ),
          ),
          Expanded(
            child: FittedBox(
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(color: Colors.grey.shade200, width: 1),
                ),
                child: QrImageView(
                  data: address,
                  version: QrVersions.auto,
                  size: 250,
                ),
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 50, vertical: 10),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    address,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 2,
                  ),
                ),
                Tapper(
                  onTap: () {
                    Clipboard.setData(ClipboardData(text: address));
                    Card3ToastUtil.showToast(message: ToastMessages.copied);
                  },
                  child: Container(
                    width: 44,
                    height: 44,
                    decoration: BoxDecoration(
                      color: ColorName.themeColorDark,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: const Icon(
                      Icons.copy_outlined,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                ),
              ],
            ),
          ),
          const Gap.v(50.0),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final network = ref.watch(networkProvider);
    final walletAddress = ref.watch(walletAddressProvider);
    final List<IToken> tokens = ref.watch(fetchWalletPortfolioOKXProvider(network: network)).valueOrNull?.tokens ?? [];

    return Row(
      spacing: 10.0,
      children: [
        _buildActionButton(
          context,
          icon: Assets.icons.receive.svg(width: 30, height: 30),
          label: 'Receive',
          onTap: () {
            if (walletAddress == null || walletAddress.isEmpty) {
              return;
            }
            // 显示接收地址二维码
            _showReceiveDialog(context, walletAddress);
          },
        ),
        // _buildActionButton(
        //   context,
        //   icon: Assets.icons.swap.svg(width: 30, height: 30),
        //   label: 'Swap',
        //   onTap: () async {
        //     // 处理交换操作
        //   },
        // ),
        _buildActionButton(
          context,
          icon: Assets.icons.send.svg(width: 30, height: 30),
          label: 'Send',
          onTap: () => _handleSendButtonTap(context, network, tokens),
        ),
      ],
    );
  }

  String getBypassUrl(String originalUrl) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final uri = Uri.parse(originalUrl);

    // 添加随机参数绕过深度链接匹配
    return uri
        .replace(
          queryParameters: {
            ...uri.queryParameters,
            'bypass_${timestamp.toString()}': '1',
            'source': 'external_launch',
            'ref': 'direct_open',
          },
        )
        .toString();
  }

  // 单个操作按钮
  Widget _buildActionButton(
    BuildContext context, {
    required Widget icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return Expanded(
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 14),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            color: context.themeColor,
          ),
          child: Column(
            spacing: 5.0,
            mainAxisSize: MainAxisSize.min,
            children: [
              icon,
              Text(
                label,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

final _tokenItemProvider = Provider.autoDispose<OKXWalletProfileToken>(
  (ref) => throw UnimplementedError(),
);
final _refreshKey = Provider.autoDispose<GlobalKey<RefreshIndicatorState>>(
  (ref) => GlobalKey(),
);

class _TokenList extends ConsumerWidget {
  const _TokenList();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final network = ref.watch(networkProvider);
    final result = ref.watch(fetchWalletPortfolioOKXProvider(network: network));
    if (result.hasError && !result.isLoading) {
      if (result.error is! ApiException) {
        ref.read(_refreshKey).currentState?.show();
      }
    }
    return result.when(
      data: (data) {
        if (data.tokens.isEmpty) {
          return SliverEmptyView(
            onTap: () => _WalletState._onRefresh(ref),
            message: context.l10n.textWalletListEmpty,
          );
        }
        return DecoratedSliver(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            color: context.theme.cardColor,
          ),
          sliver: SliverList.separated(
            separatorBuilder: (_, _) => Gap.v(6.0, color: context.theme.scaffoldBackgroundColor),
            itemCount: data.tokens.length,
            itemBuilder: (_, index) {
              final item = data.tokens[index];
              return ProviderScope(
                overrides: [_tokenItemProvider.overrideWithValue(item)],
                child: const _TokenItem(),
              );
            },
          ),
        );
      },
      loading: () => DecoratedSliver(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          color: context.theme.cardColor,
        ),
        sliver: SliverList.separated(
          separatorBuilder: (_, _) => Gap.v(4.0, color: context.theme.scaffoldBackgroundColor),
          itemCount: 5,
          itemBuilder: (_, _) => const _TokenItemShimmer(),
        ),
      ),
      error: (e, s) => SliverEmptyView(
        onTap: () => _WalletState._onRefresh(ref),
        message: isNetworkError(e) ? context.l10nME.networkError : '$e\n${context.l10nME.clickToRetryButton}',
      ),
    );
  }
}

class _TokenItem extends ConsumerWidget {
  const _TokenItem();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final token = ref.watch(_tokenItemProvider);
    final network = ref.watch(networkProvider);
    final tokenBalance = ref.watch(
      tokenBalanceStreamProvider(network: network, address: token.address),
    );
    final price = Decimal.parse('${token.priceUsd}');
    final valueUsd = price * (tokenBalance.valueOrNull?.realBalance ?? token.realBalance);
    return Container(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        spacing: 12.0,
        children: [
          // 代币图标
          if (token.logo.isNotEmpty)
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
              ),
              child: MEImage(
                token.logo,
                clipOval: true,
                fit: BoxFit.cover,
                alternativeSVG: true,
              ),
            )
          else
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: Colors.deepPurple[200],
                borderRadius: BorderRadius.circular(20),
              ),
              child: Center(
                child: Text(
                  token.symbol.substring(0, 1).toUpperCase(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                  ),
                ),
              ),
            ),

          // 代币名称和符号
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  token.symbol,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),

          // 代币数量及价值
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                tokenBalance.valueOrNull?.realBalance.toNumerical(fractionDigits: 4) ??
                    token.realBalance.toNumerical(fractionDigits: 4),
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text.rich(
                TextSpan(
                  children: [
                    const TextSpan(text: r'$'),
                    TextSpan(text: valueUsd.toNumerical()),
                  ],
                ),
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[400],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class _TokenItemShimmer extends StatelessWidget {
  const _TokenItemShimmer();

  @override
  Widget build(BuildContext context) {
    return MEShimmer(
      child: Container(
        height: 72.0,
        padding: const EdgeInsets.all(16.0),
        child: Row(
          spacing: 12.0,
          children: [
            AspectRatio(
              aspectRatio: 1.0,
              child: Container(
                decoration: BoxDecoration(
                  color: context.themeColor,
                  shape: BoxShape.circle,
                ),
              ),
            ),
            Expanded(
              child: Row(
                spacing: 12.0,
                children: [
                  Expanded(
                    flex: 5,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Gap.h(
                          86.0,
                          height: 16.0,
                          color: context.theme.cardColor,
                        ),
                        const Gap.v(4.0),
                        Gap.h(
                          56.0,
                          height: 12.0,
                          color: context.theme.cardColor,
                        ),
                      ],
                    ),
                  ),
                  Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Gap.h(
                        56.0,
                        height: 16.0,
                        color: context.theme.cardColor,
                      ),
                      const Gap.v(4.0),
                      Gap.h(
                        36.0,
                        height: 12.0,
                        color: context.theme.cardColor,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
