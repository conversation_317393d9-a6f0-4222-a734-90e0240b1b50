import 'dart:ui' as ui;

import 'package:card3/exports.dart';
import 'package:flutter/material.dart';
import 'package:sliver_tools/sliver_tools.dart';

import '/models/business.dart' show EventItem;
import '/provider/business.dart';
import '/provider/card.dart';
import '/provider/user.dart';

// 分组数据结构
@immutable
class EventCategory {
  const EventCategory({
    required this.title,
    required this.children,
  });

  final String title;
  final List<EventItem> children;

  @override
  String toString() {
    return 'EventCategory(title: $title, children: $children)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) {
      return true;
    }
    return other is EventCategory && other.title == title && other.children == children;
  }

  @override
  int get hashCode => Object.hash(title, children);
}

class Fun extends StatelessWidget {
  const Fun({super.key});

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: themeBy(
        meTheme: defaultMEThemeDark,
        locale: Localizations.localeOf(context),
      ),
      child: const BrightnessLayer(
        brightness: Brightness.dark,
        child: _MainBody(),
      ),
    );
  }
}

class _MainBody extends ConsumerWidget {
  const _MainBody();

  /// 按category分组events并排序
  List<EventCategory> _groupEventsByCategory(List<EventItem> events) {
    // 按category分组
    final Map<String, List<EventItem>> grouped = {};

    for (final event in events) {
      final category = event.eventType.or('Other');
      grouped.putIfAbsent(category, () => []).add(event);
    }

    // 创建分组对象并对每个分组内的项目排序
    final List<EventCategory> categories = grouped.entries.map<EventCategory>((entry) {
      final List<EventItem> sortedChildren = List<EventItem>.from(entry.value);
      // 按sort字段从小到大排序
      sortedChildren.sort((a, b) => (a.sort ?? 0).compareTo(b.sort ?? 0));

      return EventCategory(title: entry.key, children: sortedChildren);
    }).toList();

    // 可以根据需要对category本身进行排序
    categories.sort((a, b) => a.title.compareTo(b.title));

    return categories;
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final events = ref.watch(configProvider).events;
    final cards = ref.watch(fetchMyCardsProvider);
    final cardEventIds = (cards.valueOrNull ?? []).map((c) => c.card3EventId).toSet();
    final filteredEvents = events.where((event) {
      final eventIds = event.eventIds;
      return eventIds.isNotEmpty ? eventIds.any((id) => cardEventIds.contains(id)) : true;
    }).toList();

    // 按category分组并创建二维数组结构
    final groupedEvents = _groupEventsByCategory(filteredEvents);

    return Scaffold(
      body: RefreshIndicator(
        displacement: MediaQuery.paddingOf(context).top,
        onRefresh: () async {
          ref.invalidate(fetchUserInfoProvider);
          ref.invalidate(fetchMyCardsProvider);
          await Future.wait([
            ref.read(fetchUserInfoProvider().future),
            ref.read(fetchMyCardsProvider.future),
            ref.read(configStateProvider.notifier).loadConfig(),
          ]);
        },
        child: CustomScrollView(
          slivers: [
            const SliverToBoxAdapter(child: _PointsCard()),
            ...groupedEvents.map((category) {
              return MultiSliver(
                pushPinnedChildren: true,
                children: [
                  SliverToBoxAdapter(
                    child: Padding(
                      padding: const EdgeInsets.fromLTRB(16, 24, 16, 0),
                      child: Text(
                        category.title,
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                  const SliverGap.v(8.0),
                  SliverPadding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    sliver: SliverGrid(
                      delegate: SliverChildBuilderDelegate(
                        (context, index) {
                          final event = category.children[index];
                          return _buildFeatureCard(
                            context,
                            title: event.title,
                            subtitle: event.desc,
                            onTap: () {
                              if (event.isNative) {
                                if (event.title == 'Connections') {
                                  meNavigator.pushNamed(Routes.funConnection.name);
                                } else if (event.title == 'Referral') {
                                  meNavigator.pushNamed(Routes.funReferral.name);
                                }
                              } else {
                                meNavigator.pushNamed(
                                  Routes.webview.name,
                                  arguments: Routes.webview.d(
                                    url: '$envUrlCard3${event.link}',
                                    title: event.title,
                                  ),
                                );
                              }
                            },
                            icon: Container(
                              width: 56,
                              height: 56,
                              decoration: const BoxDecoration(
                                shape: BoxShape.circle,
                              ),
                              child: event.blendMode
                                  ? BackdropFilter(
                                      filter: ui.ImageFilter.blur(sigmaX: 0, sigmaY: 0),
                                      blendMode: BlendMode.lighten,
                                      child: MEImage(
                                        event.image,
                                        fit: BoxFit.contain,
                                        height: 56,
                                      ),
                                    )
                                  : MEImage(
                                      event.image,
                                      fit: BoxFit.contain,
                                      height: 56,
                                    ),
                            ),
                          );
                        },
                        childCount: category.children.length,
                      ),
                      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 2,
                        crossAxisSpacing: 8,
                        mainAxisSpacing: 8,
                        childAspectRatio: 1.3,
                      ),
                    ),
                  ),
                ],
              );
            }),
            const SliverGap.v(24.0),
          ],
        ),
      ),
    );
  }

  // 构建功能卡片的辅助方法
  Widget _buildFeatureCard(
    BuildContext context, {
    required String title,
    required String subtitle,
    required Widget icon,
    VoidCallback? onTap,
  }) {
    return RippleTap(
      onTap: onTap,
      constraints: const BoxConstraints(minHeight: 140),
      padding: const EdgeInsets.all(12),
      borderRadius: BorderRadius.circular(16),
      color: context.theme.cardColor,
      child: Column(
        spacing: 8.0,
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            spacing: 4.0,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              FittedBox(
                fit: BoxFit.scaleDown,
                child: Text(
                  title,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ),
              Text(
                subtitle,
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
              ),
            ],
          ),
          // 使用Align替代Row，确保图标总是在右下角
          Expanded(
            child: Align(
              alignment: AlignmentDirectional.bottomEnd,
              child: icon,
            ),
          ),
        ],
      ),
    );
  }
}

class _PointsCard extends ConsumerWidget {
  const _PointsCard();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final localUser = ref.watch(userRepoProvider);
    final userInfoResult = ref.watch(fetchUserInfoProvider());
    final userInfo = userInfoResult.valueOrNull ?? localUser;
    return Tapper(
      onTap: () => Navigator.of(context).pushNamed(Routes.funPointRecord.name),
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          Positioned(
            left: -50.0,
            bottom: 50.0,
            child: ImageFiltered(
              imageFilter: ui.ImageFilter.blur(sigmaX: 140.0, sigmaY: 100.0),
              child: Opacity(
                opacity: 0.60,
                child: Container(
                  width: 800,
                  height: 371,
                  decoration: const ShapeDecoration(
                    gradient: LinearGradient(
                      colors: [Color(0xFFCC58FF), Color(0xFF8858FF)],
                      stops: [0.25, 0.5],
                    ),
                    shape: OvalBorder(),
                  ),
                ),
              ),
            ),
          ),
          Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Gap.topPadding(context),
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 16.0).copyWith(top: 4.0),
                padding: const EdgeInsets.all(5.0),
                alignment: AlignmentDirectional.centerEnd,
                child: const Icon(Icons.history, size: 30),
              ),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16.0).copyWith(top: 0.0),
                child: Column(
                  spacing: 4.0,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Text(
                      'Points',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: Colors.grey,
                        fontSize: 24,
                      ),
                    ),
                    Text(
                      userInfo?.integral.toString() ?? '---',
                      style: const TextStyle(
                        fontSize: 48,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
              const Gap.v(40.0),
            ],
          ),
        ],
      ),
    );
  }
}

// 添加AnimatedAppLogo组件
class _AnimatedAppLogo extends StatefulWidget {
  const _AnimatedAppLogo();

  @override
  State<_AnimatedAppLogo> createState() => _AnimatedAppLogoState();
}

class _AnimatedAppLogoState extends State<_AnimatedAppLogo> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 5),
    )..repeat(reverse: true); // 反向重复播放动画

    // 创建一个从0.5到1.0的透明度动画
    _opacityAnimation = Tween<double>(begin: 0.7, end: 1.0).animate(_controller);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Opacity(
          opacity: _opacityAnimation.value,
          child: child,
        );
      },
      child: Assets.icons.headerFun.svg(width: 413.0, fit: BoxFit.cover),
    );
  }
}
