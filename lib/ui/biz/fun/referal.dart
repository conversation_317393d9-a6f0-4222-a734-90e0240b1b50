import 'package:card3/exports.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:qr_flutter/qr_flutter.dart';

import '/models/business.dart';
import '/provider/api.dart';
import '/provider/user.dart';

final _listProvider = FutureProvider.autoDispose.family<Paged<ReferralLog>, int>((ref, page) async {
  final ct = ref.cancelToken();
  ref.onDispose(() {
    ct.cancel();
  });
  final apiService = ref.read(apiServiceProvider);
  final logsPage = await apiService.getReferralLogs(
    pageNum: page,
    pageSize: 20,
    cancelToken: ct,
  );
  return logsPage;
});

@FFRoute(name: '/fun/referral')
class ReferralPage extends ConsumerStatefulWidget {
  const ReferralPage({super.key});

  @override
  ConsumerState<ReferralPage> createState() => _ReferralPageState();
}

class _ReferralPageState extends ConsumerState<ReferralPage> {
  Future<void> _refresh() async {
    ref.invalidate(_listProvider);
    await ref.read(_listProvider(1).future);
  }

  @override
  Widget build(BuildContext context) {
    final totalResult = ref.watch(_listProvider(1));
    final Widget child;
    if (totalResult.valueOrNull?.total == 0) {
      child = SliverEmptyView(
        onTap: _refresh,
        icon: Assets.icons.images.ad1.image(width: 84.0),
      );
    } else if (totalResult.hasError && !totalResult.isLoading) {
      child = SliverEmptyView(
        onTap: _refresh,
        icon: Padding(
          padding: const EdgeInsets.symmetric(vertical: 16.0),
          child: Assets.icons.images.ad1.image(width: 84.0),
        ),
        message: context.l10nME.networkError,
      );
    } else {
      child = SliverList.builder(
        itemCount: totalResult.valueOrNull?.total ?? 4,
        itemBuilder: (context, index) {
          final page = index ~/ 20 + 1;
          final indexInPage = index % 20;
          final result = ref.watch(_listProvider(page));
          return result.maybeWhen(
            data: (data) {
              if (indexInPage >= data.list.length) {
                return null;
              }
              final item = data.list[indexInPage];
              return ProviderScope(
                overrides: [_itemProvider.overrideWithValue(item)],
                child: const _ReferralItem(),
              );
            },
            orElse: () => const _ReferralItemShimmer(),
          );
        },
      );
    }
    return Theme(
      data: themeBy(
        meTheme: defaultMEThemeDark,
        locale: Localizations.localeOf(context),
      ),
      child: AppScaffold(
        body: RefreshIndicator(
          onRefresh: _refresh,
          child: CustomScrollView(
            slivers: [
              const SliverToBoxAdapter(child: _Header()),
              child,
            ],
          ),
        ),
      ),
    );
  }
}

class _Header extends ConsumerWidget {
  const _Header();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final localUser = ref.watch(userRepoProvider);
    final userInfoResult = ref.watch(fetchUserInfoProvider());
    final userInfo = userInfoResult.valueOrNull ?? localUser;
    final link = '$envUrlCard3?referral=${userInfo?.referralCode ?? ''}';

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        const Text(
          'My Referral Link',
          style: TextStyle(
            color: Colors.grey,
            fontSize: 24,
            fontWeight: FontWeight.w500,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 16),
        Text(
          userInfo?.referralCode ?? '',
          style: const TextStyle(
            color: Colors.white,
            fontSize: 36,
            fontWeight: FontWeight.bold,
            letterSpacing: 1.5,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 24),
        Row(
          children: [
            Expanded(
              child: RippleTap(
                onTap: () {
                  Clipboard.setData(ClipboardData(text: link));
                  Card3ToastUtil.showToast(message: ToastMessages.referralLinkCopied);
                },
                padding: const EdgeInsets.symmetric(vertical: 12),
                color: context.meTheme.listColor,
                borderRadius: BorderRadius.circular(12),
                child: Row(
                  spacing: 6.0,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      'Copy Link',
                      style: TextStyle(
                        color: context.meTheme.notificationColor,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Icon(
                      Icons.copy_rounded,
                      color: context.meTheme.notificationColor,
                      size: 20.0,
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: RippleTap(
                onTap: () => _showQRCodeDialog(context, link),
                padding: const EdgeInsets.symmetric(vertical: 12),
                color: context.meTheme.listColor,
                borderRadius: BorderRadius.circular(12),
                child: Row(
                  spacing: 6.0,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      'QR Code',
                      style: TextStyle(
                        color: context.meTheme.notificationColor,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Icon(
                      Icons.qr_code,
                      color: context.meTheme.notificationColor,
                      size: 20.0,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 48),
        const Text(
          'Referred',
          style: TextStyle(
            color: Colors.white,
            fontSize: 28,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
      ],
    );
  }

  // 显示二维码弹窗
  void _showQRCodeDialog(BuildContext context, String link) {
    showModalBottomSheet(
      context: context,
      backgroundColor: ColorName.cardColorDark,
      clipBehavior: Clip.antiAlias,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      isScrollControlled: true,
      builder: (context) => Stack(
        alignment: Alignment.center,
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(20, 40, 20, 40),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text(
                  'My Referral Link',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 32),
                Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  padding: const EdgeInsets.all(16),
                  child: QrImageView(
                    data: link,
                    version: QrVersions.auto,
                    size: 200.0,
                    backgroundColor: Colors.white,
                  ),
                ),
                const SizedBox(height: 24),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Expanded(
                      child: Text(
                        link,
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.grey,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    IconButton(
                      onPressed: () {
                        Clipboard.setData(ClipboardData(text: link));
                        Card3ToastUtil.showToast(message: ToastMessages.urlCopied);
                      },
                      icon: const Icon(
                        Icons.copy_rounded,
                        color: Colors.amber,
                        size: 24,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          // 关闭按钮
          Positioned(
            right: 8,
            top: 8,
            child: IconButton(
              onPressed: () => Navigator.pop(context),
              icon: const Icon(
                Icons.close,
                color: Colors.white,
                size: 28,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

final _itemProvider = Provider.autoDispose<ReferralLog>((ref) => throw UnimplementedError());

class _ReferralItem extends ConsumerWidget {
  const _ReferralItem();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final item = ref.watch(_itemProvider);
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        color: context.theme.cardColor,
      ),
      child: Row(
        children: [
          // 邮箱和日期
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item.inviteeEmail.or('Unknown User'),
                  style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Text(
                  item.createTime.withDateTimeFormat(format: 'yyyy-MM-dd HH:mm'),
                  style: context.textTheme.bodySmall?.copyWith(fontSize: 14.0),
                ),
              ],
            ),
          ),
          Text(
            '+${item.integral}',
            style: const TextStyle(
              color: Colors.amber,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}

class _ReferralItemShimmer extends StatelessWidget {
  const _ReferralItemShimmer();

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(19),
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.circular(16),
      ),
      child: MEShimmer(
        child: Row(
          children: [
            Expanded(
              child: Column(
                spacing: 4.0,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: 140,
                    height: 16,
                    color: context.theme.cardColor,
                  ),
                  Container(
                    width: 80,
                    height: 14,
                    color: context.theme.cardColor,
                  ),
                ],
              ),
            ),
            Container(
              width: 60,
              height: 24,
              color: context.theme.cardColor,
            ),
          ],
        ),
      ),
    );
  }
}
