import 'package:card3/exports.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '/feat/link/helper.dart';
import '/provider/settings.dart';
import 'home/card.dart';
import 'home/fun.dart';
import 'home/wallet.dart';

final drawerGlobalKeyProvider = Provider<GlobalKey<ScaffoldState>>((ref) {
  return GlobalKey<ScaffoldState>();
});

@FFRoute(name: '/home')
class HomePage extends ConsumerStatefulWidget {
  const HomePage({super.key});

  @override
  ConsumerState<HomePage> createState() => _HomePageState();
}

class _HomePageState extends ConsumerState<HomePage> with WidgetsBindingObserver {
  String? _clipboardText;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed) {
      WidgetsBinding.instance.addPostFrameCallback((_) async {
        _handleClipboardData();
      });
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  Future<void> _handleClipboardData() async {
    if (!mounted) {
      return;
    }
    if (!ref.read(settingsProvider).autoParseFromClipboard) {
      return;
    }
    final data = await Clipboard.getData('text/plain');
    final text = data?.text?.trim();

    if (text == _clipboardText) {
      return;
    }
    _clipboardText = text;

    if (text == null || text.isEmpty) {
      return;
    }
    if (Uri.tryParse(text) case final uri?) {
      AppLinkHelper.handleUri(uri);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: ref.watch(drawerGlobalKeyProvider),
      body: const _MainBody(),
      bottomNavigationBar: const _BottomNavBar(),
    );
  }
}

class _MainBody extends ConsumerWidget {
  const _MainBody();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final index = ref.watch(selectedIndexProvider);
    final children = [
      const Home(),
      const Fun(),
      const Wallet(),
    ];
    return IndexedStack(
      index: index,
      children: children,
    );
  }
}

class _NavItem {
  const _NavItem({
    required this.icon,
    required this.label,
    this.selectedIcon,
  });

  final SvgGenImage icon;
  final String label;
  final SvgGenImage? selectedIcon;
}

class _BottomNavBar extends ConsumerWidget {
  const _BottomNavBar();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selected = ref.watch(selectedIndexProvider);
    final navs = [
      _NavItem(
        icon: Assets.icons.navCard,
        label: context.l10n.labelNavCards,
      ),
      _NavItem(
        icon: Assets.icons.navFun,
        label: context.l10n.labelNavRewards,
        selectedIcon: Assets.icons.navFunSelected,
      ),
      _NavItem(
        icon: Assets.icons.navWallet,
        label: context.l10n.labelNavWallet,
      ),
    ];

    final selectedMiddle = selected == 1;
    final bottomPadding = MediaQuery.paddingOf(context).bottom.max(4.0);
    return Material(
      elevation: selectedMiddle ? 5 : 1,
      shadowColor: selectedMiddle ? ColorName.cardColorDark : Colors.black.withValues(alpha: 0.5),
      color: selectedMiddle ? ColorName.cardColorDark : Colors.white,
      child: Container(
        height: 64 + bottomPadding + 8,
        padding: EdgeInsets.only(top: 4.0, bottom: bottomPadding + 4),
        decoration: BoxDecoration(
          border: selectedMiddle ? null : Border(top: BorderSide(color: context.theme.dividerColor)),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: navs.mapIndexed((index, nav) => _buildNavItem(context, ref, nav, index, selected)).toList(),
        ),
      ),
    );
  }

  // 构建普通导航项
  Widget _buildNavItem(
    BuildContext context,
    WidgetRef ref,
    _NavItem item,
    int index,
    int selected,
  ) {
    final isSelected = index == selected;
    return Tapper(
      onTap: () {
        ref.read(selectedIndexProvider.notifier).state = index;
      },
      behavior: HitTestBehavior.opaque,
      child: Column(
        spacing: 2.0,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          if (item.selectedIcon case final icon? when isSelected)
            icon.svg(width: 30, height: 30)
          else
            item.icon.svg(
              width: 30,
              height: 30,
              colorFilter: isSelected ? context.themeColor.filter : null,
            ),
          Text(
            item.label,
            style: context.textTheme.bodySmall,
          ),
        ],
      ),
    );
  }
}
