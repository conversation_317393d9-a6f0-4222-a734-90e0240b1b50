import 'package:card3/exports.dart';
import 'package:flutter/material.dart';

import '/models/business.dart';
import '/models/user.dart' show UserFromRelationType;
import '/provider/api.dart';
import '/provider/chain.dart' show chainManagerProvider;
import '/provider/settings.dart';

final _listProvider = FutureProvider.autoDispose.family<Paged<Message>, int>((ref, page) {
  final ct = ref.cancelToken();
  ref.onDispose(() {
    ct.cancel();
  });
  return ref.read(apiServiceProvider).listMessages(page: page, cancelToken: ct);
});

@FFRoute(name: '/notification')
class NotificationPage extends ConsumerStatefulWidget {
  const NotificationPage({super.key});

  @override
  ConsumerState<NotificationPage> createState() => _NotificationPageState();
}

class _NotificationPageState extends ConsumerState<NotificationPage> {
  Future<void> _onRefresh() async {
    ref.invalidate(_listProvider);
    await ref.read(_listProvider(1).future);
  }

  @override
  Widget build(BuildContext context) {
    final totalResult = ref.watch(_listProvider(1));
    final Widget child;
    if (totalResult.valueOrNull?.total == 0) {
      child = RefreshableEmptyView(
        onTap: _onRefresh,
        message: 'No notifications yet.',
      );
    } else if (totalResult.hasError && !totalResult.isLoading) {
      final e = totalResult.error;
      child = RefreshableEmptyView(
        onTap: _onRefresh,
        message: isNetworkError(e) ? context.l10nME.networkError : '$e\n${context.l10nME.clickToRetryButton}',
      );
    } else {
      const size = 20;
      child = ListView.builder(
        padding: const EdgeInsets.symmetric(vertical: 14.0),
        itemCount: totalResult.valueOrNull?.total ?? 4,
        itemBuilder: (context, index) {
          final page = index ~/ size + 1;
          final indexInPage = index % size;
          final result = ref.watch(_listProvider(page));
          return result.maybeWhen(
            data: (data) {
              if (indexInPage >= data.list.length) {
                return null;
              }
              final item = data.list[indexInPage];
              return ProviderScope(
                overrides: [
                  _itemProvider.overrideWithValue(item),
                ],
                child: const _MessageItem(),
              );
            },
            orElse: () => const _MessageItemShimmer(),
          );
        },
      );
    }
    return AppScaffold(
      title: 'Notifications',
      body: RefreshIndicator(onRefresh: _onRefresh, child: child),
    );
  }
}

final _itemProvider = Provider.autoDispose<Message>((ref) => throw UnimplementedError());

class _MessageItem extends ConsumerWidget {
  const _MessageItem();

  void _switchNetwork(BuildContext context, WidgetRef ref, Network network) {
    AppLoading.run(() => ref.read(chainManagerProvider).switchNetwork(network));
  }

  // 从通知消息中提取内容
  Map<String, String> extractBracketContent(String content) {
    final RegExp bracketRegex = RegExp(r'\[(.*?)\]');
    final match = bracketRegex.firstMatch(content);
    String? extractedContent;
    String cleanedText = content;

    if (match != null) {
      extractedContent = match.group(1);
      cleanedText = content.replaceAll(bracketRegex, '').trim();
    }

    return {
      'extractedContent': extractedContent ?? '',
      'cleanedText': cleanedText,
    };
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final message = ref.watch(_itemProvider);
    return RippleTap(
      onTap: () {
        if (message.messageType == MessageType.sayHi) {
          meNavigator.pushNamed(
            Routes.funConnection.name,
            arguments: Routes.funConnection.d(type: UserFromRelationType.follower),
          );
        }
        if (message.messageType == MessageType.follow) {
          final params = MessageSayHiParams.fromJson(jsonDecode(message.params));
          meNavigator.pushNamed(
            Routes.socialProfile.name,
            arguments: Routes.socialProfile.d(code: params.referralCode),
          );
        }
        if (message.messageType == MessageType.airdrop) {
          ref.read(selectedIndexProvider.notifier).state = 2;

          // 解析 JSON 字符串
          final params = jsonDecode(message.params);
          final chainId = params['chainId'];

          final networks = BoxService.getNetworksFromLocal();
          // 从 networks 中找到对应的 Network
          final network = networks.firstWhere(
            (network) => network.id == chainId,
            orElse: () => networks.first,
          );
          _switchNetwork(context, ref, network);
          meNavigator.popAndPushNamed(Routes.home.name);
          return;
        }
        if (message.messageType == MessageType.integral) {
          meNavigator.pushNamed(Routes.funPointRecord.name);
          return;
        }
      },
      margin: const EdgeInsets.only(bottom: 12),
      borderRadius: BorderRadius.circular(16),
      padding: const EdgeInsets.all(16),
      color: context.theme.cardColor,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _buildIcon(context, message.messageType),
              Text(
                message.createTime.withDateTimeFormat(format: 'yyyy-MM-dd HH:mm'),
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
          _buildContent(context, message),
        ],
      ),
    );
  }

  // 根据消息类型渲染不同的图标
  Widget _buildIcon(BuildContext context, MessageType messageType) {
    switch (messageType) {
      case MessageType.push:
        return const Icon(
          Icons.chat_outlined,
          size: 24,
          color: Colors.black,
        );
      case MessageType.sayHi:
        return const Icon(
          Icons.waving_hand_outlined,
          size: 24,
          color: Colors.black,
        );
      case MessageType.follow:
        return const Icon(
          Icons.supervised_user_circle_outlined,
          size: 24,
          color: Colors.black,
        );
      case MessageType.airdrop:
        return const Icon(
          Icons.paid_outlined,
          size: 24,
          color: Colors.black,
        );
      case MessageType.integral:
        return const Icon(
          Icons.redeem_outlined,
          size: 24,
          color: Colors.black,
        );
      default:
        return const Icon(
          Icons.notifications_outlined,
          size: 24,
          color: Colors.black,
        );
    }
  }

  // 根据消息类型渲染内容
  Widget _buildContent(BuildContext context, Message message) {
    final content = message.message;
    final primaryColor = context.themeColor;

    switch (message.messageType) {
      case MessageType.sayHi:
      case MessageType.follow:
        final params = MessageSayHiParams.fromJson(jsonDecode(message.params.or('{}')));
        return Padding(
          padding: const EdgeInsets.only(top: 12.0),
          child: Column(
            spacing: 16.0,
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                message.messageType == MessageType.sayHi ? 'Say HI from ${params.name}' : content,
                style: const TextStyle(fontSize: 16),
              ),
              SizedBox(
                height: 60.0,
                child: Row(
                  spacing: 10.0,
                  children: [
                    AspectRatio(
                      aspectRatio: 1.0,
                      child: MEImage(
                        params.avatar,
                        backgroundColor: context.theme.dividerColor,
                        clipOval: true,
                        emptyBuilder: (context) => CircleAvatar(
                          backgroundColor: context.theme.dividerColor,
                          child: FittedBox(
                            fit: BoxFit.scaleDown,
                            child: Text(
                              params.name.characters.firstOrNull?.toUpperCase() ?? '?',
                              style: context.textTheme.bodyMedium?.copyWith(
                                fontSize: 28.0,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                    Column(
                      spacing: 2.0,
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          params.name.or('?'),
                          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                        ),
                        Flexible(
                          child: Text(
                            params.title.or(params.company).or(params.email).or('?'),
                            style: context.textTheme.bodySmall,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      case MessageType.push:
        final extracted = extractBracketContent(content);
        return Padding(
          padding: const EdgeInsets.only(top: 8.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                extracted['cleanedText']!,
                style: const TextStyle(fontSize: 16),
              ),
              if (extracted['extractedContent']!.isNotEmpty)
                Row(
                  children: [
                    Text(
                      '${extracted['extractedContent']!.split('|')[0]} :',
                      style: const TextStyle(fontSize: 16),
                    ),
                    const SizedBox(width: 4),
                    GestureDetector(
                      onTap: () {
                        // 打开链接
                        // TODO: 实现链接跳转
                      },
                      child: Text(
                        extracted['extractedContent']!.split('|')[1],
                        style: TextStyle(
                          fontSize: 16,
                          color: primaryColor,
                          decoration: TextDecoration.underline,
                        ),
                      ),
                    ),
                  ],
                ),
            ],
          ),
        );

      case MessageType.airdrop:
        return Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            spacing: 16.0,
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(content, style: const TextStyle(fontSize: 16)),
              GestureDetector(
                child: Text(
                  'Check it out now',
                  style: TextStyle(
                    fontSize: 16,
                    color: primaryColor,
                    decoration: TextDecoration.underline,
                  ),
                ),
              ),
            ],
          ),
        );

      case MessageType.integral:
        return Padding(
          padding: const EdgeInsets.only(top: 12.0),
          child: Column(
            spacing: 16.0,
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(content, style: const TextStyle(fontSize: 16)),
              GestureDetector(
                child: Text(
                  'Check it out now',
                  style: TextStyle(
                    fontSize: 16,
                    color: primaryColor,
                    decoration: TextDecoration.underline,
                  ),
                ),
              ),
            ],
          ),
        );

      default:
        return Padding(
          padding: const EdgeInsets.only(top: 8.0),
          child: Text(content, style: const TextStyle(fontSize: 16)),
        );
    }
  }
}

class _MessageItemShimmer extends StatelessWidget {
  const _MessageItemShimmer();

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        color: context.theme.cardColor,
      ),
      child: MEShimmer(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          spacing: 10.0,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Icon(Icons.message, size: 24.0),
                Container(width: 100.0, height: 12.0, color: context.theme.cardColor),
              ],
            ),
            Container(
              width: 100.0,
              height: 14.0,
              color: context.theme.dividerColor,
            ),
          ],
        ),
      ),
    );
  }
}
