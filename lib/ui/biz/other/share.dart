import 'package:card3/exports.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:qr_flutter/qr_flutter.dart';

import '/provider/user.dart';

@FFRoute(name: '/share')
class SharePage extends ConsumerWidget {
  const SharePage({super.key, required this.cardCode});

  final String cardCode;

  String get _shareUrl => '$envUrlShort/$cardCode';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return AppScaffold(
      backgroundColor: Colors.grey[900],
      bodyPadding: const EdgeInsets.all(16.0),
      body: Column(
        spacing: 16.0,
        children: [
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // 欢迎信息
                const Text(
                  'Hey!',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 32,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  'let\'s connect',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 32,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 48),
                // 个人资料卡片
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(24),
                  ),
                  child: Column(
                    children: [
                      // 头像
                      Consumer(
                        builder: (context, ref, _) {
                          final localUser = ref.watch(userRepoProvider);
                          final userInfoResult = ref.watch(fetchUserInfoProvider());
                          final userInfo = userInfoResult.valueOrNull ?? localUser;
                          return Container(
                            width: 80,
                            height: 80,
                            decoration: BoxDecoration(
                              color: Colors.white,
                              shape: BoxShape.circle,
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.grey.withValues(alpha: 0.2),
                                  spreadRadius: 1,
                                  blurRadius: 3,
                                ),
                              ],
                            ),
                            child: ClipOval(
                              child: userInfo?.avatar.isNotEmpty == true
                                  ? MEImage(
                                      userInfo?.avatar ?? '',
                                      width: 80,
                                      height: 80,
                                      fit: BoxFit.cover,
                                    )
                                  : Icon(
                                      Icons.account_circle,
                                      size: 80,
                                      color: Colors.grey.shade400,
                                    ),
                            ),
                          );
                        },
                      ),
                      const SizedBox(height: 16),
                      // 用户名
                      Consumer(
                        builder: (context, ref, _) {
                          final localUser = ref.watch(userRepoProvider);
                          final userInfoResult = ref.watch(fetchUserInfoProvider());
                          final userInfo = userInfoResult.valueOrNull ?? localUser;
                          return Text(
                            userInfo?.name ?? '',
                            style: const TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                            ),
                          );
                        },
                      ),
                      const SizedBox(height: 40),
                      // 二维码
                      Container(
                        width: 200,
                        height: 200,
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey[300]!),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Center(
                          child: QrImageView(
                            data: _shareUrl,
                            version: QrVersions.auto,
                            size: 180,
                          ),
                        ),
                      ),
                      const SizedBox(height: 24),
                      // 链接
                      Tapper(
                        onTap: () {
                          Clipboard.setData(ClipboardData(text: _shareUrl));
                          Card3ToastUtil.showToast(message: ToastMessages.copied);
                        },
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 12,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.grey[200],
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Expanded(
                                child: Text(
                                  _shareUrl,
                                  style: TextStyle(
                                    color: Colors.grey[800],
                                    fontWeight: FontWeight.w500,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Icon(
                                Icons.copy,
                                size: 20,
                                color: Colors.grey[600],
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
