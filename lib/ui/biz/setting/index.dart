import 'package:card3/exports.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

@FFAutoImport()
import '/models/user.dart' show UserInfo;
import '/provider/card.dart';
import '/provider/user.dart';
import '/services/chain_manager.dart' show ChainManager;
import '/ui/widgets/social/profile/description.dart';

@FFRoute(name: '/setting')
class SettingIndex extends ConsumerWidget {
  const SettingIndex({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final localUser = ref.watch(userRepoProvider);
    final userInfoResult = ref.watch(fetchUserInfoProvider());
    final userInfo = userInfoResult.valueOrNull ?? localUser;
    final isETHCCMode = ref.watch(validateETHCCProfileProvider(validateProfile: false));

    return AppScaffold(
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 12),
                child: Column(
                  spacing: 8.0,
                  children: [
                    _buildProfileCard(context, userInfo),
                    const Gap.v(4.0),

                    // 设置选项
                    // _buildSettingItem(
                    //   context,
                    //   icon: Icons.link,
                    //   iconColor: Colors.deepPurple,
                    //   title: 'Linked Accounts',
                    //   onTap: () {
                    //     // 导航到关联账户页面
                    //   },
                    // ),
                    _buildSettingItem(
                      context,
                      icon: const Icon(
                        Icons.account_balance_wallet,
                        color: Colors.white,
                        size: 26,
                      ),
                      iconColor: ColorName.themeColorDark,
                      title: 'Wallets Management',
                      onTap: () {
                        // 导航到钱包管理页面
                        meNavigator.pushNamed(Routes.settingWallets.name);
                      },
                    ),
                    _buildSettingItem(
                      context,
                      icon: Center(
                        child: Assets.icons.setting.personShield.svg(
                          width: 24,
                          height: 24,
                          colorFilter: Colors.white.filter,
                        ),
                      ),

                      iconColor: ColorName.themeColorDark,
                      title: 'Account Security',
                      onTap: () {
                        // 导航到钱包管理页面
                        meNavigator.pushNamed(Routes.settingAccount.name);
                      },
                    ),
                    if (kDebugMode) ...[
                      _buildSettingItem(
                        context,
                        icon: Center(
                          child: Assets.icons.setting.personShield.svg(
                            width: 24,
                            height: 24,
                            colorFilter: Colors.white.filter,
                          ),
                        ),
                        iconColor: ColorName.themeColorDark,
                        title: 'Customize',
                        onTap: () {
                          // 导航到钱包管理页面
                          meNavigator.pushNamed(
                            Routes.customize.name,
                            arguments: Routes.customize.d(code: '7MI2dCmjXPr0JMn2'),
                          );
                        },
                      ),
                    ],
                    if (isETHCCMode == true) ...[
                      _buildSettingItem(
                        context,
                        icon: Center(
                          child: Assets.icons.setting.mode.svg(
                            width: 24,
                            height: 24,
                            colorFilter: Colors.white.filter,
                          ),
                        ),
                        iconColor: ColorName.themeColorDark,
                        title: 'Mode',
                        onTap: () {
                          meNavigator.pushNamed(Routes.settingMode.name);
                        },
                      ),
                    ],
                    if (kDebugMode) ...[
                      _buildSettingItem(
                        context,
                        icon: const Icon(
                          Icons.text_snippet,
                          color: Colors.white,
                          size: 24,
                        ),
                        iconColor: Colors.deepPurple,
                        title: 'Test Page',
                        onTap: () {
                          // 导航到通知设置页面
                          meNavigator.pushNamed(
                            Routes.webview.name,
                            arguments: Routes.webview.d(
                              url: 'https://test-v.card3.fun/test',
                              title: 'Test Page',
                            ),
                          );
                        },
                      ),
                    ],
                    _buildSettingItem(
                      context,
                      icon: const Icon(
                        Icons.help_outline,
                        color: Colors.white,
                        size: 24,
                      ),
                      iconWidget: Assets.icons.setting.about.svg(
                        width: 40,
                        height: 40,
                        colorFilter: Colors.grey.filter,
                      ),
                      iconColor: Colors.grey,
                      title: 'About Card3',
                      onTap: () {
                        // 导航到关于页面
                        meNavigator.pushNamed(Routes.settingAbout.name);
                      },
                    ),
                  ],
                ),
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 16),
            child: _buildLogout(context, ref),
          ),
        ],
      ),
    );
  }

  // 用户资料卡片
  Widget _buildProfileCard(BuildContext context, UserInfo? userInfo) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Stack(
        children: [
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                // 头像
                Container(
                  width: 100,
                  height: 100,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: userInfo?.avatar.isNotEmpty == true
                      ? ClipRRect(
                          borderRadius: BorderRadius.circular(16),
                          child: MEImage(
                            userInfo?.avatar ?? '',
                            fit: BoxFit.cover,
                            clipOval: false,
                            alternativeSVG: true,
                            errorBuilder: (context, error, stackTrace) => Icon(
                              Icons.person,
                              size: 60,
                              color: Colors.grey[400],
                            ),
                          ),
                        )
                      : Icon(
                          Icons.person,
                          size: 60,
                          color: Colors.grey[400],
                        ),
                ),
                const SizedBox(width: 20),
                // 用户信息
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        userInfo?.name ?? '',
                        style: const TextStyle(
                          fontSize: 26,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        switch (userInfo) {
                          final u? => u.userEmail.or(u.handle).or(''),
                          _ => '',
                        },
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey[500],
                        ),
                      ),
                    ],
                  ),
                ),
                // 编辑按钮
              ],
            ),
          ),
          Positioned(
            right: 0,
            child: IconButton(
              onPressed: () {
                // 打开带头像模式的Description组件进行编辑
                _showEditProfileDialog(context);
              },
              icon: Icon(
                Icons.edit,
                color: Colors.grey[700],
                size: 30,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 显示编辑个人资料对话框
  void _showEditProfileDialog(BuildContext context) {
    showModalBottomSheet(
      context: context,
      scrollControlDisabledMaxHeightRatio: 0.8,
      builder: (context) => const DescriptionActionSheet(withAvatar: true),
    );
  }

  // 设置选项项目
  Widget _buildSettingItem(
    BuildContext context, {
    required Widget icon,
    required Color iconColor,
    required String title,
    required VoidCallback onTap,
    Widget? iconWidget,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          child: Row(
            children: [
              if (iconWidget != null) iconWidget,
              if (iconWidget == null)
                // 图标背景
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: iconColor,
                    shape: BoxShape.circle,
                  ),
                  child: icon,
                ),
              const SizedBox(width: 16),
              // 标题
              Expanded(
                child: Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
              ),
              // 箭头
              Icon(
                Icons.arrow_forward_ios,
                color: Colors.grey[400],
                size: 20,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLogout(BuildContext context, WidgetRef ref) {
    return GestureDetector(
      onTap: () {
        ChainManager.resetInstance();
        privyClient.logout();
        ref.read(userRepoProvider.notifier).reset();
        meNavigator.removeNamedAndPushAndRemoveUntil(Routes.login.name);
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
        ),
        child: const Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'log out',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.red,
              ),
            ),
            SizedBox(width: 4),
            Icon(
              Icons.logout,
              color: Colors.red,
              size: 24,
            ),
          ],
        ),
      ),
    );
  }
}
