import 'package:card3/exports.dart';
import 'package:flutter/material.dart';

import '/models/business.dart';
import '/provider/chain.dart' show chainManagerProvider, networkProvider, networksProvider;

class SelectNetwork extends ConsumerWidget {
  const SelectNetwork({super.key});

  void _switchNetwork(
    BuildContext context,
    WidgetRef ref,
    Network network,
  ) {
    AppLoading.run(() async {
      await ref.read(chainManagerProvider).switchNetwork(network);
      Navigator.of(context).maybePop();
    });
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final network = ref.watch(networkProvider);
    final networks = ref.watch(networksProvider);
    return Padding(
      padding: const EdgeInsets.all(16).copyWith(bottom: 0.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题与关闭按钮
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Select Network',
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                ),
              ),
              IconButton(
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
                icon: const Icon(Icons.close, color: Colors.black, size: 28),
                onPressed: () => Navigator.pop(context),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // 网络列表
          Expanded(
            child: ListView.builder(
              itemCount: networks.length,
              itemBuilder: (context, index) {
                final item = networks[index];
                final isSelected = item.id == network.id;
                return RippleTap(
                  onTap: () => _switchNetwork(context, ref, item),
                  margin: const EdgeInsets.only(bottom: 8),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                  color: isSelected ? ColorName.themeColorDark.withValues(alpha: 0.1) : Colors.grey[100],
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                    side: isSelected ? BorderSide(color: context.themeColor) : BorderSide.none,
                  ),
                  child: Row(
                    children: [
                      // 网络图标
                      Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: MEImage(
                          item.iconUrl,
                          clipOval: true,
                          fit: BoxFit.cover,
                          alternativeSVG: true,
                          emptyBuilder: (context) => const Icon(
                            Icons.account_balance_wallet,
                            size: 24,
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),

                      // 网络名称和信息
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              item.name,
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            if (item.testnet)
                              Text(
                                'Testnet',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey[600],
                                ),
                              ),
                          ],
                        ),
                      ),

                      // 选中标记
                      if (isSelected)
                        const Icon(
                          Icons.check_circle,
                          color: ColorName.themeColorDark,
                          size: 30,
                        ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
