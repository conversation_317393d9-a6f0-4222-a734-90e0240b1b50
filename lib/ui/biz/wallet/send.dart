import 'package:card3/exports.dart';
import 'package:flutter/material.dart';

@FFAutoImport()
import '/models/business.dart';
import '/provider/chain.dart';
import '/provider/token.dart';
import '/services/select_token.dart';

@FFRoute(name: '/wallet/send')
class SendPage extends ConsumerStatefulWidget {
  const SendPage({
    super.key,
    required this.token,
  });

  final IToken token;

  @override
  ConsumerState<SendPage> createState() => _SendPageState();
}

class _SendPageState extends ConsumerState<SendPage> {
  late IToken _selectedToken = widget.token;

  final _addressController = TextEditingController();
  final _amountController = TextEditingController();

  String _errorMessage = '';
  String? _txHash;
  bool _showSuccessView = false;

  @override
  void dispose() {
    _addressController.dispose();
    _amountController.dispose();
    super.dispose();
  }

  Future<void> _handleSend() async {
    // 清除之前的错误
    setState(() {
      _errorMessage = '';
    });

    final address = _addressController.text.trim();
    if (address.isEmpty) {
      setState(() {
        _errorMessage = 'Please enter the receiving address';
      });
      return;
    }
    final amount = Decimal.tryParse(_amountController.text);
    if (amount == null || amount <= Decimal.zero) {
      setState(() {
        _errorMessage = 'Please enter a valid amount';
      });
      return;
    }

    return AppLoading.run(() async {
      try {
        await _sendTransaction(address, amount);
        _showSuccessView = true;
      } catch (e) {
        _errorMessage = e.toString();
      } finally {
        safeSetState(() {});
      }
    });
  }

  Future<void> _sendTransaction(String address, Decimal amount) async {
    try {
      // 获取当前选择的网络
      final currentNetwork = ref.watch(networkProvider);
      final chainManager = ref.read(chainManagerProvider);
      final token = _selectedToken;

      // 检查ChainManager的网络和当前选择的网络是否一致
      if (chainManager.currentNetwork.id != currentNetwork.id) {
        LogUtil.d('检测到网络不一致，尝试同步网络状态...');
        LogUtil.d(
          'ChainManager网络ID: ${chainManager.currentNetwork.id}, '
          'Provider网络ID: ${currentNetwork.id}',
        );
        await ref.read(chainManagerProvider).switchNetwork(currentNetwork);
      }

      final amountAfterShifted = amount.shift(token.decimals).toBigInt();
      _txHash = await chainManager.sendTransaction(
        token: token,
        to: address,
        value: amountAfterShifted,
      );
      refreshTokenBalances(ref);
    } catch (e) {
      safeSetState(() {
        _errorMessage = 'Transaction failed: ${e.toString()}';
      });
      rethrow;
    }
  }

  void _checkStatus() {
    if (_txHash != null && _txHash!.isNotEmpty) {
      // 这里可以跳转到区块浏览器查看交易状态
      // 获取当前网络
      final currentNetwork = ref.read(networkProvider);
      if (currentNetwork.blockExplorers.isNotEmpty) {
        final explorerUrl = '${currentNetwork.blockExplorers.first.url}/tx/$_txHash';
        LogUtil.d('explorerUrl: $explorerUrl');
        // 打开浏览器
        launchUrlString(explorerUrl);
      }
    }
  }

  void _succeedAndBackToWallet() {
    refreshTokenBalances(ref);
    Navigator.of(context).popUntil((route) => route.isFirst);
  }

  @override
  Widget build(BuildContext context) {
    final network = ref.watch(networkProvider);
    final selectedToken = _selectedToken;
    final tokenBalance = ref.watch(
      tokenBalanceStreamProvider(network: network, address: selectedToken.address),
    );
    final displayBalance =
        tokenBalance.valueOrNull?.realBalance.toNumerical(fractionDigits: 4) ??
        selectedToken.realBalance.toNumerical(fractionDigits: 4);
    final List<IToken> tokens = ref.watch(fetchWalletPortfolioOKXProvider(network: network)).valueOrNull?.tokens ?? [];
    if (_showSuccessView) {
      return _buildSuccessView();
    }
    return AppScaffold(
      body: GestureDetector(
        onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
        child: ListView(
          children: [
            const Text(
              'To',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const Gap.v(10.0),
            Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20),
                border: Border.all(color: Colors.grey.shade300, width: 1),
              ),
              child: TextField(
                controller: _addressController,
                maxLines: 2,
                minLines: 2,
                decoration: const InputDecoration(
                  fillColor: Colors.white,
                  filled: true,
                  hintText: 'Enter Receiving Address',
                  hintStyle: TextStyle(
                    color: Colors.grey,
                    fontSize: 16,
                  ),
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                ),
              ),
            ),
            const SizedBox(height: 50),

            // 代币选择器
            GestureDetector(
              onTap: () => showTokenSelection(context, tokens, (tokenBalance) {
                setState(() {
                  _selectedToken = tokenBalance;
                  _amountController.clear();
                });
              }),
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 20),
                decoration: BoxDecoration(
                  borderRadius: 16.0.rVerticalTop,
                  color: context.theme.dividerColor,
                ),
                child: Row(
                  spacing: 12.0,
                  children: [
                    Container(
                      width: 32,
                      height: 32,
                      decoration: BoxDecoration(
                        color: context.theme.scaffoldBackgroundColor,
                        shape: BoxShape.circle,
                      ),
                      child: selectedToken.logo.isNotEmpty
                          ? MEImage(
                              selectedToken.logo,
                              width: 32,
                              height: 32,
                              clipOval: true,
                            )
                          : Center(
                              child: Text(
                                selectedToken.symbol.substring(0, 1).toUpperCase(),
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                    ),
                    Expanded(
                      child: Text(
                        selectedToken.symbol,
                        style: const TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            Container(
              decoration: BoxDecoration(
                borderRadius: 16.0.rVerticalBottom,
                color: context.theme.cardColor,
              ),
              child: Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: _amountController,
                      keyboardType: const TextInputType.numberWithOptions(
                        decimal: true,
                      ),
                      style: const TextStyle(
                        fontSize: 48,
                        fontWeight: FontWeight.w400,
                      ),
                      decoration: const InputDecoration(
                        fillColor: Colors.white,
                        filled: true,
                        hintText: '0',
                        border: InputBorder.none,
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 10,
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(0),
                            topRight: Radius.circular(0),
                            bottomLeft: Radius.circular(16),
                            bottomRight: Radius.circular(16),
                          ),
                          borderSide: BorderSide.none,
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(0),
                            topRight: Radius.circular(0),
                            bottomLeft: Radius.circular(16),
                            bottomRight: Radius.circular(16),
                          ),
                          borderSide: BorderSide.none,
                        ),
                      ),
                      inputFormatters: [
                        AmountInputFormatter(decimalDigits: selectedToken.decimals),
                      ],
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsetsDirectional.only(end: 8.0),
                    child: ElevatedButton(
                      onPressed: () {
                        // 设置为最大可用余额
                        _amountController.text = displayBalance;
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: ColorName.themeColorDark,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(24),
                        ),
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 10,
                        ),
                        elevation: 0,
                      ),
                      child: const Text(
                        'MAX',
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // 可用余额
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
              child: Row(
                children: [
                  Text(
                    'Available: ',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey[600],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Expanded(
                    child: Text(
                      displayBalance,
                      style: const TextStyle(
                        fontSize: 16,
                        color: Colors.black,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // 错误消息
            if (_errorMessage.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: Text(
                  _errorMessage,
                  style: const TextStyle(
                    color: Colors.red,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),

            const SizedBox(height: 24),
          ],
        ),
      ),
      bottomButtonBuilder: (context) => ThemeTextButton(
        onPressed: _handleSend,
        text: context.l10nME.nextButton,
      ),
    );
  }

  Widget _buildSuccessView() {
    return AppScaffold(
      body: SizedBox(
        width: double.infinity,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            const Spacer(),
            // 成功图标
            Container(
              width: 80,
              height: 80,
              decoration: const BoxDecoration(
                shape: BoxShape.circle,
              ),
              child: Assets.icons.check.svg(
                width: 80,
                height: 80,
                colorFilter: ColorName.successColor.filter,
              ),
            ),
            const SizedBox(height: 24),

            // Sent 文本
            const Text(
              'Sent',
              style: TextStyle(
                fontSize: 36,
                fontWeight: FontWeight.bold,
                color: Colors.black,
              ),
            ),

            const Spacer(),

            // Check status 按钮
            ThemeTextButton(
              onPressed: _checkStatus,
              text: 'Check status',
            ),

            const SizedBox(height: 36),

            // Back to wallet 文本按钮
            GestureDetector(
              onTap: _succeedAndBackToWallet,
              child: const Text(
                'Back to wallet',
                style: TextStyle(
                  fontSize: 16,
                  color: Color(0xFF8560FA),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),

            const SizedBox(height: 40),
          ],
        ),
      ),
    );
  }
}
