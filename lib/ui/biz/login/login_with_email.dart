import 'dart:async';

import 'package:card3/exports.dart';
import 'package:flutter/material.dart';
import 'package:pin_code_fields/pin_code_fields.dart';

import '/internals/box.dart' show Boxes;
import '/internals/methods.dart' as methods;
import '/provider/api.dart' show apiServiceProvider;
import '/services/chain_manager.dart' show ChainManager;

// 主要修复：
// 1. 创建单独的PinCodeInputWidget，管理自己的TextEditingController生命周期
// 2. 使用字符串_pinCode保存验证码，而不是直接访问可能被销毁的控制器
// 3. 添加_disposed标志防止在组件销毁后操作
// 4. 使用safeSetState确保安全更新UI状态
// 5. 在回调中添加mounted和_disposed检查，防止异步操作在组件销毁后执行

@FFRoute(name: '/login/email')
class LoginWithEmail extends ConsumerStatefulWidget {
  const LoginWithEmail({super.key});

  @override
  ConsumerState<LoginWithEmail> createState() => _LoginWithEmailState();
}

// 添加一个专门处理PinCode的StatefulWidget
class PinCodeInputWidget extends StatefulWidget {
  const PinCodeInputWidget({
    super.key,
    required this.onCompleted,
    required this.onChanged,
    this.isLoading = false,
  });

  final Function(String) onCompleted;
  final Function(String) onChanged;
  final bool isLoading;

  @override
  State<PinCodeInputWidget> createState() => _PinCodeInputWidgetState();
}

class _PinCodeInputWidgetState extends State<PinCodeInputWidget> {
  final TextEditingController _controller = TextEditingController();
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _focusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PinCodeTextField(
      controller: _controller,
      focusNode: _focusNode,
      enabled: !widget.isLoading,
      length: 6,
      obscureText: false,
      animationType: AnimationType.fade,
      keyboardType: TextInputType.number,
      onChanged: widget.onChanged,
      onCompleted: widget.onCompleted,
      appContext: context,
      beforeTextPaste: (text) {
        return text != null && RegExp(r'^\d{6}$').hasMatch(text);
      },
      pinTheme: PinTheme(
        shape: PinCodeFieldShape.box,
        borderRadius: BorderRadius.circular(8),
        fieldHeight: 60,
        fieldWidth: 50,
        activeFillColor: Colors.white,
        inactiveFillColor: Colors.white,
        selectedFillColor: Colors.white,
        activeColor: const Color(0xFF8560FA),
        inactiveColor: Colors.transparent,
        selectedColor: const Color(0xFF8560FA),
      ),
      enableActiveFill: true,
    );
  }
}

class _LoginWithEmailState extends ConsumerState<LoginWithEmail> {
  final TextEditingController _emailController = TextEditingController();
  String _pinCode = ''; // 存储验证码
  // 添加邮箱输入聚焦控制
  final FocusNode _emailFocusNode = FocusNode();

  bool _isLoading = false;
  bool _isEmailInput = true; // 控制显示邮箱输入或验证码输入

  // 添加邮箱验证相关状态
  bool _emailHasError = false;
  String _emailErrorMessage = '';

  // 邮箱格式验证正则表达式
  static final RegExp _emailRegex = RegExp(
    r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
  );

  // 验证邮箱格式
  bool _validateEmail(String email) {
    if (email.isEmpty) {
      _emailErrorMessage = 'Email is required';
      return false;
    }
    if (!_emailRegex.hasMatch(email)) {
      _emailErrorMessage = 'Please enter a valid email address';
      return false;
    }
    _emailErrorMessage = '';
    return true;
  }

  // 邮箱输入变化时的处理
  void _onEmailChanged(String value) {
    // 输入时清除错误状态
    if (_emailHasError) {
      safeSetState(() {
        _emailHasError = false;
        _emailErrorMessage = '';
      });
    }
  }

  Future<(String?, String?)> _ensureAndGetEthereumWallet(PrivyUser user) async {
    if (user.embeddedEthereumWallets.isEmpty) {
      const maxAttempts = 10;
      int currentAttempt = 0;

      while (currentAttempt < maxAttempts) {
        currentAttempt++;

        if (currentAttempt > 1) {
          methods.handleExceptions(
            error: Exception('Creating wallet failed for ${_emailController.text.trim()}'),
            stackTrace: StackTrace.current,
          );
        }

        try {
          await user.createEthereumWallet();
          await user.refresh();
          if (user.embeddedEthereumWallets.isNotEmpty) {
            if (user.embeddedSolanaWallets.isEmpty) {
              user.createSolanaWallet();
            }
            return (user.identityToken, user.embeddedEthereumWallets.first.address);
          }
          if (currentAttempt < maxAttempts) {
            if (mounted) {
              Card3ToastUtil.showToast(message: ToastMessages.creatingWallet);
            }
            await Future.delayed(const Duration(seconds: 1));
          }
        } catch (e) {
          if (mounted) {
            Card3ToastUtil.showToast(
              message: ToastMessages.creatingWalletFailedWithDetails(e.toString()),
            );
            await Future.delayed(const Duration(seconds: 1));
          }
          rethrow;
        }
      }
      if (mounted) {
        Card3ToastUtil.showToast(message: ToastMessages.creatingWalletFailed);
      }
      return (null, null);
    }

    return (user.identityToken, user.embeddedEthereumWallets.first.address);
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_isEmailInput) {
        _emailFocusNode.requestFocus();
      }
    });
  }

  @override
  void dispose() {
    _emailController.dispose();
    _emailFocusNode.dispose(); // 释放聚焦控制器
    super.dispose();
  }

  // 发送邮箱验证码
  Future<void> _sendEmailCode() async {
    final email = _emailController.text.trim();

    // 验证邮箱格式
    if (!_validateEmail(email)) {
      safeSetState(() {
        _emailHasError = true;
      });
      return;
    }

    safeSetState(() {
      _isLoading = true;
      _emailHasError = false;
    });

    try {
      await privyClient.email.sendCode(email);
      safeSetState(() {
        _isLoading = false;
        _isEmailInput = false; // 切换到验证码输入界面
        Card3ToastUtil.showToast(message: ToastMessages.emailCodeResent);
        // 清除当前焦点，让界面转换更平滑
        FocusScope.of(context).unfocus();
      });
    } catch (e) {
      if (mounted) {
        Card3ToastUtil.showToast(message: ToastMessages.failedToSendEmailCode);
        safeSetState(() {
          _isLoading = false;
        });
      }
    }
  }

  // 验证邮箱验证码
  Future<void> _verifyEmailCode() async {
    final code = _pinCode;
    if (code.length < 6) {
      return;
    }

    safeSetState(() {
      _isLoading = true;
    });
    try {
      final Result<PrivyUser> result = await privyClient.email.loginWithCode(
        code: code,
        email: _emailController.text.trim(),
      );
      result.fold(
        onSuccess: (user) async {
          // 使用封装的方法确保创建以太坊钱包
          final (identityToken, walletAddress) = await _ensureAndGetEthereumWallet(user);
          if (identityToken == null) {
            throw StateError('Failed to create identity');
          }
          if (walletAddress == null) {
            throw StateError('Failed to create wallet');
          }

          try {
            final authorization = await ref
                .read(apiServiceProvider)
                .login(
                  token: identityToken,
                  wallet: walletAddress,
                );
            final user = await ref
                .read(apiServiceProvider)
                .getSelfUserInfo(
                  token: authorization,
                );
            await Future.wait([
              ref.read(persistentTokenRepoProvider.notifier).update(authorization),
              ref.read(userRepoProvider.notifier).update(user),
              Boxes.initUser(user),
              BoxService.updateChainsFromRemote(),
            ]);
            await ChainManager.instance.initialize();
            meNavigator.removeNamedAndPushAndRemoveUntil(Routes.home.name);
          } catch (e) {
            await privyClient.logout();
            meNavigator.removeNamedAndPushAndRemoveUntil(Routes.login.name);
            Card3ToastUtil.showToast(message: ToastMessages.errorMessage(e.toString()));
            rethrow;
          } finally {
            safeSetState(() {
              _isLoading = false;
            });
          }
        },
        onFailure: (error) {
          safeSetState(() {
            _isLoading = false;
          });
          if (mounted) {
            Card3ToastUtil.showToast(message: ToastMessages.invalidOrExpiredLoginCode);
          }
        },
      );
    } catch (e) {
      safeSetState(() {
        _isLoading = false;
      });
      if (mounted) {
        Card3ToastUtil.showToast(message: ToastMessages.loginFailed(e.toString()));
      }
    }
  }

  // 重新发送验证码
  Future<void> _resendCode() async {
    safeSetState(() {
      _isLoading = true;
    });
    try {
      await privyClient.email.sendCode(
        _emailController.text.trim(),
      );

      if (mounted) {
        Card3ToastUtil.showToast(message: ToastMessages.emailCodeResent);
      }
    } catch (e) {
      if (mounted) {
        Card3ToastUtil.showToast(message: ToastMessages.failedToResendEmailCode);
      }
      rethrow;
    } finally {
      safeSetState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF1f1f1),
      appBar: AppBar(
        backgroundColor: const Color(0xFFF1f1f1),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.black),
          onPressed: () {
            if (_isEmailInput) {
              meNavigator.pop();
            } else {
              setState(() {
                _isEmailInput = true; // 切换到邮箱输入界面
              });

              // 切换回邮箱输入界面时，重新聚焦到邮箱输入框
              WidgetsBinding.instance.addPostFrameCallback((_) {
                if (mounted) {
                  _emailFocusNode.requestFocus();
                }
              });
            }
          },
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 24),

            // 根据状态显示邮箱输入或验证码输入
            _isEmailInput ? _buildEmailInput() : _buildPinInput(),

            const Spacer(),

            // 底部提交按钮
            if (_isEmailInput)
              _buildSubmitButton(
                label: 'Submit',
                onPressed: _sendEmailCode,
              ),
          ],
        ),
      ),
    );
  }

  // 邮箱输入界面
  Widget _buildEmailInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 标题
        const Text(
          'Log In / Sign Up',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Color(0xFF212121),
          ),
        ),
        const SizedBox(height: 24),
        // 邮箱输入框
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: _emailHasError ? Border.all(color: Colors.red, width: 1) : null,
          ),
          child: TextField(
            controller: _emailController,
            focusNode: _emailFocusNode,
            // 使用焦点控制
            decoration: InputDecoration(
              filled: true,
              fillColor: Colors.transparent,
              hintText: 'Your email',
              border: InputBorder.none,
              hintStyle: const TextStyle(
                color: Color(0xFF9E9E9E),
                fontSize: 18,
              ),
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
              focusedBorder: OutlineInputBorder(
                borderSide: BorderSide(
                  color: _emailHasError ? Colors.red : Colors.deepPurpleAccent,
                  width: 1,
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              enabledBorder: _emailHasError
                  ? OutlineInputBorder(
                      borderSide: const BorderSide(color: Colors.red, width: 1),
                      borderRadius: BorderRadius.circular(12),
                    )
                  : InputBorder.none,
            ),
            style: const TextStyle(
              fontSize: 24,
              color: Color(0xFF212121),
            ),
            keyboardType: TextInputType.emailAddress,
            autocorrect: false,
            textInputAction: TextInputAction.done,
            // 键盘完成按钮
            onSubmitted: (_) => _sendEmailCode(),
            // 按下完成按钮时发送验证码
            onChanged: _onEmailChanged,
            // 添加实时验证
            enabled: !_isLoading,
          ),
        ),
        // 错误提示文本
        if (_emailHasError && _emailErrorMessage.isNotEmpty) ...[
          const SizedBox(height: 8),
          Text(
            _emailErrorMessage,
            style: const TextStyle(
              color: Colors.red,
              fontSize: 14,
            ),
          ),
        ],
        const SizedBox(height: 16),
      ],
    );
  }

  // 验证码输入界面
  Widget _buildPinInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 标题
        const Text(
          'Enter login code',
          style: TextStyle(
            fontSize: 28,
            fontWeight: FontWeight.bold,
            color: Color(0xFF212121),
          ),
        ),
        const SizedBox(height: 16),

        // 提示文本
        RichText(
          text: TextSpan(
            style: const TextStyle(
              fontSize: 16,
              color: Color(0xFF212121),
            ),
            children: [
              const TextSpan(text: 'Please check '),
              TextSpan(
                text: _emailController.text,
                style: const TextStyle(
                  color: Color(0xFF8560FA), // 紫色
                  fontWeight: FontWeight.bold,
                ),
              ),
              const TextSpan(
                text: ' for an email from privy.io and enter your code below.',
              ),
            ],
          ),
        ),
        const SizedBox(height: 28),

        // 验证码输入框
        PinCodeInputWidget(
          onCompleted: (value) {
            if (mounted) {
              _pinCode = value;
              _verifyEmailCode();
            }
          },
          onChanged: (value) {
            if (mounted) {
              _pinCode = value;
            }
          },
          isLoading: _isLoading,
        ),
        const SizedBox(height: 24),

        // 重新发送按钮
        Center(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Text(
                'Didn\'t get an email? ',
                style: TextStyle(
                  fontSize: 16,
                  color: Color(0xFF757575),
                ),
              ),
              GestureDetector(
                onTap: _isLoading ? null : _resendCode,
                child: const Text(
                  'Resend code',
                  style: TextStyle(
                    fontSize: 16,
                    color: Color(0xFF8560FA),
                    fontWeight: FontWeight.bold,
                    decoration: TextDecoration.none,
                  ),
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 24),

        if (_isLoading)
          const Center(
            child: SizedBox(
              height: 24,
              width: 24,
              child: CircularProgressIndicator(
                color: Color(0xFF8560FA),
              ),
            ),
          ),
      ],
    );
  }

  // 提交按钮
  Widget _buildSubmitButton({
    required String label,
    required VoidCallback onPressed,
  }) {
    return Container(
      width: double.infinity,
      height: 56,
      margin: const EdgeInsets.only(bottom: 24),
      child: ElevatedButton(
        onPressed: _isLoading ? null : onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF8560FA), // 紫色
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          elevation: 0,
        ),
        child: _isLoading
            ? const SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  color: Colors.white,
                  strokeWidth: 3,
                ),
              )
            : Text(
                label,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
      ),
    );
  }
}
