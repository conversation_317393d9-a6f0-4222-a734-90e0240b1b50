import 'dart:async';

import 'package:card3/exports.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import '/internals/box.dart' show Boxes;
import '/internals/methods.dart' show isNetworkError;
import '/provider/api.dart';
import '/provider/settings.dart';
import '/provider/version.dart';
import '/services/chain_manager.dart' show ChainManager;
import '/ui/widgets/app_logo.dart';

@FFRoute(name: '/')
class SplashPage extends ConsumerStatefulWidget {
  const SplashPage({super.key});

  @override
  ConsumerState<SplashPage> createState() => _SplashPageState();
}

class _SplashPageState extends ConsumerState<SplashPage> {
  // late Object _id;

  /// Apply connectivity check for initialization.
  final _connectivityLock = Completer<void>();
  late final StreamSubscription<List<ConnectivityResult>> _connectivitySub;

  final int _maxLongWaitingTime = 50;
  int _longWaitingTime = 1;
  Timer? _longWaitingTimer;
  Object? _exception;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) => _initialize());
    _connectivitySub = Connectivity().onConnectivityChanged.listen((events) {
      if (events.isNotEmpty && !events.contains(ConnectivityResult.none) && !_connectivityLock.isCompleted) {
        _connectivityLock.complete();
      }
    });
  }

  @override
  void dispose() {
    _connectivitySub.cancel();
    _longWaitingTimer?.cancel();
    super.dispose();
  }

  Future<void> _initialize() async {
    DeviceUtil.setHighestRefreshRate();
    _checkLongWaiting();
    try {
      if (kReleaseMode) {
        await _connectivityLock.future.timeout(
          Duration(seconds: _maxLongWaitingTime),
          onTimeout: () => throw TimeoutException('Connectivity result'),
        );
      }

      final (_, version) = await ref.read(appVersionProvider.future);
      LogUtil.d(version);
      await checkAppUpdate(version);
      if (version.shouldUpdate(currentCode: PackageUtil.versionCode) && version.forceUpdate) {
        throw 'Update ${version.name}+${version.code} required.';
      }

      await privyClient.awaitReady();
      if (privyClient.currentAuthState is PrivyAuthStateUnauthenticated) {
        meNavigator.removeNamedAndPushAndRemoveUntil(Routes.login.name);
        return;
      }

      if (ref.read(userRepoProvider) case final user?) {
        await Boxes.initUser(user);
        _longWaitingTimer?.cancel();
        // initializeSecureVerify();
        // final verify = await SecureVerify.verify(
        //   context,
        //   subtitle: globalL10nME.pinUnlockText,
        //   theme: themeBy(
        //     meTheme: defaultMEThemeDark,
        //     locale: Localizations.localeOf(context),
        //   ),
        // );
        // if (!verify && mounted) {
        //   await exitApp();
        //   return;
        // }
        // await SecureVerify.setAuthAt();
        _checkLongWaiting();
      }
      try {
        if (!await ref.read(persistentTokenRepoProvider.notifier).refresh()) {
          meNavigator.removeNamedAndPushAndRemoveUntil(Routes.login.name);
          return;
        }
        final user = await ref.read(apiServiceProvider).getSelfUserInfo();
        await Future.wait([
          ref.read(userRepoProvider.notifier).update(user),
          Boxes.initUser(user),
          BoxService.updateChainsFromRemote(),
        ]);
        await ChainManager.instance.initialize();
      } on ApiException catch (e) {
        if (e.code == 401) {
          ref.read(userRepoProvider.notifier).reset();
          meNavigator.removeNamedAndPushAndRemoveUntil(Routes.login.name);
          return;
        }
        rethrow;
      }

      if (ref.read(userRepoProvider) == null) {
        meNavigator.removeNamedAndPushAndRemoveUntil(Routes.login.name);
      } else {
        // final bool verify = await SecureVerify.verify(
        //   context,
        //   subtitle: globalL10n.pinUnlockText,
        //   theme: themeBy(
        //     locale: ref.read(settingsProvider).locale,
        //     meTheme: defaultMEThemeDark,
        //     brightness: Brightness.dark,
        //   ),
        // );
        // if (verify) {
        //   await SecureVerify.setAuthAt();
        //   meNavigator.removeNamedAndPushAndRemoveUntil(Routes.home.name);
        // } else if (mounted) {
        //   await exitApp();
        // }
        meNavigator.removeNamedAndPushAndRemoveUntil(Routes.home.name);
      }
    } catch (e) {
      _longWaitingTimer?.cancel();
      safeSetState(() {
        _exception = e;
      });
      rethrow;
    }
  }

  Future<void> _checkLongWaiting() async {
    _longWaitingTimer?.cancel();
    setState(() {
      _longWaitingTime = 1;
    });
    _longWaitingTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (!mounted) {
        return;
      }
      if (_longWaitingTime == _maxLongWaitingTime) {
        timer.cancel();
        LogUtil.i('Timeout when first initialize');
        _exception = TimeoutException('First initialize.');
        _longWaitingTimer = null;
      } else {
        _longWaitingTime++;
      }
      safeSetState(() {});
    });
  }

  Widget _buildLogo(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 20),
      child: Hero(
        tag: 'logo-hero',
        child: Consumer(
          builder: (context, ref, _) {
            ref.watch(settingsEnvTapCount);
            return GestureDetector(
              onTap: () => settingsEnvOnTap(context, ref),
              child: const AppLogo(width: 200.0),
            );
          },
        ),
      ),
    );
  }

  Widget _buildLoadingIndicator(BuildContext context) {
    return AnimatedCrossFade(
      duration: kThemeAnimationDuration,
      crossFadeState: _longWaitingTime > 5 && _exception == null ? CrossFadeState.showSecond : CrossFadeState.showFirst,
      sizeCurve: Curves.easeInOutCubic,
      firstChild: const SizedBox.shrink(),
      secondChild: const Padding(
        padding: EdgeInsets.symmetric(vertical: 8),
        child: AppLoading(size: 60.0, alignment: null),
      ),
    );
  }

  Widget _buildLoadingTimeIndicator(BuildContext context) {
    return AnimatedCrossFade(
      duration: kThemeAnimationDuration,
      crossFadeState: _longWaitingTimer != null && _longWaitingTime > 15
          ? CrossFadeState.showSecond
          : CrossFadeState.showFirst,
      sizeCurve: Curves.easeInOutCubic,
      firstChild: const SizedBox.shrink(),
      secondChild: Text(
        context.l10n.textTakingLongerThanExpected(_longWaitingTime),
        style: context.textTheme.bodySmall?.copyWith(
          fontSize: 10.0,
        ),
      ),
    );
  }

  Widget _buildRetryButton(BuildContext context) {
    return ThemeTextButton.textOnly(
      onPressed: () async {
        setState(() => _exception = null);
        await 50.milliseconds.delay;
        _initialize();
      },
      text: context.l10nME.clickToRetryButton,
      textStyle: context.textTheme.bodySmall?.copyWith(
        fontSize: 18.0,
        fontWeight: FontWeight.bold,
      ),
    );
  }

  Widget _buildExceptionText(BuildContext context) {
    return Flexible(
      child: Text(
        isNetworkError(_exception) ? context.l10nME.networkError : '$_exception',
        style: context.textTheme.bodySmall,
        maxLines: 4,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBody: true,
      resizeToAvoidBottomInset: false,
      body: SizedBox.expand(
        child: Column(
          children: <Widget>[
            const Spacer(),
            Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                _buildLogo(context),
                _buildLoadingIndicator(context),
              ],
            ),
            _buildLoadingTimeIndicator(context),
            if (_exception != null)
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(50),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: <Widget>[
                      _buildRetryButton(context),
                      _buildExceptionText(context),
                    ],
                  ),
                ),
              )
            else
              const Spacer(),
          ],
        ),
      ),
    );
  }
}
