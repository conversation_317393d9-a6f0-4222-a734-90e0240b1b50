import 'dart:async';
import 'dart:convert';
import 'dart:io' as io;
import 'dart:typed_data';

import 'package:card3/internals/riverpod.dart' show globalContainer;
import 'package:collection/collection.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:hive_ce_flutter/hive_flutter.dart';
import 'package:me_extensions/me_extensions.dart';
import 'package:me_utils/me_utils.dart' show DeviceUtil, LogUtil;
import 'package:uuid/uuid.dart' show Uuid;

import '/constants/envs.dart' as envs;
import '/models/business.dart' show Network;
import '/models/user.dart';
import '/provider/business.dart' show fetchNetworksNewProvider;
import 'methods.dart' show udid;

const _secGroupId = '7Y7B57CS89.fun.card3';
const _secAccountName = '_osp_lovieo_';

List<int> _generateSecureKey() {
  if (io.Platform.isMacOS) {
    return utf8.encode(envs.envActive.SEC_KEY_MACOS);
  }
  return Hive.generateSecureKey();
}

Future<Uint8List> _prepareEncryptKey() async {
  final keyField = 'card3_ec_key_${io.Platform.operatingSystem}';
  const storage = FlutterSecureStorage(
    aOptions: AndroidOptions(encryptedSharedPreferences: true),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.unlocked_this_device,
      accountName: _secAccountName,
      groupId: _secGroupId,
      synchronizable: false,
    ),
    mOptions: MacOsOptions(
      accessibility: KeychainAccessibility.unlocked_this_device,
      accountName: _secAccountName,
      groupId: _secGroupId,
      synchronizable: false,
    ),
  );

  final stored = await storage.read(key: keyField);
  if (stored != null) {
    return base64Url.decode(stored);
  }

  // Creates a new key if no previous key found.
  final newKey = base64UrlEncode(_generateSecureKey());
  await storage.write(key: keyField, value: newKey);
  return base64Url.decode(newKey);
}

final class Boxes {
  const Boxes._();

  static const _tag = '💾 Hive';

  static Box get cache => _cache;
  static late Box _cache;
  static const nameCache = 'cache_v1';

  static Box get settings => _settings;
  static late Box _settings;
  static const nameSettings = 'settings_v1';

  /////////////////////////////// User specific ///////////////////////////////

  static Box get user => _user;
  static late Box _user;
  static const nameUser = 'user_v1';

  static Box get userSettings => _userSettings;
  static late Box _userSettings;
  static const nameUserSettings = 'user_settings_v1';

  /////////////////////////////// User specific ///////////////////////////////

  static bool get initialized => _initialized;
  static bool _initialized = false;

  // Set this to true to allow boxes recovery.
  static bool get recoveredFromAnotherDevice => _recoveredFromAnotherDevice;
  static bool _recoveredFromAnotherDevice = false;

  static Future<void> init() async {
    await Hive.initFlutter();
    final key = await _prepareEncryptKey();
    final cipher = HiveAesCipher(key);

    try {
      await _init(cipher);
    } on HiveError catch (e) {
      if (e.message.startsWith('Wrong checksum')) {
        _recoveredFromAnotherDevice = true;
        await _init(cipher);
        return;
      }
      rethrow;
    }
  }

  static Future<void> initUser(UserInfo user) async {
    final prefix = await _getBoxPrefix();
    final key = await _prepareEncryptKey();
    final cipher = HiveAesCipher(key);
    await Future.wait([
      _openUserBox(user.referralCode, nameUserSettings, prefix, cipher).then((box) async {
        _userSettings = box;
        if (!box.containsKey('udid:1')) {
          await box.put('udid:1', udid());
        }
        String? uuid = box.get('uuid:1');
        if (uuid == null) {
          uuid = const Uuid().v7();
          await box.put('uuid:1', uuid);
        }
        DeviceUtil.deviceUuid = uuid;
      }),
    ]);
  }

  static Future<String> _getBoxPrefix() async {
    // if (await envs.EnvOverrides.localOverrode case final env?) {
    //   Env.$active = env;
    // }
    final buffer = StringBuffer(envs.envActive.env);
    if (envs.isAuditing) {
      buffer.write(':auditing');
    }
    return buffer.toString();
  }

  static Future<void> _init(HiveCipher cipher) async {
    LogUtil.d(
      'Opening boxes with cipher: ${cipher.calculateKeyCrc()}',
      tag: _tag,
    );
    final prefix = await _getBoxPrefix();
    await Future.wait([
      _openBox(nameUser, prefix, cipher).then((box) => _user = box),
      _openBox(nameCache, prefix, cipher).then((box) => _cache = box),
      _openBox(nameSettings, prefix, cipher).then((box) => _settings = box),
    ]);
    _initialized = true;
  }

  static Future<Box<E>> openBox<E>({
    required String name,
    required bool standalone,
    String? prefix,
    HiveCipher? cipher,
  }) async {
    if (standalone) {
      await Hive.initFlutter();
    }
    prefix ??= await _getBoxPrefix();
    if (cipher == null) {
      final key = await _prepareEncryptKey();
      cipher = HiveAesCipher(key);
    }
    return Hive.openBox(
      '${prefix}_$name',
      encryptionCipher: cipher,
      crashRecovery: _recoveredFromAnotherDevice,
    );
  }

  static Future<Box<E>> _openBox<E>(
    String name,
    String prefix,
    HiveCipher cipher,
  ) {
    return openBox(
      name: name,
      standalone: false,
      prefix: prefix,
      cipher: cipher,
    );
  }

  static Future<Box<E>> _openUserBox<E>(
    String? id,
    String name,
    String prefix,
    HiveCipher cipher,
  ) {
    return openBox(
      name: '${id ?? '0'}:$name',
      standalone: false,
      prefix: prefix,
      cipher: cipher,
    );
  }
}

final class BoxKeys {
  const BoxKeys._();

  static const token = 'token';
  static const info = 'info';
  static const settings = 'settings';
  static const networks = 'network-list';
  static const selectedNetwork = 'network-selected';
}

abstract class BoxCaches {
  static const kWalletPortfolio = 'wallet_portfolio:v1';

  static const keys = [
    kWalletPortfolio,
  ];

  static const balanceKeys = [
    kWalletPortfolio,
  ];

  static E? get<E>(dynamic key, {E? defaultValue}) {
    return Boxes.cache.get(key, defaultValue: defaultValue);
  }

  static Future<void> put<E>(dynamic key, E value) {
    return Boxes.cache.put(key, value);
  }

  static Future<void> delete(dynamic key) {
    return Boxes.cache.delete(key);
  }

  static Future<void> clear() {
    final keys = BoxCaches.keys
        .map(
          (prefix) => Boxes.cache.keys.where(
            (k) => k.toString().startsWith(prefix),
          ),
        )
        .reduce((v, e) => [...v, ...e]);
    return Boxes.cache.deleteAll(keys);
  }

  static Future<void> clearByPrefix(String prefix) {
    final keys = Boxes.cache.keys.where(
      (k) => k.toString().startsWith(prefix),
    );
    return Boxes.cache.deleteAll(keys);
  }

  static Future<void> clearBalanceCaches() {
    return Future.wait(balanceKeys.map(clearByPrefix));
  }
}

final class BoxService {
  const BoxService._();

  static bool get initialized => Boxes.initialized;

  static Stream<BoxEvent> watch({required Box box, dynamic key}) {
    return box.watch(key: key);
  }

  static Stream<BoxEvent> watchUser({dynamic key}) {
    return watch(box: Boxes.user, key: key);
  }

  static Future<void> clearUserBox() => Boxes.user.clear();

  static String? getToken() => Boxes.user.get(BoxKeys.token) as String?;

  static Future<String> updateToken(String token) async {
    await Boxes.user.put(BoxKeys.token, token);
    return token;
  }

  static UserInfo? getUserInfo({Box? box}) {
    UserInfo? get(Box box) {
      return switch (box.get(BoxKeys.info)) {
        final String s when s.isNotEmpty => UserInfo.fromJson(
          s.deserialize(),
        ),
        _ => null,
      };
    }

    if (box != null) {
      return get(box);
    }
    if (!initialized) {
      return null;
    }
    return get(Boxes.user);
  }

  static Future<UserInfo> updateUser(UserInfo user) async {
    await Boxes.user.put(BoxKeys.info, user.toJson().serialize());
    return user;
  }

  static dynamic getSetting(dynamic key, {dynamic defaultValue}) {
    key = '${BoxKeys.settings}:$key';
    return Boxes.settings.get(key, defaultValue: defaultValue);
  }

  static Future<void> updateSetting(dynamic key, dynamic value) {
    key = '${BoxKeys.settings}:$key';
    return Boxes.settings.put(key, value);
  }

  static List<Network> getNetworksFromLocal() {
    if (Boxes.settings.get(BoxKeys.networks) case final List list) {
      final result = list.cast<Map>().map((e) => Network.fromJson(e.cast()));
      return result.toList();
    }
    return [];
  }

  static Network getNetworkByName(String name) {
    return getNetworksFromLocal().firstWhere((e) => e.name == name);
  }

  static Network? getNetworkByNameOrNull(String? name) {
    if (name == null || name.isEmpty) {
      return null;
    }
    return getNetworksFromLocal().firstWhereOrNull((e) => e.name == name);
  }

  static Network getNetworkByIdOKX(String id) {
    return getNetworksFromLocal().firstWhere((e) => e.chainIndexOKX == id);
  }

  static Future<List<Network>> updateChainsFromRemote({
    String? token,
  }) async {
    final result = await globalContainer.read(fetchNetworksNewProvider.future);
    await Boxes.settings.put(
      BoxKeys.networks,
      result.map((e) => e.toJson()).toList(),
    );
    return result;
  }

  static Network getNetworkSelected() {
    final selected = getSetting(BoxKeys.selectedNetwork) as String?;
    final list = getNetworksFromLocal();
    if (selected == null) {
      return list.first;
    }
    return list.firstWhere((e) => e.name == selected, orElse: () => list.first);
  }

  static Future<Network> updateSelectedNetwork(Network network) async {
    await updateSetting(BoxKeys.selectedNetwork, network.name);
    return network;
  }
}
