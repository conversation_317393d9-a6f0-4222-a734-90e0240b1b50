// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'business.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

/// @nodoc
mixin _$BlockExplorer {
  @JsonKey(name: 'explorerName')
  String get explorerName;
  @JsonKey(name: 'url')
  String get url;

  /// Create a copy of BlockExplorer
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $BlockExplorerCopyWith<BlockExplorer> get copyWith =>
      _$BlockExplorerCopyWithImpl<BlockExplorer>(
        this as BlockExplorer,
        _$identity,
      );

  /// Serializes this BlockExplorer to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is BlockExplorer &&
            (identical(other.explorerName, explorerName) ||
                other.explorerName == explorerName) &&
            (identical(other.url, url) || other.url == url));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, explorerName, url);

  @override
  String toString() {
    return 'BlockExplorer(explorerName: $explorerName, url: $url)';
  }
}

/// @nodoc
abstract mixin class $BlockExplorerCopyWith<$Res> {
  factory $BlockExplorerCopyWith(
    BlockExplorer value,
    $Res Function(BlockExplorer) _then,
  ) = _$BlockExplorerCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'explorerName') String explorerName,
    @JsonKey(name: 'url') String url,
  });
}

/// @nodoc
class _$BlockExplorerCopyWithImpl<$Res>
    implements $BlockExplorerCopyWith<$Res> {
  _$BlockExplorerCopyWithImpl(this._self, this._then);

  final BlockExplorer _self;
  final $Res Function(BlockExplorer) _then;

  /// Create a copy of BlockExplorer
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? explorerName = null, Object? url = null}) {
    return _then(
      _self.copyWith(
        explorerName: null == explorerName
            ? _self.explorerName
            : explorerName // ignore: cast_nullable_to_non_nullable
                  as String,
        url: null == url
            ? _self.url
            : url // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _BlockExplorer implements BlockExplorer {
  const _BlockExplorer({
    @JsonKey(name: 'explorerName') required this.explorerName,
    @JsonKey(name: 'url') required this.url,
  });
  factory _BlockExplorer.fromJson(Map<String, dynamic> json) =>
      _$BlockExplorerFromJson(json);

  @override
  @JsonKey(name: 'explorerName')
  final String explorerName;
  @override
  @JsonKey(name: 'url')
  final String url;

  /// Create a copy of BlockExplorer
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$BlockExplorerCopyWith<_BlockExplorer> get copyWith =>
      __$BlockExplorerCopyWithImpl<_BlockExplorer>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$BlockExplorerToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _BlockExplorer &&
            (identical(other.explorerName, explorerName) ||
                other.explorerName == explorerName) &&
            (identical(other.url, url) || other.url == url));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, explorerName, url);

  @override
  String toString() {
    return 'BlockExplorer(explorerName: $explorerName, url: $url)';
  }
}

/// @nodoc
abstract mixin class _$BlockExplorerCopyWith<$Res>
    implements $BlockExplorerCopyWith<$Res> {
  factory _$BlockExplorerCopyWith(
    _BlockExplorer value,
    $Res Function(_BlockExplorer) _then,
  ) = __$BlockExplorerCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'explorerName') String explorerName,
    @JsonKey(name: 'url') String url,
  });
}

/// @nodoc
class __$BlockExplorerCopyWithImpl<$Res>
    implements _$BlockExplorerCopyWith<$Res> {
  __$BlockExplorerCopyWithImpl(this._self, this._then);

  final _BlockExplorer _self;
  final $Res Function(_BlockExplorer) _then;

  /// Create a copy of BlockExplorer
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({Object? explorerName = null, Object? url = null}) {
    return _then(
      _BlockExplorer(
        explorerName: null == explorerName
            ? _self.explorerName
            : explorerName // ignore: cast_nullable_to_non_nullable
                  as String,
        url: null == url
            ? _self.url
            : url // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
mixin _$Ens {
  @JsonKey(name: 'address')
  String get address;
  @JsonKey(name: 'blockCreated')
  int get blockCreated;

  /// Create a copy of Ens
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $EnsCopyWith<Ens> get copyWith =>
      _$EnsCopyWithImpl<Ens>(this as Ens, _$identity);

  /// Serializes this Ens to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is Ens &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.blockCreated, blockCreated) ||
                other.blockCreated == blockCreated));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, address, blockCreated);

  @override
  String toString() {
    return 'Ens(address: $address, blockCreated: $blockCreated)';
  }
}

/// @nodoc
abstract mixin class $EnsCopyWith<$Res> {
  factory $EnsCopyWith(Ens value, $Res Function(Ens) _then) = _$EnsCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'address') String address,
    @JsonKey(name: 'blockCreated') int blockCreated,
  });
}

/// @nodoc
class _$EnsCopyWithImpl<$Res> implements $EnsCopyWith<$Res> {
  _$EnsCopyWithImpl(this._self, this._then);

  final Ens _self;
  final $Res Function(Ens) _then;

  /// Create a copy of Ens
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? address = null, Object? blockCreated = null}) {
    return _then(
      _self.copyWith(
        address: null == address
            ? _self.address
            : address // ignore: cast_nullable_to_non_nullable
                  as String,
        blockCreated: null == blockCreated
            ? _self.blockCreated
            : blockCreated // ignore: cast_nullable_to_non_nullable
                  as int,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _Ens implements Ens {
  const _Ens({
    @JsonKey(name: 'address') required this.address,
    @JsonKey(name: 'blockCreated') required this.blockCreated,
  });
  factory _Ens.fromJson(Map<String, dynamic> json) => _$EnsFromJson(json);

  @override
  @JsonKey(name: 'address')
  final String address;
  @override
  @JsonKey(name: 'blockCreated')
  final int blockCreated;

  /// Create a copy of Ens
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$EnsCopyWith<_Ens> get copyWith =>
      __$EnsCopyWithImpl<_Ens>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$EnsToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Ens &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.blockCreated, blockCreated) ||
                other.blockCreated == blockCreated));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, address, blockCreated);

  @override
  String toString() {
    return 'Ens(address: $address, blockCreated: $blockCreated)';
  }
}

/// @nodoc
abstract mixin class _$EnsCopyWith<$Res> implements $EnsCopyWith<$Res> {
  factory _$EnsCopyWith(_Ens value, $Res Function(_Ens) _then) =
      __$EnsCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'address') String address,
    @JsonKey(name: 'blockCreated') int blockCreated,
  });
}

/// @nodoc
class __$EnsCopyWithImpl<$Res> implements _$EnsCopyWith<$Res> {
  __$EnsCopyWithImpl(this._self, this._then);

  final _Ens _self;
  final $Res Function(_Ens) _then;

  /// Create a copy of Ens
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({Object? address = null, Object? blockCreated = null}) {
    return _then(
      _Ens(
        address: null == address
            ? _self.address
            : address // ignore: cast_nullable_to_non_nullable
                  as String,
        blockCreated: null == blockCreated
            ? _self.blockCreated
            : blockCreated // ignore: cast_nullable_to_non_nullable
                  as int,
      ),
    );
  }
}

/// @nodoc
mixin _$NativeCurrency {
  @JsonKey(name: 'decimals')
  int get decimals;
  @JsonKey(name: 'name')
  String get name;
  @JsonKey(name: 'symbol')
  String get symbol;

  /// Create a copy of NativeCurrency
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $NativeCurrencyCopyWith<NativeCurrency> get copyWith =>
      _$NativeCurrencyCopyWithImpl<NativeCurrency>(
        this as NativeCurrency,
        _$identity,
      );

  /// Serializes this NativeCurrency to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is NativeCurrency &&
            (identical(other.decimals, decimals) ||
                other.decimals == decimals) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.symbol, symbol) || other.symbol == symbol));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, decimals, name, symbol);

  @override
  String toString() {
    return 'NativeCurrency(decimals: $decimals, name: $name, symbol: $symbol)';
  }
}

/// @nodoc
abstract mixin class $NativeCurrencyCopyWith<$Res> {
  factory $NativeCurrencyCopyWith(
    NativeCurrency value,
    $Res Function(NativeCurrency) _then,
  ) = _$NativeCurrencyCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'decimals') int decimals,
    @JsonKey(name: 'name') String name,
    @JsonKey(name: 'symbol') String symbol,
  });
}

/// @nodoc
class _$NativeCurrencyCopyWithImpl<$Res>
    implements $NativeCurrencyCopyWith<$Res> {
  _$NativeCurrencyCopyWithImpl(this._self, this._then);

  final NativeCurrency _self;
  final $Res Function(NativeCurrency) _then;

  /// Create a copy of NativeCurrency
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? decimals = null,
    Object? name = null,
    Object? symbol = null,
  }) {
    return _then(
      _self.copyWith(
        decimals: null == decimals
            ? _self.decimals
            : decimals // ignore: cast_nullable_to_non_nullable
                  as int,
        name: null == name
            ? _self.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        symbol: null == symbol
            ? _self.symbol
            : symbol // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _NativeCurrency implements NativeCurrency {
  const _NativeCurrency({
    @JsonKey(name: 'decimals') required this.decimals,
    @JsonKey(name: 'name') required this.name,
    @JsonKey(name: 'symbol') required this.symbol,
  });
  factory _NativeCurrency.fromJson(Map<String, dynamic> json) =>
      _$NativeCurrencyFromJson(json);

  @override
  @JsonKey(name: 'decimals')
  final int decimals;
  @override
  @JsonKey(name: 'name')
  final String name;
  @override
  @JsonKey(name: 'symbol')
  final String symbol;

  /// Create a copy of NativeCurrency
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$NativeCurrencyCopyWith<_NativeCurrency> get copyWith =>
      __$NativeCurrencyCopyWithImpl<_NativeCurrency>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$NativeCurrencyToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _NativeCurrency &&
            (identical(other.decimals, decimals) ||
                other.decimals == decimals) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.symbol, symbol) || other.symbol == symbol));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, decimals, name, symbol);

  @override
  String toString() {
    return 'NativeCurrency(decimals: $decimals, name: $name, symbol: $symbol)';
  }
}

/// @nodoc
abstract mixin class _$NativeCurrencyCopyWith<$Res>
    implements $NativeCurrencyCopyWith<$Res> {
  factory _$NativeCurrencyCopyWith(
    _NativeCurrency value,
    $Res Function(_NativeCurrency) _then,
  ) = __$NativeCurrencyCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'decimals') int decimals,
    @JsonKey(name: 'name') String name,
    @JsonKey(name: 'symbol') String symbol,
  });
}

/// @nodoc
class __$NativeCurrencyCopyWithImpl<$Res>
    implements _$NativeCurrencyCopyWith<$Res> {
  __$NativeCurrencyCopyWithImpl(this._self, this._then);

  final _NativeCurrency _self;
  final $Res Function(_NativeCurrency) _then;

  /// Create a copy of NativeCurrency
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? decimals = null,
    Object? name = null,
    Object? symbol = null,
  }) {
    return _then(
      _NativeCurrency(
        decimals: null == decimals
            ? _self.decimals
            : decimals // ignore: cast_nullable_to_non_nullable
                  as int,
        name: null == name
            ? _self.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        symbol: null == symbol
            ? _self.symbol
            : symbol // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
mixin _$RPCProvider {
  @JsonKey(name: 'providerName')
  String get providerName;
  @JsonKey(name: 'url')
  String get url;
  @JsonKey(name: 'websocket')
  String? get websocket;

  /// Create a copy of RPCProvider
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $RPCProviderCopyWith<RPCProvider> get copyWith =>
      _$RPCProviderCopyWithImpl<RPCProvider>(this as RPCProvider, _$identity);

  /// Serializes this RPCProvider to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is RPCProvider &&
            (identical(other.providerName, providerName) ||
                other.providerName == providerName) &&
            (identical(other.url, url) || other.url == url) &&
            (identical(other.websocket, websocket) ||
                other.websocket == websocket));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, providerName, url, websocket);

  @override
  String toString() {
    return 'RPCProvider(providerName: $providerName, url: $url, websocket: $websocket)';
  }
}

/// @nodoc
abstract mixin class $RPCProviderCopyWith<$Res> {
  factory $RPCProviderCopyWith(
    RPCProvider value,
    $Res Function(RPCProvider) _then,
  ) = _$RPCProviderCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'providerName') String providerName,
    @JsonKey(name: 'url') String url,
    @JsonKey(name: 'websocket') String? websocket,
  });
}

/// @nodoc
class _$RPCProviderCopyWithImpl<$Res> implements $RPCProviderCopyWith<$Res> {
  _$RPCProviderCopyWithImpl(this._self, this._then);

  final RPCProvider _self;
  final $Res Function(RPCProvider) _then;

  /// Create a copy of RPCProvider
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? providerName = null,
    Object? url = null,
    Object? websocket = freezed,
  }) {
    return _then(
      _self.copyWith(
        providerName: null == providerName
            ? _self.providerName
            : providerName // ignore: cast_nullable_to_non_nullable
                  as String,
        url: null == url
            ? _self.url
            : url // ignore: cast_nullable_to_non_nullable
                  as String,
        websocket: freezed == websocket
            ? _self.websocket
            : websocket // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _RPCProvider implements RPCProvider {
  const _RPCProvider({
    @JsonKey(name: 'providerName') this.providerName = '',
    @JsonKey(name: 'url') required this.url,
    @JsonKey(name: 'websocket') this.websocket,
  });
  factory _RPCProvider.fromJson(Map<String, dynamic> json) =>
      _$RPCProviderFromJson(json);

  @override
  @JsonKey(name: 'providerName')
  final String providerName;
  @override
  @JsonKey(name: 'url')
  final String url;
  @override
  @JsonKey(name: 'websocket')
  final String? websocket;

  /// Create a copy of RPCProvider
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$RPCProviderCopyWith<_RPCProvider> get copyWith =>
      __$RPCProviderCopyWithImpl<_RPCProvider>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$RPCProviderToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _RPCProvider &&
            (identical(other.providerName, providerName) ||
                other.providerName == providerName) &&
            (identical(other.url, url) || other.url == url) &&
            (identical(other.websocket, websocket) ||
                other.websocket == websocket));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, providerName, url, websocket);

  @override
  String toString() {
    return 'RPCProvider(providerName: $providerName, url: $url, websocket: $websocket)';
  }
}

/// @nodoc
abstract mixin class _$RPCProviderCopyWith<$Res>
    implements $RPCProviderCopyWith<$Res> {
  factory _$RPCProviderCopyWith(
    _RPCProvider value,
    $Res Function(_RPCProvider) _then,
  ) = __$RPCProviderCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'providerName') String providerName,
    @JsonKey(name: 'url') String url,
    @JsonKey(name: 'websocket') String? websocket,
  });
}

/// @nodoc
class __$RPCProviderCopyWithImpl<$Res> implements _$RPCProviderCopyWith<$Res> {
  __$RPCProviderCopyWithImpl(this._self, this._then);

  final _RPCProvider _self;
  final $Res Function(_RPCProvider) _then;

  /// Create a copy of RPCProvider
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? providerName = null,
    Object? url = null,
    Object? websocket = freezed,
  }) {
    return _then(
      _RPCProvider(
        providerName: null == providerName
            ? _self.providerName
            : providerName // ignore: cast_nullable_to_non_nullable
                  as String,
        url: null == url
            ? _self.url
            : url // ignore: cast_nullable_to_non_nullable
                  as String,
        websocket: freezed == websocket
            ? _self.websocket
            : websocket // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc
mixin _$Network {
  @JsonKey(name: 'blockExplorers')
  List<BlockExplorer> get blockExplorers;
  @JsonKey(name: 'ens')
  Ens get ens;
  @JsonKey(name: 'iconUrl')
  String get iconUrl;
  @JsonKey(name: 'id')
  int get id;
  @JsonKey(name: 'isEIP1559')
  bool get isEIP1559;
  @JsonKey(name: 'name')
  String get name;
  @JsonKey(name: 'nativeCurrency')
  NativeCurrency get nativeCurrency;
  @JsonKey(name: 'network')
  String get network;
  @JsonKey(name: 'networkType')
  String get networkType;
  @JsonKey(name: 'nftEnable')
  bool get nftEnable;
  @JsonKey(name: 'rpcProviders')
  List<RPCProvider> get rpcProviders;
  @JsonKey(name: 'shortName')
  String get shortName;
  @JsonKey(name: 'weight')
  int get weight;
  @JsonKey(name: 'testnet')
  bool get testnet;
  @JsonKey(name: 'chainIndexOkx')
  String get chainIndexOKX;

  /// Create a copy of Network
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $NetworkCopyWith<Network> get copyWith =>
      _$NetworkCopyWithImpl<Network>(this as Network, _$identity);

  /// Serializes this Network to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is Network &&
            const DeepCollectionEquality().equals(
              other.blockExplorers,
              blockExplorers,
            ) &&
            (identical(other.ens, ens) || other.ens == ens) &&
            (identical(other.iconUrl, iconUrl) || other.iconUrl == iconUrl) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.isEIP1559, isEIP1559) ||
                other.isEIP1559 == isEIP1559) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.nativeCurrency, nativeCurrency) ||
                other.nativeCurrency == nativeCurrency) &&
            (identical(other.network, network) || other.network == network) &&
            (identical(other.networkType, networkType) ||
                other.networkType == networkType) &&
            (identical(other.nftEnable, nftEnable) ||
                other.nftEnable == nftEnable) &&
            const DeepCollectionEquality().equals(
              other.rpcProviders,
              rpcProviders,
            ) &&
            (identical(other.shortName, shortName) ||
                other.shortName == shortName) &&
            (identical(other.weight, weight) || other.weight == weight) &&
            (identical(other.testnet, testnet) || other.testnet == testnet) &&
            (identical(other.chainIndexOKX, chainIndexOKX) ||
                other.chainIndexOKX == chainIndexOKX));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    const DeepCollectionEquality().hash(blockExplorers),
    ens,
    iconUrl,
    id,
    isEIP1559,
    name,
    nativeCurrency,
    network,
    networkType,
    nftEnable,
    const DeepCollectionEquality().hash(rpcProviders),
    shortName,
    weight,
    testnet,
    chainIndexOKX,
  );

  @override
  String toString() {
    return 'Network(blockExplorers: $blockExplorers, ens: $ens, iconUrl: $iconUrl, id: $id, isEIP1559: $isEIP1559, name: $name, nativeCurrency: $nativeCurrency, network: $network, networkType: $networkType, nftEnable: $nftEnable, rpcProviders: $rpcProviders, shortName: $shortName, weight: $weight, testnet: $testnet, chainIndexOKX: $chainIndexOKX)';
  }
}

/// @nodoc
abstract mixin class $NetworkCopyWith<$Res> {
  factory $NetworkCopyWith(Network value, $Res Function(Network) _then) =
      _$NetworkCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'blockExplorers') List<BlockExplorer> blockExplorers,
    @JsonKey(name: 'ens') Ens ens,
    @JsonKey(name: 'iconUrl') String iconUrl,
    @JsonKey(name: 'id') int id,
    @JsonKey(name: 'isEIP1559') bool isEIP1559,
    @JsonKey(name: 'name') String name,
    @JsonKey(name: 'nativeCurrency') NativeCurrency nativeCurrency,
    @JsonKey(name: 'network') String network,
    @JsonKey(name: 'networkType') String networkType,
    @JsonKey(name: 'nftEnable') bool nftEnable,
    @JsonKey(name: 'rpcProviders') List<RPCProvider> rpcProviders,
    @JsonKey(name: 'shortName') String shortName,
    @JsonKey(name: 'weight') int weight,
    @JsonKey(name: 'testnet') bool testnet,
    @JsonKey(name: 'chainIndexOkx') String chainIndexOKX,
  });

  $EnsCopyWith<$Res> get ens;
  $NativeCurrencyCopyWith<$Res> get nativeCurrency;
}

/// @nodoc
class _$NetworkCopyWithImpl<$Res> implements $NetworkCopyWith<$Res> {
  _$NetworkCopyWithImpl(this._self, this._then);

  final Network _self;
  final $Res Function(Network) _then;

  /// Create a copy of Network
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? blockExplorers = null,
    Object? ens = null,
    Object? iconUrl = null,
    Object? id = null,
    Object? isEIP1559 = null,
    Object? name = null,
    Object? nativeCurrency = null,
    Object? network = null,
    Object? networkType = null,
    Object? nftEnable = null,
    Object? rpcProviders = null,
    Object? shortName = null,
    Object? weight = null,
    Object? testnet = null,
    Object? chainIndexOKX = null,
  }) {
    return _then(
      _self.copyWith(
        blockExplorers: null == blockExplorers
            ? _self.blockExplorers
            : blockExplorers // ignore: cast_nullable_to_non_nullable
                  as List<BlockExplorer>,
        ens: null == ens
            ? _self.ens
            : ens // ignore: cast_nullable_to_non_nullable
                  as Ens,
        iconUrl: null == iconUrl
            ? _self.iconUrl
            : iconUrl // ignore: cast_nullable_to_non_nullable
                  as String,
        id: null == id
            ? _self.id
            : id // ignore: cast_nullable_to_non_nullable
                  as int,
        isEIP1559: null == isEIP1559
            ? _self.isEIP1559
            : isEIP1559 // ignore: cast_nullable_to_non_nullable
                  as bool,
        name: null == name
            ? _self.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        nativeCurrency: null == nativeCurrency
            ? _self.nativeCurrency
            : nativeCurrency // ignore: cast_nullable_to_non_nullable
                  as NativeCurrency,
        network: null == network
            ? _self.network
            : network // ignore: cast_nullable_to_non_nullable
                  as String,
        networkType: null == networkType
            ? _self.networkType
            : networkType // ignore: cast_nullable_to_non_nullable
                  as String,
        nftEnable: null == nftEnable
            ? _self.nftEnable
            : nftEnable // ignore: cast_nullable_to_non_nullable
                  as bool,
        rpcProviders: null == rpcProviders
            ? _self.rpcProviders
            : rpcProviders // ignore: cast_nullable_to_non_nullable
                  as List<RPCProvider>,
        shortName: null == shortName
            ? _self.shortName
            : shortName // ignore: cast_nullable_to_non_nullable
                  as String,
        weight: null == weight
            ? _self.weight
            : weight // ignore: cast_nullable_to_non_nullable
                  as int,
        testnet: null == testnet
            ? _self.testnet
            : testnet // ignore: cast_nullable_to_non_nullable
                  as bool,
        chainIndexOKX: null == chainIndexOKX
            ? _self.chainIndexOKX
            : chainIndexOKX // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }

  /// Create a copy of Network
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $EnsCopyWith<$Res> get ens {
    return $EnsCopyWith<$Res>(_self.ens, (value) {
      return _then(_self.copyWith(ens: value));
    });
  }

  /// Create a copy of Network
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $NativeCurrencyCopyWith<$Res> get nativeCurrency {
    return $NativeCurrencyCopyWith<$Res>(_self.nativeCurrency, (value) {
      return _then(_self.copyWith(nativeCurrency: value));
    });
  }
}

/// @nodoc
@JsonSerializable()
class _Network extends Network {
  const _Network({
    @JsonKey(name: 'blockExplorers')
    final List<BlockExplorer> blockExplorers = const [],
    @JsonKey(name: 'ens') this.ens = const Ens(address: '', blockCreated: 0),
    @JsonKey(name: 'iconUrl') this.iconUrl = '',
    @JsonKey(name: 'id') this.id = 0,
    @JsonKey(name: 'isEIP1559') this.isEIP1559 = false,
    @JsonKey(name: 'name') this.name = '',
    @JsonKey(name: 'nativeCurrency')
    this.nativeCurrency = const NativeCurrency(
      decimals: 0,
      name: '',
      symbol: '',
    ),
    @JsonKey(name: 'network') this.network = '',
    @JsonKey(name: 'networkType') this.networkType = '',
    @JsonKey(name: 'nftEnable') this.nftEnable = false,
    @JsonKey(name: 'rpcProviders')
    final List<RPCProvider> rpcProviders = const [],
    @JsonKey(name: 'shortName') this.shortName = '',
    @JsonKey(name: 'weight') this.weight = 0,
    @JsonKey(name: 'testnet') this.testnet = false,
    @JsonKey(name: 'chainIndexOkx') this.chainIndexOKX = '',
  }) : _blockExplorers = blockExplorers,
       _rpcProviders = rpcProviders,
       super._();
  factory _Network.fromJson(Map<String, dynamic> json) =>
      _$NetworkFromJson(json);

  final List<BlockExplorer> _blockExplorers;
  @override
  @JsonKey(name: 'blockExplorers')
  List<BlockExplorer> get blockExplorers {
    if (_blockExplorers is EqualUnmodifiableListView) return _blockExplorers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_blockExplorers);
  }

  @override
  @JsonKey(name: 'ens')
  final Ens ens;
  @override
  @JsonKey(name: 'iconUrl')
  final String iconUrl;
  @override
  @JsonKey(name: 'id')
  final int id;
  @override
  @JsonKey(name: 'isEIP1559')
  final bool isEIP1559;
  @override
  @JsonKey(name: 'name')
  final String name;
  @override
  @JsonKey(name: 'nativeCurrency')
  final NativeCurrency nativeCurrency;
  @override
  @JsonKey(name: 'network')
  final String network;
  @override
  @JsonKey(name: 'networkType')
  final String networkType;
  @override
  @JsonKey(name: 'nftEnable')
  final bool nftEnable;
  final List<RPCProvider> _rpcProviders;
  @override
  @JsonKey(name: 'rpcProviders')
  List<RPCProvider> get rpcProviders {
    if (_rpcProviders is EqualUnmodifiableListView) return _rpcProviders;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_rpcProviders);
  }

  @override
  @JsonKey(name: 'shortName')
  final String shortName;
  @override
  @JsonKey(name: 'weight')
  final int weight;
  @override
  @JsonKey(name: 'testnet')
  final bool testnet;
  @override
  @JsonKey(name: 'chainIndexOkx')
  final String chainIndexOKX;

  /// Create a copy of Network
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$NetworkCopyWith<_Network> get copyWith =>
      __$NetworkCopyWithImpl<_Network>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$NetworkToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Network &&
            const DeepCollectionEquality().equals(
              other._blockExplorers,
              _blockExplorers,
            ) &&
            (identical(other.ens, ens) || other.ens == ens) &&
            (identical(other.iconUrl, iconUrl) || other.iconUrl == iconUrl) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.isEIP1559, isEIP1559) ||
                other.isEIP1559 == isEIP1559) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.nativeCurrency, nativeCurrency) ||
                other.nativeCurrency == nativeCurrency) &&
            (identical(other.network, network) || other.network == network) &&
            (identical(other.networkType, networkType) ||
                other.networkType == networkType) &&
            (identical(other.nftEnable, nftEnable) ||
                other.nftEnable == nftEnable) &&
            const DeepCollectionEquality().equals(
              other._rpcProviders,
              _rpcProviders,
            ) &&
            (identical(other.shortName, shortName) ||
                other.shortName == shortName) &&
            (identical(other.weight, weight) || other.weight == weight) &&
            (identical(other.testnet, testnet) || other.testnet == testnet) &&
            (identical(other.chainIndexOKX, chainIndexOKX) ||
                other.chainIndexOKX == chainIndexOKX));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    const DeepCollectionEquality().hash(_blockExplorers),
    ens,
    iconUrl,
    id,
    isEIP1559,
    name,
    nativeCurrency,
    network,
    networkType,
    nftEnable,
    const DeepCollectionEquality().hash(_rpcProviders),
    shortName,
    weight,
    testnet,
    chainIndexOKX,
  );

  @override
  String toString() {
    return 'Network(blockExplorers: $blockExplorers, ens: $ens, iconUrl: $iconUrl, id: $id, isEIP1559: $isEIP1559, name: $name, nativeCurrency: $nativeCurrency, network: $network, networkType: $networkType, nftEnable: $nftEnable, rpcProviders: $rpcProviders, shortName: $shortName, weight: $weight, testnet: $testnet, chainIndexOKX: $chainIndexOKX)';
  }
}

/// @nodoc
abstract mixin class _$NetworkCopyWith<$Res> implements $NetworkCopyWith<$Res> {
  factory _$NetworkCopyWith(_Network value, $Res Function(_Network) _then) =
      __$NetworkCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'blockExplorers') List<BlockExplorer> blockExplorers,
    @JsonKey(name: 'ens') Ens ens,
    @JsonKey(name: 'iconUrl') String iconUrl,
    @JsonKey(name: 'id') int id,
    @JsonKey(name: 'isEIP1559') bool isEIP1559,
    @JsonKey(name: 'name') String name,
    @JsonKey(name: 'nativeCurrency') NativeCurrency nativeCurrency,
    @JsonKey(name: 'network') String network,
    @JsonKey(name: 'networkType') String networkType,
    @JsonKey(name: 'nftEnable') bool nftEnable,
    @JsonKey(name: 'rpcProviders') List<RPCProvider> rpcProviders,
    @JsonKey(name: 'shortName') String shortName,
    @JsonKey(name: 'weight') int weight,
    @JsonKey(name: 'testnet') bool testnet,
    @JsonKey(name: 'chainIndexOkx') String chainIndexOKX,
  });

  @override
  $EnsCopyWith<$Res> get ens;
  @override
  $NativeCurrencyCopyWith<$Res> get nativeCurrency;
}

/// @nodoc
class __$NetworkCopyWithImpl<$Res> implements _$NetworkCopyWith<$Res> {
  __$NetworkCopyWithImpl(this._self, this._then);

  final _Network _self;
  final $Res Function(_Network) _then;

  /// Create a copy of Network
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? blockExplorers = null,
    Object? ens = null,
    Object? iconUrl = null,
    Object? id = null,
    Object? isEIP1559 = null,
    Object? name = null,
    Object? nativeCurrency = null,
    Object? network = null,
    Object? networkType = null,
    Object? nftEnable = null,
    Object? rpcProviders = null,
    Object? shortName = null,
    Object? weight = null,
    Object? testnet = null,
    Object? chainIndexOKX = null,
  }) {
    return _then(
      _Network(
        blockExplorers: null == blockExplorers
            ? _self._blockExplorers
            : blockExplorers // ignore: cast_nullable_to_non_nullable
                  as List<BlockExplorer>,
        ens: null == ens
            ? _self.ens
            : ens // ignore: cast_nullable_to_non_nullable
                  as Ens,
        iconUrl: null == iconUrl
            ? _self.iconUrl
            : iconUrl // ignore: cast_nullable_to_non_nullable
                  as String,
        id: null == id
            ? _self.id
            : id // ignore: cast_nullable_to_non_nullable
                  as int,
        isEIP1559: null == isEIP1559
            ? _self.isEIP1559
            : isEIP1559 // ignore: cast_nullable_to_non_nullable
                  as bool,
        name: null == name
            ? _self.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        nativeCurrency: null == nativeCurrency
            ? _self.nativeCurrency
            : nativeCurrency // ignore: cast_nullable_to_non_nullable
                  as NativeCurrency,
        network: null == network
            ? _self.network
            : network // ignore: cast_nullable_to_non_nullable
                  as String,
        networkType: null == networkType
            ? _self.networkType
            : networkType // ignore: cast_nullable_to_non_nullable
                  as String,
        nftEnable: null == nftEnable
            ? _self.nftEnable
            : nftEnable // ignore: cast_nullable_to_non_nullable
                  as bool,
        rpcProviders: null == rpcProviders
            ? _self._rpcProviders
            : rpcProviders // ignore: cast_nullable_to_non_nullable
                  as List<RPCProvider>,
        shortName: null == shortName
            ? _self.shortName
            : shortName // ignore: cast_nullable_to_non_nullable
                  as String,
        weight: null == weight
            ? _self.weight
            : weight // ignore: cast_nullable_to_non_nullable
                  as int,
        testnet: null == testnet
            ? _self.testnet
            : testnet // ignore: cast_nullable_to_non_nullable
                  as bool,
        chainIndexOKX: null == chainIndexOKX
            ? _self.chainIndexOKX
            : chainIndexOKX // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }

  /// Create a copy of Network
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $EnsCopyWith<$Res> get ens {
    return $EnsCopyWith<$Res>(_self.ens, (value) {
      return _then(_self.copyWith(ens: value));
    });
  }

  /// Create a copy of Network
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $NativeCurrencyCopyWith<$Res> get nativeCurrency {
    return $NativeCurrencyCopyWith<$Res>(_self.nativeCurrency, (value) {
      return _then(_self.copyWith(nativeCurrency: value));
    });
  }
}

/// @nodoc
mixin _$TokenAmount {
  Decimal get amount;
  int get decimals;

  /// Create a copy of TokenAmount
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $TokenAmountCopyWith<TokenAmount> get copyWith =>
      _$TokenAmountCopyWithImpl<TokenAmount>(this as TokenAmount, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is TokenAmount &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.decimals, decimals) ||
                other.decimals == decimals));
  }

  @override
  int get hashCode => Object.hash(runtimeType, amount, decimals);

  @override
  String toString() {
    return 'TokenAmount(amount: $amount, decimals: $decimals)';
  }
}

/// @nodoc
abstract mixin class $TokenAmountCopyWith<$Res> {
  factory $TokenAmountCopyWith(
    TokenAmount value,
    $Res Function(TokenAmount) _then,
  ) = _$TokenAmountCopyWithImpl;
  @useResult
  $Res call({Decimal amount, int decimals});
}

/// @nodoc
class _$TokenAmountCopyWithImpl<$Res> implements $TokenAmountCopyWith<$Res> {
  _$TokenAmountCopyWithImpl(this._self, this._then);

  final TokenAmount _self;
  final $Res Function(TokenAmount) _then;

  /// Create a copy of TokenAmount
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? amount = null, Object? decimals = null}) {
    return _then(
      _self.copyWith(
        amount: null == amount
            ? _self.amount
            : amount // ignore: cast_nullable_to_non_nullable
                  as Decimal,
        decimals: null == decimals
            ? _self.decimals
            : decimals // ignore: cast_nullable_to_non_nullable
                  as int,
      ),
    );
  }
}

/// @nodoc

class _TokenAmount extends TokenAmount {
  const _TokenAmount({required this.amount, required this.decimals})
    : super._();

  @override
  final Decimal amount;
  @override
  final int decimals;

  /// Create a copy of TokenAmount
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$TokenAmountCopyWith<_TokenAmount> get copyWith =>
      __$TokenAmountCopyWithImpl<_TokenAmount>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _TokenAmount &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.decimals, decimals) ||
                other.decimals == decimals));
  }

  @override
  int get hashCode => Object.hash(runtimeType, amount, decimals);

  @override
  String toString() {
    return 'TokenAmount(amount: $amount, decimals: $decimals)';
  }
}

/// @nodoc
abstract mixin class _$TokenAmountCopyWith<$Res>
    implements $TokenAmountCopyWith<$Res> {
  factory _$TokenAmountCopyWith(
    _TokenAmount value,
    $Res Function(_TokenAmount) _then,
  ) = __$TokenAmountCopyWithImpl;
  @override
  @useResult
  $Res call({Decimal amount, int decimals});
}

/// @nodoc
class __$TokenAmountCopyWithImpl<$Res> implements _$TokenAmountCopyWith<$Res> {
  __$TokenAmountCopyWithImpl(this._self, this._then);

  final _TokenAmount _self;
  final $Res Function(_TokenAmount) _then;

  /// Create a copy of TokenAmount
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({Object? amount = null, Object? decimals = null}) {
    return _then(
      _TokenAmount(
        amount: null == amount
            ? _self.amount
            : amount // ignore: cast_nullable_to_non_nullable
                  as Decimal,
        decimals: null == decimals
            ? _self.decimals
            : decimals // ignore: cast_nullable_to_non_nullable
                  as int,
      ),
    );
  }
}

/// @nodoc
mixin _$Message {
  @JsonKey(name: 'createTime')
  String get createTime;
  @JsonKey(name: 'id')
  int get id;
  @JsonKey(name: 'message')
  String get message;
  @JsonKey(name: 'params')
  String get params;
  @JsonKey(name: 'messageType')
  @MessageTypeConverter()
  MessageType get messageType;

  /// Create a copy of Message
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $MessageCopyWith<Message> get copyWith =>
      _$MessageCopyWithImpl<Message>(this as Message, _$identity);

  /// Serializes this Message to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is Message &&
            (identical(other.createTime, createTime) ||
                other.createTime == createTime) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.params, params) || other.params == params) &&
            (identical(other.messageType, messageType) ||
                other.messageType == messageType));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, createTime, id, message, params, messageType);

  @override
  String toString() {
    return 'Message(createTime: $createTime, id: $id, message: $message, params: $params, messageType: $messageType)';
  }
}

/// @nodoc
abstract mixin class $MessageCopyWith<$Res> {
  factory $MessageCopyWith(Message value, $Res Function(Message) _then) =
      _$MessageCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'createTime') String createTime,
    @JsonKey(name: 'id') int id,
    @JsonKey(name: 'message') String message,
    @JsonKey(name: 'params') String params,
    @JsonKey(name: 'messageType')
    @MessageTypeConverter()
    MessageType messageType,
  });
}

/// @nodoc
class _$MessageCopyWithImpl<$Res> implements $MessageCopyWith<$Res> {
  _$MessageCopyWithImpl(this._self, this._then);

  final Message _self;
  final $Res Function(Message) _then;

  /// Create a copy of Message
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? createTime = null,
    Object? id = null,
    Object? message = null,
    Object? params = null,
    Object? messageType = null,
  }) {
    return _then(
      _self.copyWith(
        createTime: null == createTime
            ? _self.createTime
            : createTime // ignore: cast_nullable_to_non_nullable
                  as String,
        id: null == id
            ? _self.id
            : id // ignore: cast_nullable_to_non_nullable
                  as int,
        message: null == message
            ? _self.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String,
        params: null == params
            ? _self.params
            : params // ignore: cast_nullable_to_non_nullable
                  as String,
        messageType: null == messageType
            ? _self.messageType
            : messageType // ignore: cast_nullable_to_non_nullable
                  as MessageType,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _Message implements Message {
  const _Message({
    @JsonKey(name: 'createTime') required this.createTime,
    @JsonKey(name: 'id') required this.id,
    @JsonKey(name: 'message') required this.message,
    @JsonKey(name: 'params') this.params = '',
    @JsonKey(name: 'messageType')
    @MessageTypeConverter()
    required this.messageType,
  });
  factory _Message.fromJson(Map<String, dynamic> json) =>
      _$MessageFromJson(json);

  @override
  @JsonKey(name: 'createTime')
  final String createTime;
  @override
  @JsonKey(name: 'id')
  final int id;
  @override
  @JsonKey(name: 'message')
  final String message;
  @override
  @JsonKey(name: 'params')
  final String params;
  @override
  @JsonKey(name: 'messageType')
  @MessageTypeConverter()
  final MessageType messageType;

  /// Create a copy of Message
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$MessageCopyWith<_Message> get copyWith =>
      __$MessageCopyWithImpl<_Message>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$MessageToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Message &&
            (identical(other.createTime, createTime) ||
                other.createTime == createTime) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.params, params) || other.params == params) &&
            (identical(other.messageType, messageType) ||
                other.messageType == messageType));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, createTime, id, message, params, messageType);

  @override
  String toString() {
    return 'Message(createTime: $createTime, id: $id, message: $message, params: $params, messageType: $messageType)';
  }
}

/// @nodoc
abstract mixin class _$MessageCopyWith<$Res> implements $MessageCopyWith<$Res> {
  factory _$MessageCopyWith(_Message value, $Res Function(_Message) _then) =
      __$MessageCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'createTime') String createTime,
    @JsonKey(name: 'id') int id,
    @JsonKey(name: 'message') String message,
    @JsonKey(name: 'params') String params,
    @JsonKey(name: 'messageType')
    @MessageTypeConverter()
    MessageType messageType,
  });
}

/// @nodoc
class __$MessageCopyWithImpl<$Res> implements _$MessageCopyWith<$Res> {
  __$MessageCopyWithImpl(this._self, this._then);

  final _Message _self;
  final $Res Function(_Message) _then;

  /// Create a copy of Message
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? createTime = null,
    Object? id = null,
    Object? message = null,
    Object? params = null,
    Object? messageType = null,
  }) {
    return _then(
      _Message(
        createTime: null == createTime
            ? _self.createTime
            : createTime // ignore: cast_nullable_to_non_nullable
                  as String,
        id: null == id
            ? _self.id
            : id // ignore: cast_nullable_to_non_nullable
                  as int,
        message: null == message
            ? _self.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String,
        params: null == params
            ? _self.params
            : params // ignore: cast_nullable_to_non_nullable
                  as String,
        messageType: null == messageType
            ? _self.messageType
            : messageType // ignore: cast_nullable_to_non_nullable
                  as MessageType,
      ),
    );
  }
}

/// @nodoc
mixin _$MessageSayHiParams {
  @JsonKey(name: 'name')
  String get name;
  @JsonKey(name: 'email')
  String get email;
  @JsonKey(name: 'avatar')
  String get avatar;
  @JsonKey(name: 'title')
  String get title;
  @JsonKey(name: 'company')
  String get company;
  @JsonKey(name: 'referralCode')
  String get referralCode;

  /// Create a copy of MessageSayHiParams
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $MessageSayHiParamsCopyWith<MessageSayHiParams> get copyWith =>
      _$MessageSayHiParamsCopyWithImpl<MessageSayHiParams>(
        this as MessageSayHiParams,
        _$identity,
      );

  /// Serializes this MessageSayHiParams to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is MessageSayHiParams &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.avatar, avatar) || other.avatar == avatar) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.company, company) || other.company == company) &&
            (identical(other.referralCode, referralCode) ||
                other.referralCode == referralCode));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    name,
    email,
    avatar,
    title,
    company,
    referralCode,
  );

  @override
  String toString() {
    return 'MessageSayHiParams(name: $name, email: $email, avatar: $avatar, title: $title, company: $company, referralCode: $referralCode)';
  }
}

/// @nodoc
abstract mixin class $MessageSayHiParamsCopyWith<$Res> {
  factory $MessageSayHiParamsCopyWith(
    MessageSayHiParams value,
    $Res Function(MessageSayHiParams) _then,
  ) = _$MessageSayHiParamsCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'name') String name,
    @JsonKey(name: 'email') String email,
    @JsonKey(name: 'avatar') String avatar,
    @JsonKey(name: 'title') String title,
    @JsonKey(name: 'company') String company,
    @JsonKey(name: 'referralCode') String referralCode,
  });
}

/// @nodoc
class _$MessageSayHiParamsCopyWithImpl<$Res>
    implements $MessageSayHiParamsCopyWith<$Res> {
  _$MessageSayHiParamsCopyWithImpl(this._self, this._then);

  final MessageSayHiParams _self;
  final $Res Function(MessageSayHiParams) _then;

  /// Create a copy of MessageSayHiParams
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? email = null,
    Object? avatar = null,
    Object? title = null,
    Object? company = null,
    Object? referralCode = null,
  }) {
    return _then(
      _self.copyWith(
        name: null == name
            ? _self.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        email: null == email
            ? _self.email
            : email // ignore: cast_nullable_to_non_nullable
                  as String,
        avatar: null == avatar
            ? _self.avatar
            : avatar // ignore: cast_nullable_to_non_nullable
                  as String,
        title: null == title
            ? _self.title
            : title // ignore: cast_nullable_to_non_nullable
                  as String,
        company: null == company
            ? _self.company
            : company // ignore: cast_nullable_to_non_nullable
                  as String,
        referralCode: null == referralCode
            ? _self.referralCode
            : referralCode // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _MessageSayHiParams implements MessageSayHiParams {
  const _MessageSayHiParams({
    @JsonKey(name: 'name') this.name = '',
    @JsonKey(name: 'email') this.email = '',
    @JsonKey(name: 'avatar') this.avatar = '',
    @JsonKey(name: 'title') this.title = '',
    @JsonKey(name: 'company') this.company = '',
    @JsonKey(name: 'referralCode') this.referralCode = '',
  });
  factory _MessageSayHiParams.fromJson(Map<String, dynamic> json) =>
      _$MessageSayHiParamsFromJson(json);

  @override
  @JsonKey(name: 'name')
  final String name;
  @override
  @JsonKey(name: 'email')
  final String email;
  @override
  @JsonKey(name: 'avatar')
  final String avatar;
  @override
  @JsonKey(name: 'title')
  final String title;
  @override
  @JsonKey(name: 'company')
  final String company;
  @override
  @JsonKey(name: 'referralCode')
  final String referralCode;

  /// Create a copy of MessageSayHiParams
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$MessageSayHiParamsCopyWith<_MessageSayHiParams> get copyWith =>
      __$MessageSayHiParamsCopyWithImpl<_MessageSayHiParams>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$MessageSayHiParamsToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _MessageSayHiParams &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.avatar, avatar) || other.avatar == avatar) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.company, company) || other.company == company) &&
            (identical(other.referralCode, referralCode) ||
                other.referralCode == referralCode));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    name,
    email,
    avatar,
    title,
    company,
    referralCode,
  );

  @override
  String toString() {
    return 'MessageSayHiParams(name: $name, email: $email, avatar: $avatar, title: $title, company: $company, referralCode: $referralCode)';
  }
}

/// @nodoc
abstract mixin class _$MessageSayHiParamsCopyWith<$Res>
    implements $MessageSayHiParamsCopyWith<$Res> {
  factory _$MessageSayHiParamsCopyWith(
    _MessageSayHiParams value,
    $Res Function(_MessageSayHiParams) _then,
  ) = __$MessageSayHiParamsCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'name') String name,
    @JsonKey(name: 'email') String email,
    @JsonKey(name: 'avatar') String avatar,
    @JsonKey(name: 'title') String title,
    @JsonKey(name: 'company') String company,
    @JsonKey(name: 'referralCode') String referralCode,
  });
}

/// @nodoc
class __$MessageSayHiParamsCopyWithImpl<$Res>
    implements _$MessageSayHiParamsCopyWith<$Res> {
  __$MessageSayHiParamsCopyWithImpl(this._self, this._then);

  final _MessageSayHiParams _self;
  final $Res Function(_MessageSayHiParams) _then;

  /// Create a copy of MessageSayHiParams
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? name = null,
    Object? email = null,
    Object? avatar = null,
    Object? title = null,
    Object? company = null,
    Object? referralCode = null,
  }) {
    return _then(
      _MessageSayHiParams(
        name: null == name
            ? _self.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        email: null == email
            ? _self.email
            : email // ignore: cast_nullable_to_non_nullable
                  as String,
        avatar: null == avatar
            ? _self.avatar
            : avatar // ignore: cast_nullable_to_non_nullable
                  as String,
        title: null == title
            ? _self.title
            : title // ignore: cast_nullable_to_non_nullable
                  as String,
        company: null == company
            ? _self.company
            : company // ignore: cast_nullable_to_non_nullable
                  as String,
        referralCode: null == referralCode
            ? _self.referralCode
            : referralCode // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
mixin _$Point {
  @JsonKey(name: 'action')
  String get action;
  @JsonKey(name: 'createTime')
  String get createTime;
  @JsonKey(name: 'description')
  String get description;
  @JsonKey(name: 'integral')
  int get integral;
  @JsonKey(name: 'ip')
  String get ip;
  @JsonKey(name: 'id')
  int get id;
  @JsonKey(name: 'contentId')
  int get contentId;
  @JsonKey(name: 'contentType')
  String get contentType;
  @JsonKey(name: 'userId')
  int get userId;

  /// Create a copy of Point
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $PointCopyWith<Point> get copyWith =>
      _$PointCopyWithImpl<Point>(this as Point, _$identity);

  /// Serializes this Point to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is Point &&
            (identical(other.action, action) || other.action == action) &&
            (identical(other.createTime, createTime) ||
                other.createTime == createTime) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.integral, integral) ||
                other.integral == integral) &&
            (identical(other.ip, ip) || other.ip == ip) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.contentId, contentId) ||
                other.contentId == contentId) &&
            (identical(other.contentType, contentType) ||
                other.contentType == contentType) &&
            (identical(other.userId, userId) || other.userId == userId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    action,
    createTime,
    description,
    integral,
    ip,
    id,
    contentId,
    contentType,
    userId,
  );

  @override
  String toString() {
    return 'Point(action: $action, createTime: $createTime, description: $description, integral: $integral, ip: $ip, id: $id, contentId: $contentId, contentType: $contentType, userId: $userId)';
  }
}

/// @nodoc
abstract mixin class $PointCopyWith<$Res> {
  factory $PointCopyWith(Point value, $Res Function(Point) _then) =
      _$PointCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'action') String action,
    @JsonKey(name: 'createTime') String createTime,
    @JsonKey(name: 'description') String description,
    @JsonKey(name: 'integral') int integral,
    @JsonKey(name: 'ip') String ip,
    @JsonKey(name: 'id') int id,
    @JsonKey(name: 'contentId') int contentId,
    @JsonKey(name: 'contentType') String contentType,
    @JsonKey(name: 'userId') int userId,
  });
}

/// @nodoc
class _$PointCopyWithImpl<$Res> implements $PointCopyWith<$Res> {
  _$PointCopyWithImpl(this._self, this._then);

  final Point _self;
  final $Res Function(Point) _then;

  /// Create a copy of Point
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? action = null,
    Object? createTime = null,
    Object? description = null,
    Object? integral = null,
    Object? ip = null,
    Object? id = null,
    Object? contentId = null,
    Object? contentType = null,
    Object? userId = null,
  }) {
    return _then(
      _self.copyWith(
        action: null == action
            ? _self.action
            : action // ignore: cast_nullable_to_non_nullable
                  as String,
        createTime: null == createTime
            ? _self.createTime
            : createTime // ignore: cast_nullable_to_non_nullable
                  as String,
        description: null == description
            ? _self.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String,
        integral: null == integral
            ? _self.integral
            : integral // ignore: cast_nullable_to_non_nullable
                  as int,
        ip: null == ip
            ? _self.ip
            : ip // ignore: cast_nullable_to_non_nullable
                  as String,
        id: null == id
            ? _self.id
            : id // ignore: cast_nullable_to_non_nullable
                  as int,
        contentId: null == contentId
            ? _self.contentId
            : contentId // ignore: cast_nullable_to_non_nullable
                  as int,
        contentType: null == contentType
            ? _self.contentType
            : contentType // ignore: cast_nullable_to_non_nullable
                  as String,
        userId: null == userId
            ? _self.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as int,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _Point implements Point {
  const _Point({
    @JsonKey(name: 'action') this.action = '',
    @JsonKey(name: 'createTime') required this.createTime,
    @JsonKey(name: 'description') required this.description,
    @JsonKey(name: 'integral') required this.integral,
    @JsonKey(name: 'ip') this.ip = '',
    @JsonKey(name: 'id') required this.id,
    @JsonKey(name: 'contentId') this.contentId = 0,
    @JsonKey(name: 'contentType') this.contentType = '',
    @JsonKey(name: 'userId') required this.userId,
  });
  factory _Point.fromJson(Map<String, dynamic> json) => _$PointFromJson(json);

  @override
  @JsonKey(name: 'action')
  final String action;
  @override
  @JsonKey(name: 'createTime')
  final String createTime;
  @override
  @JsonKey(name: 'description')
  final String description;
  @override
  @JsonKey(name: 'integral')
  final int integral;
  @override
  @JsonKey(name: 'ip')
  final String ip;
  @override
  @JsonKey(name: 'id')
  final int id;
  @override
  @JsonKey(name: 'contentId')
  final int contentId;
  @override
  @JsonKey(name: 'contentType')
  final String contentType;
  @override
  @JsonKey(name: 'userId')
  final int userId;

  /// Create a copy of Point
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$PointCopyWith<_Point> get copyWith =>
      __$PointCopyWithImpl<_Point>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$PointToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Point &&
            (identical(other.action, action) || other.action == action) &&
            (identical(other.createTime, createTime) ||
                other.createTime == createTime) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.integral, integral) ||
                other.integral == integral) &&
            (identical(other.ip, ip) || other.ip == ip) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.contentId, contentId) ||
                other.contentId == contentId) &&
            (identical(other.contentType, contentType) ||
                other.contentType == contentType) &&
            (identical(other.userId, userId) || other.userId == userId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    action,
    createTime,
    description,
    integral,
    ip,
    id,
    contentId,
    contentType,
    userId,
  );

  @override
  String toString() {
    return 'Point(action: $action, createTime: $createTime, description: $description, integral: $integral, ip: $ip, id: $id, contentId: $contentId, contentType: $contentType, userId: $userId)';
  }
}

/// @nodoc
abstract mixin class _$PointCopyWith<$Res> implements $PointCopyWith<$Res> {
  factory _$PointCopyWith(_Point value, $Res Function(_Point) _then) =
      __$PointCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'action') String action,
    @JsonKey(name: 'createTime') String createTime,
    @JsonKey(name: 'description') String description,
    @JsonKey(name: 'integral') int integral,
    @JsonKey(name: 'ip') String ip,
    @JsonKey(name: 'id') int id,
    @JsonKey(name: 'contentId') int contentId,
    @JsonKey(name: 'contentType') String contentType,
    @JsonKey(name: 'userId') int userId,
  });
}

/// @nodoc
class __$PointCopyWithImpl<$Res> implements _$PointCopyWith<$Res> {
  __$PointCopyWithImpl(this._self, this._then);

  final _Point _self;
  final $Res Function(_Point) _then;

  /// Create a copy of Point
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? action = null,
    Object? createTime = null,
    Object? description = null,
    Object? integral = null,
    Object? ip = null,
    Object? id = null,
    Object? contentId = null,
    Object? contentType = null,
    Object? userId = null,
  }) {
    return _then(
      _Point(
        action: null == action
            ? _self.action
            : action // ignore: cast_nullable_to_non_nullable
                  as String,
        createTime: null == createTime
            ? _self.createTime
            : createTime // ignore: cast_nullable_to_non_nullable
                  as String,
        description: null == description
            ? _self.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String,
        integral: null == integral
            ? _self.integral
            : integral // ignore: cast_nullable_to_non_nullable
                  as int,
        ip: null == ip
            ? _self.ip
            : ip // ignore: cast_nullable_to_non_nullable
                  as String,
        id: null == id
            ? _self.id
            : id // ignore: cast_nullable_to_non_nullable
                  as int,
        contentId: null == contentId
            ? _self.contentId
            : contentId // ignore: cast_nullable_to_non_nullable
                  as int,
        contentType: null == contentType
            ? _self.contentType
            : contentType // ignore: cast_nullable_to_non_nullable
                  as String,
        userId: null == userId
            ? _self.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as int,
      ),
    );
  }
}

/// @nodoc
mixin _$Connection {
  @JsonKey(name: 'cardCode')
  String get cardCode;
  @JsonKey(name: 'city')
  String get city;
  @JsonKey(name: 'image')
  String get image;
  @JsonKey(name: 'createTime')
  String get createTime;
  @JsonKey(name: 'name')
  String get name;
  @JsonKey(name: 'senderAvatar')
  String get senderAvatar;
  @JsonKey(name: 'twitter')
  String get twitter;
  @JsonKey(name: 'uniqId')
  String get uniqId;
  @JsonKey(name: 'verifyStatus')
  VerifyStatus get verifyStatus;

  /// Create a copy of Connection
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ConnectionCopyWith<Connection> get copyWith =>
      _$ConnectionCopyWithImpl<Connection>(this as Connection, _$identity);

  /// Serializes this Connection to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is Connection &&
            (identical(other.cardCode, cardCode) ||
                other.cardCode == cardCode) &&
            (identical(other.city, city) || other.city == city) &&
            (identical(other.image, image) || other.image == image) &&
            (identical(other.createTime, createTime) ||
                other.createTime == createTime) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.senderAvatar, senderAvatar) ||
                other.senderAvatar == senderAvatar) &&
            (identical(other.twitter, twitter) || other.twitter == twitter) &&
            (identical(other.uniqId, uniqId) || other.uniqId == uniqId) &&
            (identical(other.verifyStatus, verifyStatus) ||
                other.verifyStatus == verifyStatus));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    cardCode,
    city,
    image,
    createTime,
    name,
    senderAvatar,
    twitter,
    uniqId,
    verifyStatus,
  );

  @override
  String toString() {
    return 'Connection(cardCode: $cardCode, city: $city, image: $image, createTime: $createTime, name: $name, senderAvatar: $senderAvatar, twitter: $twitter, uniqId: $uniqId, verifyStatus: $verifyStatus)';
  }
}

/// @nodoc
abstract mixin class $ConnectionCopyWith<$Res> {
  factory $ConnectionCopyWith(
    Connection value,
    $Res Function(Connection) _then,
  ) = _$ConnectionCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'cardCode') String cardCode,
    @JsonKey(name: 'city') String city,
    @JsonKey(name: 'image') String image,
    @JsonKey(name: 'createTime') String createTime,
    @JsonKey(name: 'name') String name,
    @JsonKey(name: 'senderAvatar') String senderAvatar,
    @JsonKey(name: 'twitter') String twitter,
    @JsonKey(name: 'uniqId') String uniqId,
    @JsonKey(name: 'verifyStatus') VerifyStatus verifyStatus,
  });
}

/// @nodoc
class _$ConnectionCopyWithImpl<$Res> implements $ConnectionCopyWith<$Res> {
  _$ConnectionCopyWithImpl(this._self, this._then);

  final Connection _self;
  final $Res Function(Connection) _then;

  /// Create a copy of Connection
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? cardCode = null,
    Object? city = null,
    Object? image = null,
    Object? createTime = null,
    Object? name = null,
    Object? senderAvatar = null,
    Object? twitter = null,
    Object? uniqId = null,
    Object? verifyStatus = null,
  }) {
    return _then(
      _self.copyWith(
        cardCode: null == cardCode
            ? _self.cardCode
            : cardCode // ignore: cast_nullable_to_non_nullable
                  as String,
        city: null == city
            ? _self.city
            : city // ignore: cast_nullable_to_non_nullable
                  as String,
        image: null == image
            ? _self.image
            : image // ignore: cast_nullable_to_non_nullable
                  as String,
        createTime: null == createTime
            ? _self.createTime
            : createTime // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _self.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        senderAvatar: null == senderAvatar
            ? _self.senderAvatar
            : senderAvatar // ignore: cast_nullable_to_non_nullable
                  as String,
        twitter: null == twitter
            ? _self.twitter
            : twitter // ignore: cast_nullable_to_non_nullable
                  as String,
        uniqId: null == uniqId
            ? _self.uniqId
            : uniqId // ignore: cast_nullable_to_non_nullable
                  as String,
        verifyStatus: null == verifyStatus
            ? _self.verifyStatus
            : verifyStatus // ignore: cast_nullable_to_non_nullable
                  as VerifyStatus,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _Connection implements Connection {
  const _Connection({
    @JsonKey(name: 'cardCode') this.cardCode = '',
    @JsonKey(name: 'city') this.city = '',
    @JsonKey(name: 'image') this.image = '',
    @JsonKey(name: 'createTime') this.createTime = '',
    @JsonKey(name: 'name') this.name = '',
    @JsonKey(name: 'senderAvatar') this.senderAvatar = '',
    @JsonKey(name: 'twitter') this.twitter = '',
    @JsonKey(name: 'uniqId') this.uniqId = '',
    @JsonKey(name: 'verifyStatus') this.verifyStatus = VerifyStatus.notSure,
  });
  factory _Connection.fromJson(Map<String, dynamic> json) =>
      _$ConnectionFromJson(json);

  @override
  @JsonKey(name: 'cardCode')
  final String cardCode;
  @override
  @JsonKey(name: 'city')
  final String city;
  @override
  @JsonKey(name: 'image')
  final String image;
  @override
  @JsonKey(name: 'createTime')
  final String createTime;
  @override
  @JsonKey(name: 'name')
  final String name;
  @override
  @JsonKey(name: 'senderAvatar')
  final String senderAvatar;
  @override
  @JsonKey(name: 'twitter')
  final String twitter;
  @override
  @JsonKey(name: 'uniqId')
  final String uniqId;
  @override
  @JsonKey(name: 'verifyStatus')
  final VerifyStatus verifyStatus;

  /// Create a copy of Connection
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ConnectionCopyWith<_Connection> get copyWith =>
      __$ConnectionCopyWithImpl<_Connection>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ConnectionToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Connection &&
            (identical(other.cardCode, cardCode) ||
                other.cardCode == cardCode) &&
            (identical(other.city, city) || other.city == city) &&
            (identical(other.image, image) || other.image == image) &&
            (identical(other.createTime, createTime) ||
                other.createTime == createTime) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.senderAvatar, senderAvatar) ||
                other.senderAvatar == senderAvatar) &&
            (identical(other.twitter, twitter) || other.twitter == twitter) &&
            (identical(other.uniqId, uniqId) || other.uniqId == uniqId) &&
            (identical(other.verifyStatus, verifyStatus) ||
                other.verifyStatus == verifyStatus));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    cardCode,
    city,
    image,
    createTime,
    name,
    senderAvatar,
    twitter,
    uniqId,
    verifyStatus,
  );

  @override
  String toString() {
    return 'Connection(cardCode: $cardCode, city: $city, image: $image, createTime: $createTime, name: $name, senderAvatar: $senderAvatar, twitter: $twitter, uniqId: $uniqId, verifyStatus: $verifyStatus)';
  }
}

/// @nodoc
abstract mixin class _$ConnectionCopyWith<$Res>
    implements $ConnectionCopyWith<$Res> {
  factory _$ConnectionCopyWith(
    _Connection value,
    $Res Function(_Connection) _then,
  ) = __$ConnectionCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'cardCode') String cardCode,
    @JsonKey(name: 'city') String city,
    @JsonKey(name: 'image') String image,
    @JsonKey(name: 'createTime') String createTime,
    @JsonKey(name: 'name') String name,
    @JsonKey(name: 'senderAvatar') String senderAvatar,
    @JsonKey(name: 'twitter') String twitter,
    @JsonKey(name: 'uniqId') String uniqId,
    @JsonKey(name: 'verifyStatus') VerifyStatus verifyStatus,
  });
}

/// @nodoc
class __$ConnectionCopyWithImpl<$Res> implements _$ConnectionCopyWith<$Res> {
  __$ConnectionCopyWithImpl(this._self, this._then);

  final _Connection _self;
  final $Res Function(_Connection) _then;

  /// Create a copy of Connection
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? cardCode = null,
    Object? city = null,
    Object? image = null,
    Object? createTime = null,
    Object? name = null,
    Object? senderAvatar = null,
    Object? twitter = null,
    Object? uniqId = null,
    Object? verifyStatus = null,
  }) {
    return _then(
      _Connection(
        cardCode: null == cardCode
            ? _self.cardCode
            : cardCode // ignore: cast_nullable_to_non_nullable
                  as String,
        city: null == city
            ? _self.city
            : city // ignore: cast_nullable_to_non_nullable
                  as String,
        image: null == image
            ? _self.image
            : image // ignore: cast_nullable_to_non_nullable
                  as String,
        createTime: null == createTime
            ? _self.createTime
            : createTime // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _self.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        senderAvatar: null == senderAvatar
            ? _self.senderAvatar
            : senderAvatar // ignore: cast_nullable_to_non_nullable
                  as String,
        twitter: null == twitter
            ? _self.twitter
            : twitter // ignore: cast_nullable_to_non_nullable
                  as String,
        uniqId: null == uniqId
            ? _self.uniqId
            : uniqId // ignore: cast_nullable_to_non_nullable
                  as String,
        verifyStatus: null == verifyStatus
            ? _self.verifyStatus
            : verifyStatus // ignore: cast_nullable_to_non_nullable
                  as VerifyStatus,
      ),
    );
  }
}

/// @nodoc
mixin _$ReferralLog {
  @JsonKey(name: 'createTime')
  String get createTime;
  @JsonKey(name: 'integral')
  int get integral;
  @JsonKey(name: 'inviteeEmail')
  String get inviteeEmail;

  /// Create a copy of ReferralLog
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ReferralLogCopyWith<ReferralLog> get copyWith =>
      _$ReferralLogCopyWithImpl<ReferralLog>(this as ReferralLog, _$identity);

  /// Serializes this ReferralLog to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ReferralLog &&
            (identical(other.createTime, createTime) ||
                other.createTime == createTime) &&
            (identical(other.integral, integral) ||
                other.integral == integral) &&
            (identical(other.inviteeEmail, inviteeEmail) ||
                other.inviteeEmail == inviteeEmail));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, createTime, integral, inviteeEmail);

  @override
  String toString() {
    return 'ReferralLog(createTime: $createTime, integral: $integral, inviteeEmail: $inviteeEmail)';
  }
}

/// @nodoc
abstract mixin class $ReferralLogCopyWith<$Res> {
  factory $ReferralLogCopyWith(
    ReferralLog value,
    $Res Function(ReferralLog) _then,
  ) = _$ReferralLogCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'createTime') String createTime,
    @JsonKey(name: 'integral') int integral,
    @JsonKey(name: 'inviteeEmail') String inviteeEmail,
  });
}

/// @nodoc
class _$ReferralLogCopyWithImpl<$Res> implements $ReferralLogCopyWith<$Res> {
  _$ReferralLogCopyWithImpl(this._self, this._then);

  final ReferralLog _self;
  final $Res Function(ReferralLog) _then;

  /// Create a copy of ReferralLog
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? createTime = null,
    Object? integral = null,
    Object? inviteeEmail = null,
  }) {
    return _then(
      _self.copyWith(
        createTime: null == createTime
            ? _self.createTime
            : createTime // ignore: cast_nullable_to_non_nullable
                  as String,
        integral: null == integral
            ? _self.integral
            : integral // ignore: cast_nullable_to_non_nullable
                  as int,
        inviteeEmail: null == inviteeEmail
            ? _self.inviteeEmail
            : inviteeEmail // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _ReferralLog implements ReferralLog {
  const _ReferralLog({
    @JsonKey(name: 'createTime') this.createTime = '',
    @JsonKey(name: 'integral') this.integral = 0,
    @JsonKey(name: 'inviteeEmail') this.inviteeEmail = '',
  });
  factory _ReferralLog.fromJson(Map<String, dynamic> json) =>
      _$ReferralLogFromJson(json);

  @override
  @JsonKey(name: 'createTime')
  final String createTime;
  @override
  @JsonKey(name: 'integral')
  final int integral;
  @override
  @JsonKey(name: 'inviteeEmail')
  final String inviteeEmail;

  /// Create a copy of ReferralLog
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ReferralLogCopyWith<_ReferralLog> get copyWith =>
      __$ReferralLogCopyWithImpl<_ReferralLog>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ReferralLogToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ReferralLog &&
            (identical(other.createTime, createTime) ||
                other.createTime == createTime) &&
            (identical(other.integral, integral) ||
                other.integral == integral) &&
            (identical(other.inviteeEmail, inviteeEmail) ||
                other.inviteeEmail == inviteeEmail));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, createTime, integral, inviteeEmail);

  @override
  String toString() {
    return 'ReferralLog(createTime: $createTime, integral: $integral, inviteeEmail: $inviteeEmail)';
  }
}

/// @nodoc
abstract mixin class _$ReferralLogCopyWith<$Res>
    implements $ReferralLogCopyWith<$Res> {
  factory _$ReferralLogCopyWith(
    _ReferralLog value,
    $Res Function(_ReferralLog) _then,
  ) = __$ReferralLogCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'createTime') String createTime,
    @JsonKey(name: 'integral') int integral,
    @JsonKey(name: 'inviteeEmail') String inviteeEmail,
  });
}

/// @nodoc
class __$ReferralLogCopyWithImpl<$Res> implements _$ReferralLogCopyWith<$Res> {
  __$ReferralLogCopyWithImpl(this._self, this._then);

  final _ReferralLog _self;
  final $Res Function(_ReferralLog) _then;

  /// Create a copy of ReferralLog
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? createTime = null,
    Object? integral = null,
    Object? inviteeEmail = null,
  }) {
    return _then(
      _ReferralLog(
        createTime: null == createTime
            ? _self.createTime
            : createTime // ignore: cast_nullable_to_non_nullable
                  as String,
        integral: null == integral
            ? _self.integral
            : integral // ignore: cast_nullable_to_non_nullable
                  as int,
        inviteeEmail: null == inviteeEmail
            ? _self.inviteeEmail
            : inviteeEmail // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
mixin _$EventItem {
  @JsonKey(name: 'title')
  String get title;
  @JsonKey(name: 'desc')
  String get desc;
  @JsonKey(name: 'image')
  String get image;
  @JsonKey(name: 'imageWidth')
  String get imageWidth;
  @JsonKey(name: 'link')
  String get link;
  @JsonKey(name: 'span')
  int? get span;
  @JsonKey(name: 'sort')
  int? get sort;
  @JsonKey(name: 'eventIds')
  List<int> get eventIds;
  @JsonKey(name: 'category')
  String get eventType;
  @JsonKey(name: 'isNative')
  bool get isNative;
  @JsonKey(name: 'blendMode')
  bool get blendMode;

  /// Create a copy of EventItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $EventItemCopyWith<EventItem> get copyWith =>
      _$EventItemCopyWithImpl<EventItem>(this as EventItem, _$identity);

  /// Serializes this EventItem to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is EventItem &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.desc, desc) || other.desc == desc) &&
            (identical(other.image, image) || other.image == image) &&
            (identical(other.imageWidth, imageWidth) ||
                other.imageWidth == imageWidth) &&
            (identical(other.link, link) || other.link == link) &&
            (identical(other.span, span) || other.span == span) &&
            (identical(other.sort, sort) || other.sort == sort) &&
            const DeepCollectionEquality().equals(other.eventIds, eventIds) &&
            (identical(other.eventType, eventType) ||
                other.eventType == eventType) &&
            (identical(other.isNative, isNative) ||
                other.isNative == isNative) &&
            (identical(other.blendMode, blendMode) ||
                other.blendMode == blendMode));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    title,
    desc,
    image,
    imageWidth,
    link,
    span,
    sort,
    const DeepCollectionEquality().hash(eventIds),
    eventType,
    isNative,
    blendMode,
  );

  @override
  String toString() {
    return 'EventItem(title: $title, desc: $desc, image: $image, imageWidth: $imageWidth, link: $link, span: $span, sort: $sort, eventIds: $eventIds, eventType: $eventType, isNative: $isNative, blendMode: $blendMode)';
  }
}

/// @nodoc
abstract mixin class $EventItemCopyWith<$Res> {
  factory $EventItemCopyWith(EventItem value, $Res Function(EventItem) _then) =
      _$EventItemCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'title') String title,
    @JsonKey(name: 'desc') String desc,
    @JsonKey(name: 'image') String image,
    @JsonKey(name: 'imageWidth') String imageWidth,
    @JsonKey(name: 'link') String link,
    @JsonKey(name: 'span') int? span,
    @JsonKey(name: 'sort') int? sort,
    @JsonKey(name: 'eventIds') List<int> eventIds,
    @JsonKey(name: 'category') String eventType,
    @JsonKey(name: 'isNative') bool isNative,
    @JsonKey(name: 'blendMode') bool blendMode,
  });
}

/// @nodoc
class _$EventItemCopyWithImpl<$Res> implements $EventItemCopyWith<$Res> {
  _$EventItemCopyWithImpl(this._self, this._then);

  final EventItem _self;
  final $Res Function(EventItem) _then;

  /// Create a copy of EventItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = null,
    Object? desc = null,
    Object? image = null,
    Object? imageWidth = null,
    Object? link = null,
    Object? span = freezed,
    Object? sort = freezed,
    Object? eventIds = null,
    Object? eventType = null,
    Object? isNative = null,
    Object? blendMode = null,
  }) {
    return _then(
      _self.copyWith(
        title: null == title
            ? _self.title
            : title // ignore: cast_nullable_to_non_nullable
                  as String,
        desc: null == desc
            ? _self.desc
            : desc // ignore: cast_nullable_to_non_nullable
                  as String,
        image: null == image
            ? _self.image
            : image // ignore: cast_nullable_to_non_nullable
                  as String,
        imageWidth: null == imageWidth
            ? _self.imageWidth
            : imageWidth // ignore: cast_nullable_to_non_nullable
                  as String,
        link: null == link
            ? _self.link
            : link // ignore: cast_nullable_to_non_nullable
                  as String,
        span: freezed == span
            ? _self.span
            : span // ignore: cast_nullable_to_non_nullable
                  as int?,
        sort: freezed == sort
            ? _self.sort
            : sort // ignore: cast_nullable_to_non_nullable
                  as int?,
        eventIds: null == eventIds
            ? _self.eventIds
            : eventIds // ignore: cast_nullable_to_non_nullable
                  as List<int>,
        eventType: null == eventType
            ? _self.eventType
            : eventType // ignore: cast_nullable_to_non_nullable
                  as String,
        isNative: null == isNative
            ? _self.isNative
            : isNative // ignore: cast_nullable_to_non_nullable
                  as bool,
        blendMode: null == blendMode
            ? _self.blendMode
            : blendMode // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _EventItem implements EventItem {
  const _EventItem({
    @JsonKey(name: 'title') required this.title,
    @JsonKey(name: 'desc') required this.desc,
    @JsonKey(name: 'image') required this.image,
    @JsonKey(name: 'imageWidth') this.imageWidth = '',
    @JsonKey(name: 'link') required this.link,
    @JsonKey(name: 'span') this.span = 1,
    @JsonKey(name: 'sort') this.sort = 0,
    @JsonKey(name: 'eventIds') final List<int> eventIds = const [],
    @JsonKey(name: 'category') this.eventType = '',
    @JsonKey(name: 'isNative') this.isNative = false,
    @JsonKey(name: 'blendMode') this.blendMode = false,
  }) : _eventIds = eventIds;
  factory _EventItem.fromJson(Map<String, dynamic> json) =>
      _$EventItemFromJson(json);

  @override
  @JsonKey(name: 'title')
  final String title;
  @override
  @JsonKey(name: 'desc')
  final String desc;
  @override
  @JsonKey(name: 'image')
  final String image;
  @override
  @JsonKey(name: 'imageWidth')
  final String imageWidth;
  @override
  @JsonKey(name: 'link')
  final String link;
  @override
  @JsonKey(name: 'span')
  final int? span;
  @override
  @JsonKey(name: 'sort')
  final int? sort;
  final List<int> _eventIds;
  @override
  @JsonKey(name: 'eventIds')
  List<int> get eventIds {
    if (_eventIds is EqualUnmodifiableListView) return _eventIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_eventIds);
  }

  @override
  @JsonKey(name: 'category')
  final String eventType;
  @override
  @JsonKey(name: 'isNative')
  final bool isNative;
  @override
  @JsonKey(name: 'blendMode')
  final bool blendMode;

  /// Create a copy of EventItem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$EventItemCopyWith<_EventItem> get copyWith =>
      __$EventItemCopyWithImpl<_EventItem>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$EventItemToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _EventItem &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.desc, desc) || other.desc == desc) &&
            (identical(other.image, image) || other.image == image) &&
            (identical(other.imageWidth, imageWidth) ||
                other.imageWidth == imageWidth) &&
            (identical(other.link, link) || other.link == link) &&
            (identical(other.span, span) || other.span == span) &&
            (identical(other.sort, sort) || other.sort == sort) &&
            const DeepCollectionEquality().equals(other._eventIds, _eventIds) &&
            (identical(other.eventType, eventType) ||
                other.eventType == eventType) &&
            (identical(other.isNative, isNative) ||
                other.isNative == isNative) &&
            (identical(other.blendMode, blendMode) ||
                other.blendMode == blendMode));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    title,
    desc,
    image,
    imageWidth,
    link,
    span,
    sort,
    const DeepCollectionEquality().hash(_eventIds),
    eventType,
    isNative,
    blendMode,
  );

  @override
  String toString() {
    return 'EventItem(title: $title, desc: $desc, image: $image, imageWidth: $imageWidth, link: $link, span: $span, sort: $sort, eventIds: $eventIds, eventType: $eventType, isNative: $isNative, blendMode: $blendMode)';
  }
}

/// @nodoc
abstract mixin class _$EventItemCopyWith<$Res>
    implements $EventItemCopyWith<$Res> {
  factory _$EventItemCopyWith(
    _EventItem value,
    $Res Function(_EventItem) _then,
  ) = __$EventItemCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'title') String title,
    @JsonKey(name: 'desc') String desc,
    @JsonKey(name: 'image') String image,
    @JsonKey(name: 'imageWidth') String imageWidth,
    @JsonKey(name: 'link') String link,
    @JsonKey(name: 'span') int? span,
    @JsonKey(name: 'sort') int? sort,
    @JsonKey(name: 'eventIds') List<int> eventIds,
    @JsonKey(name: 'category') String eventType,
    @JsonKey(name: 'isNative') bool isNative,
    @JsonKey(name: 'blendMode') bool blendMode,
  });
}

/// @nodoc
class __$EventItemCopyWithImpl<$Res> implements _$EventItemCopyWith<$Res> {
  __$EventItemCopyWithImpl(this._self, this._then);

  final _EventItem _self;
  final $Res Function(_EventItem) _then;

  /// Create a copy of EventItem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? title = null,
    Object? desc = null,
    Object? image = null,
    Object? imageWidth = null,
    Object? link = null,
    Object? span = freezed,
    Object? sort = freezed,
    Object? eventIds = null,
    Object? eventType = null,
    Object? isNative = null,
    Object? blendMode = null,
  }) {
    return _then(
      _EventItem(
        title: null == title
            ? _self.title
            : title // ignore: cast_nullable_to_non_nullable
                  as String,
        desc: null == desc
            ? _self.desc
            : desc // ignore: cast_nullable_to_non_nullable
                  as String,
        image: null == image
            ? _self.image
            : image // ignore: cast_nullable_to_non_nullable
                  as String,
        imageWidth: null == imageWidth
            ? _self.imageWidth
            : imageWidth // ignore: cast_nullable_to_non_nullable
                  as String,
        link: null == link
            ? _self.link
            : link // ignore: cast_nullable_to_non_nullable
                  as String,
        span: freezed == span
            ? _self.span
            : span // ignore: cast_nullable_to_non_nullable
                  as int?,
        sort: freezed == sort
            ? _self.sort
            : sort // ignore: cast_nullable_to_non_nullable
                  as int?,
        eventIds: null == eventIds
            ? _self._eventIds
            : eventIds // ignore: cast_nullable_to_non_nullable
                  as List<int>,
        eventType: null == eventType
            ? _self.eventType
            : eventType // ignore: cast_nullable_to_non_nullable
                  as String,
        isNative: null == isNative
            ? _self.isNative
            : isNative // ignore: cast_nullable_to_non_nullable
                  as bool,
        blendMode: null == blendMode
            ? _self.blendMode
            : blendMode // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}

/// @nodoc
mixin _$Config {
  /// chainTokenFilters 示例:
  /// {
  ///   "1": ["ETH", "USDC", "USDT"],
  ///   "8453": ["ETH", "USDT", "USDC", "DEGEN"],
  ///   ...
  /// }
  @JsonKey(name: 'chainTokenFilters')
  Map<String, List<String>> get chainTokenFilters;

  /// 支持的链ID列表
  /// 例如: [10, 324, 59144]
  @JsonKey(name: 'chainFilters')
  List<int> get chainFilters;

  /// 以太坊社区活动ID列表
  /// 例如: [10, 324, 59144]
  @JsonKey(name: 'ethccEventIds')
  List<int> get ethccEventIds;

  /// 默认模式
  @JsonKey(name: 'defaultMode')
  ProfileMode get defaultMode;

  /// 是否支持NFC
  @JsonKey(name: 'nfc')
  bool get nfc;

  /// 是否开启引导
  @JsonKey(name: 'activationGuide')
  bool get activationGuide;

  /// 是否支持OKX
  @JsonKey(name: 'okx')
  bool get okx;

  /// 事件列表
  ///
  @JsonKey(name: 'events')
  List<EventItem> get events;

  /// Create a copy of Config
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ConfigCopyWith<Config> get copyWith =>
      _$ConfigCopyWithImpl<Config>(this as Config, _$identity);

  /// Serializes this Config to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is Config &&
            const DeepCollectionEquality().equals(
              other.chainTokenFilters,
              chainTokenFilters,
            ) &&
            const DeepCollectionEquality().equals(
              other.chainFilters,
              chainFilters,
            ) &&
            const DeepCollectionEquality().equals(
              other.ethccEventIds,
              ethccEventIds,
            ) &&
            (identical(other.defaultMode, defaultMode) ||
                other.defaultMode == defaultMode) &&
            (identical(other.nfc, nfc) || other.nfc == nfc) &&
            (identical(other.activationGuide, activationGuide) ||
                other.activationGuide == activationGuide) &&
            (identical(other.okx, okx) || other.okx == okx) &&
            const DeepCollectionEquality().equals(other.events, events));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    const DeepCollectionEquality().hash(chainTokenFilters),
    const DeepCollectionEquality().hash(chainFilters),
    const DeepCollectionEquality().hash(ethccEventIds),
    defaultMode,
    nfc,
    activationGuide,
    okx,
    const DeepCollectionEquality().hash(events),
  );

  @override
  String toString() {
    return 'Config(chainTokenFilters: $chainTokenFilters, chainFilters: $chainFilters, ethccEventIds: $ethccEventIds, defaultMode: $defaultMode, nfc: $nfc, activationGuide: $activationGuide, okx: $okx, events: $events)';
  }
}

/// @nodoc
abstract mixin class $ConfigCopyWith<$Res> {
  factory $ConfigCopyWith(Config value, $Res Function(Config) _then) =
      _$ConfigCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'chainTokenFilters')
    Map<String, List<String>> chainTokenFilters,
    @JsonKey(name: 'chainFilters') List<int> chainFilters,
    @JsonKey(name: 'ethccEventIds') List<int> ethccEventIds,
    @JsonKey(name: 'defaultMode') ProfileMode defaultMode,
    @JsonKey(name: 'nfc') bool nfc,
    @JsonKey(name: 'activationGuide') bool activationGuide,
    @JsonKey(name: 'okx') bool okx,
    @JsonKey(name: 'events') List<EventItem> events,
  });
}

/// @nodoc
class _$ConfigCopyWithImpl<$Res> implements $ConfigCopyWith<$Res> {
  _$ConfigCopyWithImpl(this._self, this._then);

  final Config _self;
  final $Res Function(Config) _then;

  /// Create a copy of Config
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? chainTokenFilters = null,
    Object? chainFilters = null,
    Object? ethccEventIds = null,
    Object? defaultMode = null,
    Object? nfc = null,
    Object? activationGuide = null,
    Object? okx = null,
    Object? events = null,
  }) {
    return _then(
      _self.copyWith(
        chainTokenFilters: null == chainTokenFilters
            ? _self.chainTokenFilters
            : chainTokenFilters // ignore: cast_nullable_to_non_nullable
                  as Map<String, List<String>>,
        chainFilters: null == chainFilters
            ? _self.chainFilters
            : chainFilters // ignore: cast_nullable_to_non_nullable
                  as List<int>,
        ethccEventIds: null == ethccEventIds
            ? _self.ethccEventIds
            : ethccEventIds // ignore: cast_nullable_to_non_nullable
                  as List<int>,
        defaultMode: null == defaultMode
            ? _self.defaultMode
            : defaultMode // ignore: cast_nullable_to_non_nullable
                  as ProfileMode,
        nfc: null == nfc
            ? _self.nfc
            : nfc // ignore: cast_nullable_to_non_nullable
                  as bool,
        activationGuide: null == activationGuide
            ? _self.activationGuide
            : activationGuide // ignore: cast_nullable_to_non_nullable
                  as bool,
        okx: null == okx
            ? _self.okx
            : okx // ignore: cast_nullable_to_non_nullable
                  as bool,
        events: null == events
            ? _self.events
            : events // ignore: cast_nullable_to_non_nullable
                  as List<EventItem>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _Config extends Config {
  const _Config({
    @JsonKey(name: 'chainTokenFilters')
    required final Map<String, List<String>> chainTokenFilters,
    @JsonKey(name: 'chainFilters') required final List<int> chainFilters,
    @JsonKey(name: 'ethccEventIds') final List<int> ethccEventIds = const [],
    @JsonKey(name: 'defaultMode') this.defaultMode = ProfileMode.ETHCC,
    @JsonKey(name: 'nfc') this.nfc = false,
    @JsonKey(name: 'activationGuide') this.activationGuide = false,
    @JsonKey(name: 'okx') this.okx = false,
    @JsonKey(name: 'events') required final List<EventItem> events,
  }) : _chainTokenFilters = chainTokenFilters,
       _chainFilters = chainFilters,
       _ethccEventIds = ethccEventIds,
       _events = events,
       super._();
  factory _Config.fromJson(Map<String, dynamic> json) => _$ConfigFromJson(json);

  /// chainTokenFilters 示例:
  /// {
  ///   "1": ["ETH", "USDC", "USDT"],
  ///   "8453": ["ETH", "USDT", "USDC", "DEGEN"],
  ///   ...
  /// }
  final Map<String, List<String>> _chainTokenFilters;

  /// chainTokenFilters 示例:
  /// {
  ///   "1": ["ETH", "USDC", "USDT"],
  ///   "8453": ["ETH", "USDT", "USDC", "DEGEN"],
  ///   ...
  /// }
  @override
  @JsonKey(name: 'chainTokenFilters')
  Map<String, List<String>> get chainTokenFilters {
    if (_chainTokenFilters is EqualUnmodifiableMapView)
      return _chainTokenFilters;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_chainTokenFilters);
  }

  /// 支持的链ID列表
  /// 例如: [10, 324, 59144]
  final List<int> _chainFilters;

  /// 支持的链ID列表
  /// 例如: [10, 324, 59144]
  @override
  @JsonKey(name: 'chainFilters')
  List<int> get chainFilters {
    if (_chainFilters is EqualUnmodifiableListView) return _chainFilters;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_chainFilters);
  }

  /// 以太坊社区活动ID列表
  /// 例如: [10, 324, 59144]
  final List<int> _ethccEventIds;

  /// 以太坊社区活动ID列表
  /// 例如: [10, 324, 59144]
  @override
  @JsonKey(name: 'ethccEventIds')
  List<int> get ethccEventIds {
    if (_ethccEventIds is EqualUnmodifiableListView) return _ethccEventIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_ethccEventIds);
  }

  /// 默认模式
  @override
  @JsonKey(name: 'defaultMode')
  final ProfileMode defaultMode;

  /// 是否支持NFC
  @override
  @JsonKey(name: 'nfc')
  final bool nfc;

  /// 是否开启引导
  @override
  @JsonKey(name: 'activationGuide')
  final bool activationGuide;

  /// 是否支持OKX
  @override
  @JsonKey(name: 'okx')
  final bool okx;

  /// 事件列表
  ///
  final List<EventItem> _events;

  /// 事件列表
  ///
  @override
  @JsonKey(name: 'events')
  List<EventItem> get events {
    if (_events is EqualUnmodifiableListView) return _events;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_events);
  }

  /// Create a copy of Config
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ConfigCopyWith<_Config> get copyWith =>
      __$ConfigCopyWithImpl<_Config>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ConfigToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Config &&
            const DeepCollectionEquality().equals(
              other._chainTokenFilters,
              _chainTokenFilters,
            ) &&
            const DeepCollectionEquality().equals(
              other._chainFilters,
              _chainFilters,
            ) &&
            const DeepCollectionEquality().equals(
              other._ethccEventIds,
              _ethccEventIds,
            ) &&
            (identical(other.defaultMode, defaultMode) ||
                other.defaultMode == defaultMode) &&
            (identical(other.nfc, nfc) || other.nfc == nfc) &&
            (identical(other.activationGuide, activationGuide) ||
                other.activationGuide == activationGuide) &&
            (identical(other.okx, okx) || other.okx == okx) &&
            const DeepCollectionEquality().equals(other._events, _events));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    const DeepCollectionEquality().hash(_chainTokenFilters),
    const DeepCollectionEquality().hash(_chainFilters),
    const DeepCollectionEquality().hash(_ethccEventIds),
    defaultMode,
    nfc,
    activationGuide,
    okx,
    const DeepCollectionEquality().hash(_events),
  );

  @override
  String toString() {
    return 'Config(chainTokenFilters: $chainTokenFilters, chainFilters: $chainFilters, ethccEventIds: $ethccEventIds, defaultMode: $defaultMode, nfc: $nfc, activationGuide: $activationGuide, okx: $okx, events: $events)';
  }
}

/// @nodoc
abstract mixin class _$ConfigCopyWith<$Res> implements $ConfigCopyWith<$Res> {
  factory _$ConfigCopyWith(_Config value, $Res Function(_Config) _then) =
      __$ConfigCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'chainTokenFilters')
    Map<String, List<String>> chainTokenFilters,
    @JsonKey(name: 'chainFilters') List<int> chainFilters,
    @JsonKey(name: 'ethccEventIds') List<int> ethccEventIds,
    @JsonKey(name: 'defaultMode') ProfileMode defaultMode,
    @JsonKey(name: 'nfc') bool nfc,
    @JsonKey(name: 'activationGuide') bool activationGuide,
    @JsonKey(name: 'okx') bool okx,
    @JsonKey(name: 'events') List<EventItem> events,
  });
}

/// @nodoc
class __$ConfigCopyWithImpl<$Res> implements _$ConfigCopyWith<$Res> {
  __$ConfigCopyWithImpl(this._self, this._then);

  final _Config _self;
  final $Res Function(_Config) _then;

  /// Create a copy of Config
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? chainTokenFilters = null,
    Object? chainFilters = null,
    Object? ethccEventIds = null,
    Object? defaultMode = null,
    Object? nfc = null,
    Object? activationGuide = null,
    Object? okx = null,
    Object? events = null,
  }) {
    return _then(
      _Config(
        chainTokenFilters: null == chainTokenFilters
            ? _self._chainTokenFilters
            : chainTokenFilters // ignore: cast_nullable_to_non_nullable
                  as Map<String, List<String>>,
        chainFilters: null == chainFilters
            ? _self._chainFilters
            : chainFilters // ignore: cast_nullable_to_non_nullable
                  as List<int>,
        ethccEventIds: null == ethccEventIds
            ? _self._ethccEventIds
            : ethccEventIds // ignore: cast_nullable_to_non_nullable
                  as List<int>,
        defaultMode: null == defaultMode
            ? _self.defaultMode
            : defaultMode // ignore: cast_nullable_to_non_nullable
                  as ProfileMode,
        nfc: null == nfc
            ? _self.nfc
            : nfc // ignore: cast_nullable_to_non_nullable
                  as bool,
        activationGuide: null == activationGuide
            ? _self.activationGuide
            : activationGuide // ignore: cast_nullable_to_non_nullable
                  as bool,
        okx: null == okx
            ? _self.okx
            : okx // ignore: cast_nullable_to_non_nullable
                  as bool,
        events: null == events
            ? _self._events
            : events // ignore: cast_nullable_to_non_nullable
                  as List<EventItem>,
      ),
    );
  }
}
