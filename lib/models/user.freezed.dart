// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

/// @nodoc
mixin _$UserInfo {
  @JsonKey(name: 'avatar')
  String get avatar;
  @JsonKey(name: 'bannerHref')
  String get bannerHref;
  @JsonKey(name: 'bannerImage')
  String get bannerImage;
  @JsonKey(name: 'cardCode')
  String get cardCode;
  @JsonKey(name: 'company')
  String get company;
  @JsonKey(name: 'currentType')
  int get currentType;
  @JsonKey(name: 'evmWallet')
  String get evmWallet;
  @JsonKey(name: 'handle')
  String get handle;
  @JsonKey(name: 'integral')
  int get integral;
  @JsonKey(name: 'lastMessageId')
  int get lastMessageId;
  @JsonKey(name: 'latestMessageId')
  int get latestMessageId;
  @JsonKey(name: 'name')
  String get name;
  @JsonKey(name: 'profileMode')
  ProfileMode get profileMode;
  @JsonKey(name: 'redirectUrl')
  String get redirectUrl;
  @JsonKey(name: 'referralCode')
  String get referralCode;
  @JsonKey(name: 'title')
  String get title;
  @JsonKey(name: 'userEmail')
  String get userEmail;

  /// Create a copy of UserInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $UserInfoCopyWith<UserInfo> get copyWith =>
      _$UserInfoCopyWithImpl<UserInfo>(this as UserInfo, _$identity);

  /// Serializes this UserInfo to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is UserInfo &&
            (identical(other.avatar, avatar) || other.avatar == avatar) &&
            (identical(other.bannerHref, bannerHref) ||
                other.bannerHref == bannerHref) &&
            (identical(other.bannerImage, bannerImage) ||
                other.bannerImage == bannerImage) &&
            (identical(other.cardCode, cardCode) ||
                other.cardCode == cardCode) &&
            (identical(other.company, company) || other.company == company) &&
            (identical(other.currentType, currentType) ||
                other.currentType == currentType) &&
            (identical(other.evmWallet, evmWallet) ||
                other.evmWallet == evmWallet) &&
            (identical(other.handle, handle) || other.handle == handle) &&
            (identical(other.integral, integral) ||
                other.integral == integral) &&
            (identical(other.lastMessageId, lastMessageId) ||
                other.lastMessageId == lastMessageId) &&
            (identical(other.latestMessageId, latestMessageId) ||
                other.latestMessageId == latestMessageId) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.profileMode, profileMode) ||
                other.profileMode == profileMode) &&
            (identical(other.redirectUrl, redirectUrl) ||
                other.redirectUrl == redirectUrl) &&
            (identical(other.referralCode, referralCode) ||
                other.referralCode == referralCode) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.userEmail, userEmail) ||
                other.userEmail == userEmail));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    avatar,
    bannerHref,
    bannerImage,
    cardCode,
    company,
    currentType,
    evmWallet,
    handle,
    integral,
    lastMessageId,
    latestMessageId,
    name,
    profileMode,
    redirectUrl,
    referralCode,
    title,
    userEmail,
  );

  @override
  String toString() {
    return 'UserInfo(avatar: $avatar, bannerHref: $bannerHref, bannerImage: $bannerImage, cardCode: $cardCode, company: $company, currentType: $currentType, evmWallet: $evmWallet, handle: $handle, integral: $integral, lastMessageId: $lastMessageId, latestMessageId: $latestMessageId, name: $name, profileMode: $profileMode, redirectUrl: $redirectUrl, referralCode: $referralCode, title: $title, userEmail: $userEmail)';
  }
}

/// @nodoc
abstract mixin class $UserInfoCopyWith<$Res> {
  factory $UserInfoCopyWith(UserInfo value, $Res Function(UserInfo) _then) =
      _$UserInfoCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'avatar') String avatar,
    @JsonKey(name: 'bannerHref') String bannerHref,
    @JsonKey(name: 'bannerImage') String bannerImage,
    @JsonKey(name: 'cardCode') String cardCode,
    @JsonKey(name: 'company') String company,
    @JsonKey(name: 'currentType') int currentType,
    @JsonKey(name: 'evmWallet') String evmWallet,
    @JsonKey(name: 'handle') String handle,
    @JsonKey(name: 'integral') int integral,
    @JsonKey(name: 'lastMessageId') int lastMessageId,
    @JsonKey(name: 'latestMessageId') int latestMessageId,
    @JsonKey(name: 'name') String name,
    @JsonKey(name: 'profileMode') ProfileMode profileMode,
    @JsonKey(name: 'redirectUrl') String redirectUrl,
    @JsonKey(name: 'referralCode') String referralCode,
    @JsonKey(name: 'title') String title,
    @JsonKey(name: 'userEmail') String userEmail,
  });
}

/// @nodoc
class _$UserInfoCopyWithImpl<$Res> implements $UserInfoCopyWith<$Res> {
  _$UserInfoCopyWithImpl(this._self, this._then);

  final UserInfo _self;
  final $Res Function(UserInfo) _then;

  /// Create a copy of UserInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? avatar = null,
    Object? bannerHref = null,
    Object? bannerImage = null,
    Object? cardCode = null,
    Object? company = null,
    Object? currentType = null,
    Object? evmWallet = null,
    Object? handle = null,
    Object? integral = null,
    Object? lastMessageId = null,
    Object? latestMessageId = null,
    Object? name = null,
    Object? profileMode = null,
    Object? redirectUrl = null,
    Object? referralCode = null,
    Object? title = null,
    Object? userEmail = null,
  }) {
    return _then(
      _self.copyWith(
        avatar: null == avatar
            ? _self.avatar
            : avatar // ignore: cast_nullable_to_non_nullable
                  as String,
        bannerHref: null == bannerHref
            ? _self.bannerHref
            : bannerHref // ignore: cast_nullable_to_non_nullable
                  as String,
        bannerImage: null == bannerImage
            ? _self.bannerImage
            : bannerImage // ignore: cast_nullable_to_non_nullable
                  as String,
        cardCode: null == cardCode
            ? _self.cardCode
            : cardCode // ignore: cast_nullable_to_non_nullable
                  as String,
        company: null == company
            ? _self.company
            : company // ignore: cast_nullable_to_non_nullable
                  as String,
        currentType: null == currentType
            ? _self.currentType
            : currentType // ignore: cast_nullable_to_non_nullable
                  as int,
        evmWallet: null == evmWallet
            ? _self.evmWallet
            : evmWallet // ignore: cast_nullable_to_non_nullable
                  as String,
        handle: null == handle
            ? _self.handle
            : handle // ignore: cast_nullable_to_non_nullable
                  as String,
        integral: null == integral
            ? _self.integral
            : integral // ignore: cast_nullable_to_non_nullable
                  as int,
        lastMessageId: null == lastMessageId
            ? _self.lastMessageId
            : lastMessageId // ignore: cast_nullable_to_non_nullable
                  as int,
        latestMessageId: null == latestMessageId
            ? _self.latestMessageId
            : latestMessageId // ignore: cast_nullable_to_non_nullable
                  as int,
        name: null == name
            ? _self.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        profileMode: null == profileMode
            ? _self.profileMode
            : profileMode // ignore: cast_nullable_to_non_nullable
                  as ProfileMode,
        redirectUrl: null == redirectUrl
            ? _self.redirectUrl
            : redirectUrl // ignore: cast_nullable_to_non_nullable
                  as String,
        referralCode: null == referralCode
            ? _self.referralCode
            : referralCode // ignore: cast_nullable_to_non_nullable
                  as String,
        title: null == title
            ? _self.title
            : title // ignore: cast_nullable_to_non_nullable
                  as String,
        userEmail: null == userEmail
            ? _self.userEmail
            : userEmail // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _UserInfo implements UserInfo {
  const _UserInfo({
    @JsonKey(name: 'avatar') this.avatar = '',
    @JsonKey(name: 'bannerHref') this.bannerHref = '',
    @JsonKey(name: 'bannerImage') this.bannerImage = '',
    @JsonKey(name: 'cardCode') this.cardCode = '',
    @JsonKey(name: 'company') this.company = '',
    @JsonKey(name: 'currentType') this.currentType = 0,
    @JsonKey(name: 'evmWallet') this.evmWallet = '',
    @JsonKey(name: 'handle') this.handle = '',
    @JsonKey(name: 'integral') this.integral = 0,
    @JsonKey(name: 'lastMessageId') this.lastMessageId = 0,
    @JsonKey(name: 'latestMessageId') this.latestMessageId = 0,
    @JsonKey(name: 'name') this.name = '',
    @JsonKey(name: 'profileMode') this.profileMode = ProfileMode.EMPTY,
    @JsonKey(name: 'redirectUrl') this.redirectUrl = '',
    @JsonKey(name: 'referralCode') this.referralCode = '',
    @JsonKey(name: 'title') this.title = '',
    @JsonKey(name: 'userEmail') this.userEmail = '',
  });
  factory _UserInfo.fromJson(Map<String, dynamic> json) =>
      _$UserInfoFromJson(json);

  @override
  @JsonKey(name: 'avatar')
  final String avatar;
  @override
  @JsonKey(name: 'bannerHref')
  final String bannerHref;
  @override
  @JsonKey(name: 'bannerImage')
  final String bannerImage;
  @override
  @JsonKey(name: 'cardCode')
  final String cardCode;
  @override
  @JsonKey(name: 'company')
  final String company;
  @override
  @JsonKey(name: 'currentType')
  final int currentType;
  @override
  @JsonKey(name: 'evmWallet')
  final String evmWallet;
  @override
  @JsonKey(name: 'handle')
  final String handle;
  @override
  @JsonKey(name: 'integral')
  final int integral;
  @override
  @JsonKey(name: 'lastMessageId')
  final int lastMessageId;
  @override
  @JsonKey(name: 'latestMessageId')
  final int latestMessageId;
  @override
  @JsonKey(name: 'name')
  final String name;
  @override
  @JsonKey(name: 'profileMode')
  final ProfileMode profileMode;
  @override
  @JsonKey(name: 'redirectUrl')
  final String redirectUrl;
  @override
  @JsonKey(name: 'referralCode')
  final String referralCode;
  @override
  @JsonKey(name: 'title')
  final String title;
  @override
  @JsonKey(name: 'userEmail')
  final String userEmail;

  /// Create a copy of UserInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$UserInfoCopyWith<_UserInfo> get copyWith =>
      __$UserInfoCopyWithImpl<_UserInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$UserInfoToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _UserInfo &&
            (identical(other.avatar, avatar) || other.avatar == avatar) &&
            (identical(other.bannerHref, bannerHref) ||
                other.bannerHref == bannerHref) &&
            (identical(other.bannerImage, bannerImage) ||
                other.bannerImage == bannerImage) &&
            (identical(other.cardCode, cardCode) ||
                other.cardCode == cardCode) &&
            (identical(other.company, company) || other.company == company) &&
            (identical(other.currentType, currentType) ||
                other.currentType == currentType) &&
            (identical(other.evmWallet, evmWallet) ||
                other.evmWallet == evmWallet) &&
            (identical(other.handle, handle) || other.handle == handle) &&
            (identical(other.integral, integral) ||
                other.integral == integral) &&
            (identical(other.lastMessageId, lastMessageId) ||
                other.lastMessageId == lastMessageId) &&
            (identical(other.latestMessageId, latestMessageId) ||
                other.latestMessageId == latestMessageId) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.profileMode, profileMode) ||
                other.profileMode == profileMode) &&
            (identical(other.redirectUrl, redirectUrl) ||
                other.redirectUrl == redirectUrl) &&
            (identical(other.referralCode, referralCode) ||
                other.referralCode == referralCode) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.userEmail, userEmail) ||
                other.userEmail == userEmail));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    avatar,
    bannerHref,
    bannerImage,
    cardCode,
    company,
    currentType,
    evmWallet,
    handle,
    integral,
    lastMessageId,
    latestMessageId,
    name,
    profileMode,
    redirectUrl,
    referralCode,
    title,
    userEmail,
  );

  @override
  String toString() {
    return 'UserInfo(avatar: $avatar, bannerHref: $bannerHref, bannerImage: $bannerImage, cardCode: $cardCode, company: $company, currentType: $currentType, evmWallet: $evmWallet, handle: $handle, integral: $integral, lastMessageId: $lastMessageId, latestMessageId: $latestMessageId, name: $name, profileMode: $profileMode, redirectUrl: $redirectUrl, referralCode: $referralCode, title: $title, userEmail: $userEmail)';
  }
}

/// @nodoc
abstract mixin class _$UserInfoCopyWith<$Res>
    implements $UserInfoCopyWith<$Res> {
  factory _$UserInfoCopyWith(_UserInfo value, $Res Function(_UserInfo) _then) =
      __$UserInfoCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'avatar') String avatar,
    @JsonKey(name: 'bannerHref') String bannerHref,
    @JsonKey(name: 'bannerImage') String bannerImage,
    @JsonKey(name: 'cardCode') String cardCode,
    @JsonKey(name: 'company') String company,
    @JsonKey(name: 'currentType') int currentType,
    @JsonKey(name: 'evmWallet') String evmWallet,
    @JsonKey(name: 'handle') String handle,
    @JsonKey(name: 'integral') int integral,
    @JsonKey(name: 'lastMessageId') int lastMessageId,
    @JsonKey(name: 'latestMessageId') int latestMessageId,
    @JsonKey(name: 'name') String name,
    @JsonKey(name: 'profileMode') ProfileMode profileMode,
    @JsonKey(name: 'redirectUrl') String redirectUrl,
    @JsonKey(name: 'referralCode') String referralCode,
    @JsonKey(name: 'title') String title,
    @JsonKey(name: 'userEmail') String userEmail,
  });
}

/// @nodoc
class __$UserInfoCopyWithImpl<$Res> implements _$UserInfoCopyWith<$Res> {
  __$UserInfoCopyWithImpl(this._self, this._then);

  final _UserInfo _self;
  final $Res Function(_UserInfo) _then;

  /// Create a copy of UserInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? avatar = null,
    Object? bannerHref = null,
    Object? bannerImage = null,
    Object? cardCode = null,
    Object? company = null,
    Object? currentType = null,
    Object? evmWallet = null,
    Object? handle = null,
    Object? integral = null,
    Object? lastMessageId = null,
    Object? latestMessageId = null,
    Object? name = null,
    Object? profileMode = null,
    Object? redirectUrl = null,
    Object? referralCode = null,
    Object? title = null,
    Object? userEmail = null,
  }) {
    return _then(
      _UserInfo(
        avatar: null == avatar
            ? _self.avatar
            : avatar // ignore: cast_nullable_to_non_nullable
                  as String,
        bannerHref: null == bannerHref
            ? _self.bannerHref
            : bannerHref // ignore: cast_nullable_to_non_nullable
                  as String,
        bannerImage: null == bannerImage
            ? _self.bannerImage
            : bannerImage // ignore: cast_nullable_to_non_nullable
                  as String,
        cardCode: null == cardCode
            ? _self.cardCode
            : cardCode // ignore: cast_nullable_to_non_nullable
                  as String,
        company: null == company
            ? _self.company
            : company // ignore: cast_nullable_to_non_nullable
                  as String,
        currentType: null == currentType
            ? _self.currentType
            : currentType // ignore: cast_nullable_to_non_nullable
                  as int,
        evmWallet: null == evmWallet
            ? _self.evmWallet
            : evmWallet // ignore: cast_nullable_to_non_nullable
                  as String,
        handle: null == handle
            ? _self.handle
            : handle // ignore: cast_nullable_to_non_nullable
                  as String,
        integral: null == integral
            ? _self.integral
            : integral // ignore: cast_nullable_to_non_nullable
                  as int,
        lastMessageId: null == lastMessageId
            ? _self.lastMessageId
            : lastMessageId // ignore: cast_nullable_to_non_nullable
                  as int,
        latestMessageId: null == latestMessageId
            ? _self.latestMessageId
            : latestMessageId // ignore: cast_nullable_to_non_nullable
                  as int,
        name: null == name
            ? _self.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        profileMode: null == profileMode
            ? _self.profileMode
            : profileMode // ignore: cast_nullable_to_non_nullable
                  as ProfileMode,
        redirectUrl: null == redirectUrl
            ? _self.redirectUrl
            : redirectUrl // ignore: cast_nullable_to_non_nullable
                  as String,
        referralCode: null == referralCode
            ? _self.referralCode
            : referralCode // ignore: cast_nullable_to_non_nullable
                  as String,
        title: null == title
            ? _self.title
            : title // ignore: cast_nullable_to_non_nullable
                  as String,
        userEmail: null == userEmail
            ? _self.userEmail
            : userEmail // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
mixin _$Group {
  @JsonKey(name: 'uniqId')
  String get id;
  @JsonKey(name: 'name')
  String get name;
  @JsonKey(name: 'description')
  String get description;
  @JsonKey(name: 'logo')
  String get logo;
  @JsonKey(name: 'userCount')
  int get userCount;
  @JsonKey(name: 'creator')
  String get creatorName;

  /// Create a copy of Group
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $GroupCopyWith<Group> get copyWith =>
      _$GroupCopyWithImpl<Group>(this as Group, _$identity);

  /// Serializes this Group to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is Group &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.logo, logo) || other.logo == logo) &&
            (identical(other.userCount, userCount) ||
                other.userCount == userCount) &&
            (identical(other.creatorName, creatorName) ||
                other.creatorName == creatorName));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    name,
    description,
    logo,
    userCount,
    creatorName,
  );

  @override
  String toString() {
    return 'Group(id: $id, name: $name, description: $description, logo: $logo, userCount: $userCount, creatorName: $creatorName)';
  }
}

/// @nodoc
abstract mixin class $GroupCopyWith<$Res> {
  factory $GroupCopyWith(Group value, $Res Function(Group) _then) =
      _$GroupCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'uniqId') String id,
    @JsonKey(name: 'name') String name,
    @JsonKey(name: 'description') String description,
    @JsonKey(name: 'logo') String logo,
    @JsonKey(name: 'userCount') int userCount,
    @JsonKey(name: 'creator') String creatorName,
  });
}

/// @nodoc
class _$GroupCopyWithImpl<$Res> implements $GroupCopyWith<$Res> {
  _$GroupCopyWithImpl(this._self, this._then);

  final Group _self;
  final $Res Function(Group) _then;

  /// Create a copy of Group
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = null,
    Object? logo = null,
    Object? userCount = null,
    Object? creatorName = null,
  }) {
    return _then(
      _self.copyWith(
        id: null == id
            ? _self.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _self.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        description: null == description
            ? _self.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String,
        logo: null == logo
            ? _self.logo
            : logo // ignore: cast_nullable_to_non_nullable
                  as String,
        userCount: null == userCount
            ? _self.userCount
            : userCount // ignore: cast_nullable_to_non_nullable
                  as int,
        creatorName: null == creatorName
            ? _self.creatorName
            : creatorName // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _Group implements Group {
  const _Group({
    @JsonKey(name: 'uniqId') required this.id,
    @JsonKey(name: 'name') required this.name,
    @JsonKey(name: 'description') required this.description,
    @JsonKey(name: 'logo') this.logo = '',
    @JsonKey(name: 'userCount') this.userCount = 1,
    @JsonKey(name: 'creator') required this.creatorName,
  });
  factory _Group.fromJson(Map<String, dynamic> json) => _$GroupFromJson(json);

  @override
  @JsonKey(name: 'uniqId')
  final String id;
  @override
  @JsonKey(name: 'name')
  final String name;
  @override
  @JsonKey(name: 'description')
  final String description;
  @override
  @JsonKey(name: 'logo')
  final String logo;
  @override
  @JsonKey(name: 'userCount')
  final int userCount;
  @override
  @JsonKey(name: 'creator')
  final String creatorName;

  /// Create a copy of Group
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$GroupCopyWith<_Group> get copyWith =>
      __$GroupCopyWithImpl<_Group>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$GroupToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Group &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.logo, logo) || other.logo == logo) &&
            (identical(other.userCount, userCount) ||
                other.userCount == userCount) &&
            (identical(other.creatorName, creatorName) ||
                other.creatorName == creatorName));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    name,
    description,
    logo,
    userCount,
    creatorName,
  );

  @override
  String toString() {
    return 'Group(id: $id, name: $name, description: $description, logo: $logo, userCount: $userCount, creatorName: $creatorName)';
  }
}

/// @nodoc
abstract mixin class _$GroupCopyWith<$Res> implements $GroupCopyWith<$Res> {
  factory _$GroupCopyWith(_Group value, $Res Function(_Group) _then) =
      __$GroupCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'uniqId') String id,
    @JsonKey(name: 'name') String name,
    @JsonKey(name: 'description') String description,
    @JsonKey(name: 'logo') String logo,
    @JsonKey(name: 'userCount') int userCount,
    @JsonKey(name: 'creator') String creatorName,
  });
}

/// @nodoc
class __$GroupCopyWithImpl<$Res> implements _$GroupCopyWith<$Res> {
  __$GroupCopyWithImpl(this._self, this._then);

  final _Group _self;
  final $Res Function(_Group) _then;

  /// Create a copy of Group
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = null,
    Object? logo = null,
    Object? userCount = null,
    Object? creatorName = null,
  }) {
    return _then(
      _Group(
        id: null == id
            ? _self.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _self.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        description: null == description
            ? _self.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String,
        logo: null == logo
            ? _self.logo
            : logo // ignore: cast_nullable_to_non_nullable
                  as String,
        userCount: null == userCount
            ? _self.userCount
            : userCount // ignore: cast_nullable_to_non_nullable
                  as int,
        creatorName: null == creatorName
            ? _self.creatorName
            : creatorName // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
mixin _$UserSettingsRequest {
  @JsonKey(name: 'fcmAndroid', includeIfNull: false)
  String? get fcmAndroid;
  @JsonKey(name: 'fcmIos', includeIfNull: false)
  String? get fcmIOS;

  /// Create a copy of UserSettingsRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $UserSettingsRequestCopyWith<UserSettingsRequest> get copyWith =>
      _$UserSettingsRequestCopyWithImpl<UserSettingsRequest>(
        this as UserSettingsRequest,
        _$identity,
      );

  /// Serializes this UserSettingsRequest to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is UserSettingsRequest &&
            (identical(other.fcmAndroid, fcmAndroid) ||
                other.fcmAndroid == fcmAndroid) &&
            (identical(other.fcmIOS, fcmIOS) || other.fcmIOS == fcmIOS));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, fcmAndroid, fcmIOS);

  @override
  String toString() {
    return 'UserSettingsRequest(fcmAndroid: $fcmAndroid, fcmIOS: $fcmIOS)';
  }
}

/// @nodoc
abstract mixin class $UserSettingsRequestCopyWith<$Res> {
  factory $UserSettingsRequestCopyWith(
    UserSettingsRequest value,
    $Res Function(UserSettingsRequest) _then,
  ) = _$UserSettingsRequestCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'fcmAndroid', includeIfNull: false) String? fcmAndroid,
    @JsonKey(name: 'fcmIos', includeIfNull: false) String? fcmIOS,
  });
}

/// @nodoc
class _$UserSettingsRequestCopyWithImpl<$Res>
    implements $UserSettingsRequestCopyWith<$Res> {
  _$UserSettingsRequestCopyWithImpl(this._self, this._then);

  final UserSettingsRequest _self;
  final $Res Function(UserSettingsRequest) _then;

  /// Create a copy of UserSettingsRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? fcmAndroid = freezed, Object? fcmIOS = freezed}) {
    return _then(
      _self.copyWith(
        fcmAndroid: freezed == fcmAndroid
            ? _self.fcmAndroid
            : fcmAndroid // ignore: cast_nullable_to_non_nullable
                  as String?,
        fcmIOS: freezed == fcmIOS
            ? _self.fcmIOS
            : fcmIOS // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _UserSettingsRequest implements UserSettingsRequest {
  const _UserSettingsRequest({
    @JsonKey(name: 'fcmAndroid', includeIfNull: false) this.fcmAndroid,
    @JsonKey(name: 'fcmIos', includeIfNull: false) this.fcmIOS,
  });
  factory _UserSettingsRequest.fromJson(Map<String, dynamic> json) =>
      _$UserSettingsRequestFromJson(json);

  @override
  @JsonKey(name: 'fcmAndroid', includeIfNull: false)
  final String? fcmAndroid;
  @override
  @JsonKey(name: 'fcmIos', includeIfNull: false)
  final String? fcmIOS;

  /// Create a copy of UserSettingsRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$UserSettingsRequestCopyWith<_UserSettingsRequest> get copyWith =>
      __$UserSettingsRequestCopyWithImpl<_UserSettingsRequest>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$UserSettingsRequestToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _UserSettingsRequest &&
            (identical(other.fcmAndroid, fcmAndroid) ||
                other.fcmAndroid == fcmAndroid) &&
            (identical(other.fcmIOS, fcmIOS) || other.fcmIOS == fcmIOS));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, fcmAndroid, fcmIOS);

  @override
  String toString() {
    return 'UserSettingsRequest(fcmAndroid: $fcmAndroid, fcmIOS: $fcmIOS)';
  }
}

/// @nodoc
abstract mixin class _$UserSettingsRequestCopyWith<$Res>
    implements $UserSettingsRequestCopyWith<$Res> {
  factory _$UserSettingsRequestCopyWith(
    _UserSettingsRequest value,
    $Res Function(_UserSettingsRequest) _then,
  ) = __$UserSettingsRequestCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'fcmAndroid', includeIfNull: false) String? fcmAndroid,
    @JsonKey(name: 'fcmIos', includeIfNull: false) String? fcmIOS,
  });
}

/// @nodoc
class __$UserSettingsRequestCopyWithImpl<$Res>
    implements _$UserSettingsRequestCopyWith<$Res> {
  __$UserSettingsRequestCopyWithImpl(this._self, this._then);

  final _UserSettingsRequest _self;
  final $Res Function(_UserSettingsRequest) _then;

  /// Create a copy of UserSettingsRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({Object? fcmAndroid = freezed, Object? fcmIOS = freezed}) {
    return _then(
      _UserSettingsRequest(
        fcmAndroid: freezed == fcmAndroid
            ? _self.fcmAndroid
            : fcmAndroid // ignore: cast_nullable_to_non_nullable
                  as String?,
        fcmIOS: freezed == fcmIOS
            ? _self.fcmIOS
            : fcmIOS // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc
mixin _$UserRelation {
  @JsonKey(name: 'following')
  bool get following;
  @JsonKey(name: 'followedBy')
  bool get followedBy;

  /// Create a copy of UserRelation
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $UserRelationCopyWith<UserRelation> get copyWith =>
      _$UserRelationCopyWithImpl<UserRelation>(
        this as UserRelation,
        _$identity,
      );

  /// Serializes this UserRelation to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is UserRelation &&
            (identical(other.following, following) ||
                other.following == following) &&
            (identical(other.followedBy, followedBy) ||
                other.followedBy == followedBy));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, following, followedBy);

  @override
  String toString() {
    return 'UserRelation(following: $following, followedBy: $followedBy)';
  }
}

/// @nodoc
abstract mixin class $UserRelationCopyWith<$Res> {
  factory $UserRelationCopyWith(
    UserRelation value,
    $Res Function(UserRelation) _then,
  ) = _$UserRelationCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'following') bool following,
    @JsonKey(name: 'followedBy') bool followedBy,
  });
}

/// @nodoc
class _$UserRelationCopyWithImpl<$Res> implements $UserRelationCopyWith<$Res> {
  _$UserRelationCopyWithImpl(this._self, this._then);

  final UserRelation _self;
  final $Res Function(UserRelation) _then;

  /// Create a copy of UserRelation
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? following = null, Object? followedBy = null}) {
    return _then(
      _self.copyWith(
        following: null == following
            ? _self.following
            : following // ignore: cast_nullable_to_non_nullable
                  as bool,
        followedBy: null == followedBy
            ? _self.followedBy
            : followedBy // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _UserRelation implements UserRelation {
  const _UserRelation({
    @JsonKey(name: 'following') this.following = false,
    @JsonKey(name: 'followedBy') this.followedBy = false,
  });
  factory _UserRelation.fromJson(Map<String, dynamic> json) =>
      _$UserRelationFromJson(json);

  @override
  @JsonKey(name: 'following')
  final bool following;
  @override
  @JsonKey(name: 'followedBy')
  final bool followedBy;

  /// Create a copy of UserRelation
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$UserRelationCopyWith<_UserRelation> get copyWith =>
      __$UserRelationCopyWithImpl<_UserRelation>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$UserRelationToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _UserRelation &&
            (identical(other.following, following) ||
                other.following == following) &&
            (identical(other.followedBy, followedBy) ||
                other.followedBy == followedBy));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, following, followedBy);

  @override
  String toString() {
    return 'UserRelation(following: $following, followedBy: $followedBy)';
  }
}

/// @nodoc
abstract mixin class _$UserRelationCopyWith<$Res>
    implements $UserRelationCopyWith<$Res> {
  factory _$UserRelationCopyWith(
    _UserRelation value,
    $Res Function(_UserRelation) _then,
  ) = __$UserRelationCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'following') bool following,
    @JsonKey(name: 'followedBy') bool followedBy,
  });
}

/// @nodoc
class __$UserRelationCopyWithImpl<$Res>
    implements _$UserRelationCopyWith<$Res> {
  __$UserRelationCopyWithImpl(this._self, this._then);

  final _UserRelation _self;
  final $Res Function(_UserRelation) _then;

  /// Create a copy of UserRelation
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({Object? following = null, Object? followedBy = null}) {
    return _then(
      _UserRelation(
        following: null == following
            ? _self.following
            : following // ignore: cast_nullable_to_non_nullable
                  as bool,
        followedBy: null == followedBy
            ? _self.followedBy
            : followedBy // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}

/// @nodoc
mixin _$UserFromRelation {
  @JsonKey(name: 'referralCode')
  String get referralCode;
  @JsonKey(name: 'name')
  String get name;
  @JsonKey(name: 'title')
  String get title;
  @JsonKey(name: 'company')
  String get company;
  @JsonKey(name: 'avatar')
  String get avatar;
  @JsonKey(name: 'following')
  bool get following;
  @JsonKey(name: 'followedBy')
  bool get followedBy;

  /// Create a copy of UserFromRelation
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $UserFromRelationCopyWith<UserFromRelation> get copyWith =>
      _$UserFromRelationCopyWithImpl<UserFromRelation>(
        this as UserFromRelation,
        _$identity,
      );

  /// Serializes this UserFromRelation to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is UserFromRelation &&
            (identical(other.referralCode, referralCode) ||
                other.referralCode == referralCode) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.company, company) || other.company == company) &&
            (identical(other.avatar, avatar) || other.avatar == avatar) &&
            (identical(other.following, following) ||
                other.following == following) &&
            (identical(other.followedBy, followedBy) ||
                other.followedBy == followedBy));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    referralCode,
    name,
    title,
    company,
    avatar,
    following,
    followedBy,
  );

  @override
  String toString() {
    return 'UserFromRelation(referralCode: $referralCode, name: $name, title: $title, company: $company, avatar: $avatar, following: $following, followedBy: $followedBy)';
  }
}

/// @nodoc
abstract mixin class $UserFromRelationCopyWith<$Res> {
  factory $UserFromRelationCopyWith(
    UserFromRelation value,
    $Res Function(UserFromRelation) _then,
  ) = _$UserFromRelationCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'referralCode') String referralCode,
    @JsonKey(name: 'name') String name,
    @JsonKey(name: 'title') String title,
    @JsonKey(name: 'company') String company,
    @JsonKey(name: 'avatar') String avatar,
    @JsonKey(name: 'following') bool following,
    @JsonKey(name: 'followedBy') bool followedBy,
  });
}

/// @nodoc
class _$UserFromRelationCopyWithImpl<$Res>
    implements $UserFromRelationCopyWith<$Res> {
  _$UserFromRelationCopyWithImpl(this._self, this._then);

  final UserFromRelation _self;
  final $Res Function(UserFromRelation) _then;

  /// Create a copy of UserFromRelation
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? referralCode = null,
    Object? name = null,
    Object? title = null,
    Object? company = null,
    Object? avatar = null,
    Object? following = null,
    Object? followedBy = null,
  }) {
    return _then(
      _self.copyWith(
        referralCode: null == referralCode
            ? _self.referralCode
            : referralCode // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _self.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        title: null == title
            ? _self.title
            : title // ignore: cast_nullable_to_non_nullable
                  as String,
        company: null == company
            ? _self.company
            : company // ignore: cast_nullable_to_non_nullable
                  as String,
        avatar: null == avatar
            ? _self.avatar
            : avatar // ignore: cast_nullable_to_non_nullable
                  as String,
        following: null == following
            ? _self.following
            : following // ignore: cast_nullable_to_non_nullable
                  as bool,
        followedBy: null == followedBy
            ? _self.followedBy
            : followedBy // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _UserFromRelation implements UserFromRelation {
  const _UserFromRelation({
    @JsonKey(name: 'referralCode') required this.referralCode,
    @JsonKey(name: 'name') this.name = '',
    @JsonKey(name: 'title') this.title = '',
    @JsonKey(name: 'company') this.company = '',
    @JsonKey(name: 'avatar') this.avatar = '',
    @JsonKey(name: 'following') this.following = false,
    @JsonKey(name: 'followedBy') this.followedBy = false,
  });
  factory _UserFromRelation.fromJson(Map<String, dynamic> json) =>
      _$UserFromRelationFromJson(json);

  @override
  @JsonKey(name: 'referralCode')
  final String referralCode;
  @override
  @JsonKey(name: 'name')
  final String name;
  @override
  @JsonKey(name: 'title')
  final String title;
  @override
  @JsonKey(name: 'company')
  final String company;
  @override
  @JsonKey(name: 'avatar')
  final String avatar;
  @override
  @JsonKey(name: 'following')
  final bool following;
  @override
  @JsonKey(name: 'followedBy')
  final bool followedBy;

  /// Create a copy of UserFromRelation
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$UserFromRelationCopyWith<_UserFromRelation> get copyWith =>
      __$UserFromRelationCopyWithImpl<_UserFromRelation>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$UserFromRelationToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _UserFromRelation &&
            (identical(other.referralCode, referralCode) ||
                other.referralCode == referralCode) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.company, company) || other.company == company) &&
            (identical(other.avatar, avatar) || other.avatar == avatar) &&
            (identical(other.following, following) ||
                other.following == following) &&
            (identical(other.followedBy, followedBy) ||
                other.followedBy == followedBy));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    referralCode,
    name,
    title,
    company,
    avatar,
    following,
    followedBy,
  );

  @override
  String toString() {
    return 'UserFromRelation(referralCode: $referralCode, name: $name, title: $title, company: $company, avatar: $avatar, following: $following, followedBy: $followedBy)';
  }
}

/// @nodoc
abstract mixin class _$UserFromRelationCopyWith<$Res>
    implements $UserFromRelationCopyWith<$Res> {
  factory _$UserFromRelationCopyWith(
    _UserFromRelation value,
    $Res Function(_UserFromRelation) _then,
  ) = __$UserFromRelationCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'referralCode') String referralCode,
    @JsonKey(name: 'name') String name,
    @JsonKey(name: 'title') String title,
    @JsonKey(name: 'company') String company,
    @JsonKey(name: 'avatar') String avatar,
    @JsonKey(name: 'following') bool following,
    @JsonKey(name: 'followedBy') bool followedBy,
  });
}

/// @nodoc
class __$UserFromRelationCopyWithImpl<$Res>
    implements _$UserFromRelationCopyWith<$Res> {
  __$UserFromRelationCopyWithImpl(this._self, this._then);

  final _UserFromRelation _self;
  final $Res Function(_UserFromRelation) _then;

  /// Create a copy of UserFromRelation
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? referralCode = null,
    Object? name = null,
    Object? title = null,
    Object? company = null,
    Object? avatar = null,
    Object? following = null,
    Object? followedBy = null,
  }) {
    return _then(
      _UserFromRelation(
        referralCode: null == referralCode
            ? _self.referralCode
            : referralCode // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _self.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        title: null == title
            ? _self.title
            : title // ignore: cast_nullable_to_non_nullable
                  as String,
        company: null == company
            ? _self.company
            : company // ignore: cast_nullable_to_non_nullable
                  as String,
        avatar: null == avatar
            ? _self.avatar
            : avatar // ignore: cast_nullable_to_non_nullable
                  as String,
        following: null == following
            ? _self.following
            : following // ignore: cast_nullable_to_non_nullable
                  as bool,
        followedBy: null == followedBy
            ? _self.followedBy
            : followedBy // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}
