import 'package:decimal/decimal.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import 'user.dart' show ProfileMode;

part 'business.freezed.dart';

part 'business.g.dart';

class ChainConfig {
  const ChainConfig({
    required this.name,
    required this.rpcUrl,
    required this.chainId,
    required this.nativeCurrency,
    required this.blockExplorer,
  });

  final String name;
  final String rpcUrl;
  final int chainId;
  final String nativeCurrency;
  final String blockExplorer;
}

// 区块浏览器信息
@freezed
sealed class BlockExplorer with _$BlockExplorer {
  const factory BlockExplorer({
    @J<PERSON><PERSON>ey(name: 'explorerName') required String explorerName,
    @JsonKey(name: 'url') required String url,
  }) = _BlockExplorer;

  factory BlockExplorer.fromJson(Map<String, dynamic> json) => _$BlockExplorerFromJson(json);
}

// ENS信息
@freezed
sealed class Ens with _$Ens {
  const factory Ens({
    @Json<PERSON><PERSON>(name: 'address') required String address,
    @JsonKey(name: 'blockCreated') required int blockCreated,
  }) = _Ens;

  factory Ens.fromJson(Map<String, dynamic> json) => _$EnsFromJson(json);
}

// 原生货币信息
@freezed
sealed class NativeCurrency with _$NativeCurrency {
  const factory NativeCurrency({
    @JsonKey(name: 'decimals') required int decimals,
    @JsonKey(name: 'name') required String name,
    @JsonKey(name: 'symbol') required String symbol,
  }) = _NativeCurrency;

  factory NativeCurrency.fromJson(Map<String, dynamic> json) => _$NativeCurrencyFromJson(json);
}

// RPC提供商信息
@freezed
sealed class RPCProvider with _$RPCProvider {
  const factory RPCProvider({
    @JsonKey(name: 'providerName') @Default('') String providerName,
    @JsonKey(name: 'url') required String url,
    @JsonKey(name: 'websocket') String? websocket,
  }) = _RPCProvider;

  factory RPCProvider.fromJson(Map<String, dynamic> json) => _$RPCProviderFromJson(json);
}

// 网络信息主模型
@freezed
sealed class Network with _$Network {
  const factory Network({
    @JsonKey(name: 'blockExplorers') @Default([]) List<BlockExplorer> blockExplorers,
    @JsonKey(name: 'ens') @Default(Ens(address: '', blockCreated: 0)) Ens ens,
    @JsonKey(name: 'iconUrl') @Default('') String iconUrl,
    @JsonKey(name: 'id') @Default(0) int id,
    @JsonKey(name: 'isEIP1559') @Default(false) bool isEIP1559,
    @JsonKey(name: 'name') @Default('') String name,
    @JsonKey(name: 'nativeCurrency')
    @Default(NativeCurrency(decimals: 0, name: '', symbol: ''))
    NativeCurrency nativeCurrency,
    @JsonKey(name: 'network') @Default('') String network,
    @JsonKey(name: 'networkType') @Default('') String networkType,
    @JsonKey(name: 'nftEnable') @Default(false) bool nftEnable,
    @JsonKey(name: 'rpcProviders') @Default([]) List<RPCProvider> rpcProviders,
    @JsonKey(name: 'shortName') @Default('') String shortName,
    @JsonKey(name: 'weight') @Default(0) int weight,
    @JsonKey(name: 'testnet') @Default(false) bool testnet,
    @JsonKey(name: 'chainIndexOkx') @Default('') String chainIndexOKX,
  }) = _Network;

  const Network._();

  factory Network.fromJson(Map<String, dynamic> json) => _$NetworkFromJson(json);
}

abstract class IToken {
  String get chainName;

  String get chainIdOKX;

  String get symbol;

  String get name;

  String get logo;

  String get address;

  int get decimals;

  Decimal get balance;

  Decimal get realBalance;

  Decimal get priceUsd;

  Decimal get valueUsd;

  bool get isSOL;

  Map<String, dynamic> toJson();
}

@freezed
sealed class TokenAmount with _$TokenAmount {
  const factory TokenAmount({
    required Decimal amount,
    required int decimals,
  }) = _TokenAmount;

  const TokenAmount._();

  bool get isZero => amount == Decimal.zero;

  bool get hasValue => !isZero;

  Decimal get realBalance => amount.shift(-decimals);
}

@JsonEnum(valueField: 'value')
enum MessageType {
  unknown(''),
  text('TEXT'),
  push('PUSH'),
  sayHi('SAYHI'),
  follow('FOLLOW'),
  airdrop('AIRDROP'),
  integral('INTEGRAL');

  const MessageType(this.value);

  final String value;
}

class MessageTypeConverter implements JsonConverter<MessageType, String> {
  const MessageTypeConverter();

  @override
  MessageType fromJson(String json) {
    return MessageType.values.firstWhere(
      (e) => e.value == json,
      orElse: () => MessageType.unknown,
    );
  }

  @override
  String toJson(MessageType object) {
    return object.value;
  }
}

@freezed
sealed class Message with _$Message {
  const factory Message({
    @JsonKey(name: 'createTime') required String createTime,
    @JsonKey(name: 'id') required int id,
    @JsonKey(name: 'message') required String message,
    @JsonKey(name: 'params') @Default('') String params,
    @JsonKey(name: 'messageType') @MessageTypeConverter() required MessageType messageType,
  }) = _Message;

  factory Message.fromJson(Map<String, dynamic> json) => _$MessageFromJson(json);
}

@freezed
sealed class MessageSayHiParams with _$MessageSayHiParams {
  const factory MessageSayHiParams({
    @JsonKey(name: 'name') @Default('') String name,
    @JsonKey(name: 'email') @Default('') String email,
    @JsonKey(name: 'avatar') @Default('') String avatar,
    @JsonKey(name: 'title') @Default('') String title,
    @JsonKey(name: 'company') @Default('') String company,
    @JsonKey(name: 'referralCode') @Default('') String referralCode,
  }) = _MessageSayHiParams;

  factory MessageSayHiParams.fromJson(Map<String, dynamic> json) => _$MessageSayHiParamsFromJson(json);
}

@freezed
sealed class Point with _$Point {
  const factory Point({
    @JsonKey(name: 'action') @Default('') String action,
    @JsonKey(name: 'createTime') required String createTime,
    @JsonKey(name: 'description') required String description,
    @JsonKey(name: 'integral') required int integral,
    @JsonKey(name: 'ip') @Default('') String ip,
    @JsonKey(name: 'id') required int id,
    @JsonKey(name: 'contentId') @Default(0) int contentId,
    @JsonKey(name: 'contentType') @Default('') String contentType,
    @JsonKey(name: 'userId') required int userId,
  }) = _Point;

  factory Point.fromJson(Map<String, dynamic> json) => _$PointFromJson(json);
}

@JsonEnum(valueField: 'value')
enum VerifyStatus {
  notSure('NOT_SURE'),
  verified('VERIFIED'),
  unverified('UNVERIFIED');

  const VerifyStatus(this.value);

  final String value;
}

@freezed
sealed class Connection with _$Connection {
  const factory Connection({
    @JsonKey(name: 'cardCode') @Default('') String cardCode,
    @JsonKey(name: 'city') @Default('') String city,
    @JsonKey(name: 'image') @Default('') String image,
    @JsonKey(name: 'createTime') @Default('') String createTime,
    @JsonKey(name: 'name') @Default('') String name,
    @JsonKey(name: 'senderAvatar') @Default('') String senderAvatar,
    @JsonKey(name: 'twitter') @Default('') String twitter,
    @JsonKey(name: 'uniqId') @Default('') String uniqId,
    @JsonKey(name: 'verifyStatus') @Default(VerifyStatus.notSure) VerifyStatus verifyStatus,
  }) = _Connection;

  factory Connection.fromJson(Map<String, dynamic> json) => _$ConnectionFromJson(json);
}

@freezed
sealed class ReferralLog with _$ReferralLog {
  const factory ReferralLog({
    @JsonKey(name: 'createTime') @Default('') String createTime,
    @JsonKey(name: 'integral') @Default(0) int integral,
    @JsonKey(name: 'inviteeEmail') @Default('') String inviteeEmail,
  }) = _ReferralLog;

  factory ReferralLog.fromJson(Map<String, dynamic> json) => _$ReferralLogFromJson(json);
}

@freezed
sealed class EventItem with _$EventItem {
  const factory EventItem({
    @JsonKey(name: 'title') required String title,
    @JsonKey(name: 'desc') required String desc,
    @JsonKey(name: 'image') required String image,
    @JsonKey(name: 'imageWidth') @Default('') String imageWidth,
    @JsonKey(name: 'link') required String link,
    @JsonKey(name: 'span') @Default(1) int? span,
    @JsonKey(name: 'sort') @Default(0) int? sort,
    @JsonKey(name: 'eventIds') @Default([]) List<int> eventIds,
    @JsonKey(name: 'category') @Default('') String eventType,
    @JsonKey(name: 'isNative') @Default(false) bool isNative,
    @JsonKey(name: 'blendMode') @Default(false) bool blendMode,
  }) = _EventItem;

  factory EventItem.fromJson(Map<String, dynamic> json) => _$EventItemFromJson(json);
}

@freezed
sealed class Config with _$Config {
  const factory Config({
    /// chainTokenFilters 示例:
    /// {
    ///   "1": ["ETH", "USDC", "USDT"],
    ///   "8453": ["ETH", "USDT", "USDC", "DEGEN"],
    ///   ...
    /// }
    @JsonKey(name: 'chainTokenFilters') required Map<String, List<String>> chainTokenFilters,

    /// 支持的链ID列表
    /// 例如: [10, 324, 59144]
    @JsonKey(name: 'chainFilters') required List<int> chainFilters,

    /// 以太坊社区活动ID列表
    /// 例如: [10, 324, 59144]
    @JsonKey(name: 'ethccEventIds') @Default([]) List<int> ethccEventIds,

    /// 默认模式
    @JsonKey(name: 'defaultMode') @Default(ProfileMode.ETHCC) ProfileMode defaultMode,

    /// 是否支持NFC
    @JsonKey(name: 'nfc') @Default(false) bool nfc,

    /// 是否开启引导
    @JsonKey(name: 'activationGuide') @Default(false) bool activationGuide,

    /// 是否支持OKX
    @JsonKey(name: 'okx') @Default(false) bool okx,

    /// 事件列表
    ///
    @JsonKey(name: 'events') required List<EventItem> events,
  }) = _Config;

  const Config._();

  factory Config.fromJson(Map<String, dynamic> json) => _$ConfigFromJson(json);

  bool isNetworkSupported(int networkId) => chainFilters.contains(networkId);
}
