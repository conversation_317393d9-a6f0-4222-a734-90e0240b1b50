// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_UserInfo _$UserInfoFromJson(Map json) => _UserInfo(
  avatar: json['avatar'] as String? ?? '',
  bannerHref: json['bannerHref'] as String? ?? '',
  bannerImage: json['bannerImage'] as String? ?? '',
  cardCode: json['cardCode'] as String? ?? '',
  company: json['company'] as String? ?? '',
  currentType: (json['currentType'] as num?)?.toInt() ?? 0,
  evmWallet: json['evmWallet'] as String? ?? '',
  handle: json['handle'] as String? ?? '',
  integral: (json['integral'] as num?)?.toInt() ?? 0,
  lastMessageId: (json['lastMessageId'] as num?)?.toInt() ?? 0,
  latestMessageId: (json['latestMessageId'] as num?)?.toInt() ?? 0,
  name: json['name'] as String? ?? '',
  profileMode:
      $enumDecodeNullable(_$ProfileModeEnumMap, json['profileMode']) ??
      ProfileMode.EMPTY,
  redirectUrl: json['redirectUrl'] as String? ?? '',
  referralCode: json['referralCode'] as String? ?? '',
  title: json['title'] as String? ?? '',
  userEmail: json['userEmail'] as String? ?? '',
);

Map<String, dynamic> _$UserInfoToJson(_UserInfo instance) => <String, dynamic>{
  'avatar': instance.avatar,
  'bannerHref': instance.bannerHref,
  'bannerImage': instance.bannerImage,
  'cardCode': instance.cardCode,
  'company': instance.company,
  'currentType': instance.currentType,
  'evmWallet': instance.evmWallet,
  'handle': instance.handle,
  'integral': instance.integral,
  'lastMessageId': instance.lastMessageId,
  'latestMessageId': instance.latestMessageId,
  'name': instance.name,
  'profileMode': _$ProfileModeEnumMap[instance.profileMode]!,
  'redirectUrl': instance.redirectUrl,
  'referralCode': instance.referralCode,
  'title': instance.title,
  'userEmail': instance.userEmail,
};

const _$ProfileModeEnumMap = {
  ProfileMode.DEFAULT: 'DEFAULT',
  ProfileMode.ETHCC: 'ETHCC',
  ProfileMode.EMPTY: '',
};

_Group _$GroupFromJson(Map json) => _Group(
  id: json['uniqId'] as String,
  name: json['name'] as String,
  description: json['description'] as String,
  logo: json['logo'] as String? ?? '',
  userCount: (json['userCount'] as num?)?.toInt() ?? 1,
  creatorName: json['creator'] as String,
);

Map<String, dynamic> _$GroupToJson(_Group instance) => <String, dynamic>{
  'uniqId': instance.id,
  'name': instance.name,
  'description': instance.description,
  'logo': instance.logo,
  'userCount': instance.userCount,
  'creator': instance.creatorName,
};

_UserSettingsRequest _$UserSettingsRequestFromJson(Map json) =>
    _UserSettingsRequest(
      fcmAndroid: json['fcmAndroid'] as String?,
      fcmIOS: json['fcmIos'] as String?,
    );

Map<String, dynamic> _$UserSettingsRequestToJson(
  _UserSettingsRequest instance,
) => <String, dynamic>{
  if (instance.fcmAndroid case final value?) 'fcmAndroid': value,
  if (instance.fcmIOS case final value?) 'fcmIos': value,
};

_UserRelation _$UserRelationFromJson(Map json) => _UserRelation(
  following: json['following'] as bool? ?? false,
  followedBy: json['followedBy'] as bool? ?? false,
);

Map<String, dynamic> _$UserRelationToJson(_UserRelation instance) =>
    <String, dynamic>{
      'following': instance.following,
      'followedBy': instance.followedBy,
    };

_UserFromRelation _$UserFromRelationFromJson(Map json) => _UserFromRelation(
  referralCode: json['referralCode'] as String,
  name: json['name'] as String? ?? '',
  title: json['title'] as String? ?? '',
  company: json['company'] as String? ?? '',
  avatar: json['avatar'] as String? ?? '',
  following: json['following'] as bool? ?? false,
  followedBy: json['followedBy'] as bool? ?? false,
);

Map<String, dynamic> _$UserFromRelationToJson(_UserFromRelation instance) =>
    <String, dynamic>{
      'referralCode': instance.referralCode,
      'name': instance.name,
      'title': instance.title,
      'company': instance.company,
      'avatar': instance.avatar,
      'following': instance.following,
      'followedBy': instance.followedBy,
    };
