// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'business.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_BlockExplorer _$BlockExplorerFromJson(Map json) => _BlockExplorer(
  explorerName: json['explorerName'] as String,
  url: json['url'] as String,
);

Map<String, dynamic> _$BlockExplorerToJson(_BlockExplorer instance) =>
    <String, dynamic>{
      'explorerName': instance.explorerName,
      'url': instance.url,
    };

_Ens _$EnsFromJson(Map json) => _Ens(
  address: json['address'] as String,
  blockCreated: (json['blockCreated'] as num).toInt(),
);

Map<String, dynamic> _$EnsToJson(_Ens instance) => <String, dynamic>{
  'address': instance.address,
  'blockCreated': instance.blockCreated,
};

_NativeCurrency _$NativeCurrencyFromJson(Map json) => _NativeCurrency(
  decimals: (json['decimals'] as num).toInt(),
  name: json['name'] as String,
  symbol: json['symbol'] as String,
);

Map<String, dynamic> _$NativeCurrencyToJson(_NativeCurrency instance) =>
    <String, dynamic>{
      'decimals': instance.decimals,
      'name': instance.name,
      'symbol': instance.symbol,
    };

_RPCProvider _$RPCProviderFromJson(Map json) => _RPCProvider(
  providerName: json['providerName'] as String? ?? '',
  url: json['url'] as String,
  websocket: json['websocket'] as String?,
);

Map<String, dynamic> _$RPCProviderToJson(_RPCProvider instance) =>
    <String, dynamic>{
      'providerName': instance.providerName,
      'url': instance.url,
      'websocket': instance.websocket,
    };

_Network _$NetworkFromJson(Map json) => _Network(
  blockExplorers:
      (json['blockExplorers'] as List<dynamic>?)
          ?.map(
            (e) => BlockExplorer.fromJson(Map<String, dynamic>.from(e as Map)),
          )
          .toList() ??
      const [],
  ens: json['ens'] == null
      ? const Ens(address: '', blockCreated: 0)
      : Ens.fromJson(Map<String, dynamic>.from(json['ens'] as Map)),
  iconUrl: json['iconUrl'] as String? ?? '',
  id: (json['id'] as num?)?.toInt() ?? 0,
  isEIP1559: json['isEIP1559'] as bool? ?? false,
  name: json['name'] as String? ?? '',
  nativeCurrency: json['nativeCurrency'] == null
      ? const NativeCurrency(decimals: 0, name: '', symbol: '')
      : NativeCurrency.fromJson(
          Map<String, dynamic>.from(json['nativeCurrency'] as Map),
        ),
  network: json['network'] as String? ?? '',
  networkType: json['networkType'] as String? ?? '',
  nftEnable: json['nftEnable'] as bool? ?? false,
  rpcProviders:
      (json['rpcProviders'] as List<dynamic>?)
          ?.map(
            (e) => RPCProvider.fromJson(Map<String, dynamic>.from(e as Map)),
          )
          .toList() ??
      const [],
  shortName: json['shortName'] as String? ?? '',
  weight: (json['weight'] as num?)?.toInt() ?? 0,
  testnet: json['testnet'] as bool? ?? false,
  chainIndexOKX: json['chainIndexOkx'] as String? ?? '',
);

Map<String, dynamic> _$NetworkToJson(_Network instance) => <String, dynamic>{
  'blockExplorers': instance.blockExplorers.map((e) => e.toJson()).toList(),
  'ens': instance.ens.toJson(),
  'iconUrl': instance.iconUrl,
  'id': instance.id,
  'isEIP1559': instance.isEIP1559,
  'name': instance.name,
  'nativeCurrency': instance.nativeCurrency.toJson(),
  'network': instance.network,
  'networkType': instance.networkType,
  'nftEnable': instance.nftEnable,
  'rpcProviders': instance.rpcProviders.map((e) => e.toJson()).toList(),
  'shortName': instance.shortName,
  'weight': instance.weight,
  'testnet': instance.testnet,
  'chainIndexOkx': instance.chainIndexOKX,
};

_Message _$MessageFromJson(Map json) => _Message(
  createTime: json['createTime'] as String,
  id: (json['id'] as num).toInt(),
  message: json['message'] as String,
  params: json['params'] as String? ?? '',
  messageType: const MessageTypeConverter().fromJson(
    json['messageType'] as String,
  ),
);

Map<String, dynamic> _$MessageToJson(_Message instance) => <String, dynamic>{
  'createTime': instance.createTime,
  'id': instance.id,
  'message': instance.message,
  'params': instance.params,
  'messageType': const MessageTypeConverter().toJson(instance.messageType),
};

_MessageSayHiParams _$MessageSayHiParamsFromJson(Map json) =>
    _MessageSayHiParams(
      name: json['name'] as String? ?? '',
      email: json['email'] as String? ?? '',
      avatar: json['avatar'] as String? ?? '',
      title: json['title'] as String? ?? '',
      company: json['company'] as String? ?? '',
      referralCode: json['referralCode'] as String? ?? '',
    );

Map<String, dynamic> _$MessageSayHiParamsToJson(_MessageSayHiParams instance) =>
    <String, dynamic>{
      'name': instance.name,
      'email': instance.email,
      'avatar': instance.avatar,
      'title': instance.title,
      'company': instance.company,
      'referralCode': instance.referralCode,
    };

_Point _$PointFromJson(Map json) => _Point(
  action: json['action'] as String? ?? '',
  createTime: json['createTime'] as String,
  description: json['description'] as String,
  integral: (json['integral'] as num).toInt(),
  ip: json['ip'] as String? ?? '',
  id: (json['id'] as num).toInt(),
  contentId: (json['contentId'] as num?)?.toInt() ?? 0,
  contentType: json['contentType'] as String? ?? '',
  userId: (json['userId'] as num).toInt(),
);

Map<String, dynamic> _$PointToJson(_Point instance) => <String, dynamic>{
  'action': instance.action,
  'createTime': instance.createTime,
  'description': instance.description,
  'integral': instance.integral,
  'ip': instance.ip,
  'id': instance.id,
  'contentId': instance.contentId,
  'contentType': instance.contentType,
  'userId': instance.userId,
};

_Connection _$ConnectionFromJson(Map json) => _Connection(
  cardCode: json['cardCode'] as String? ?? '',
  city: json['city'] as String? ?? '',
  image: json['image'] as String? ?? '',
  createTime: json['createTime'] as String? ?? '',
  name: json['name'] as String? ?? '',
  senderAvatar: json['senderAvatar'] as String? ?? '',
  twitter: json['twitter'] as String? ?? '',
  uniqId: json['uniqId'] as String? ?? '',
  verifyStatus:
      $enumDecodeNullable(_$VerifyStatusEnumMap, json['verifyStatus']) ??
      VerifyStatus.notSure,
);

Map<String, dynamic> _$ConnectionToJson(_Connection instance) =>
    <String, dynamic>{
      'cardCode': instance.cardCode,
      'city': instance.city,
      'image': instance.image,
      'createTime': instance.createTime,
      'name': instance.name,
      'senderAvatar': instance.senderAvatar,
      'twitter': instance.twitter,
      'uniqId': instance.uniqId,
      'verifyStatus': _$VerifyStatusEnumMap[instance.verifyStatus]!,
    };

const _$VerifyStatusEnumMap = {
  VerifyStatus.notSure: 'NOT_SURE',
  VerifyStatus.verified: 'VERIFIED',
  VerifyStatus.unverified: 'UNVERIFIED',
};

_ReferralLog _$ReferralLogFromJson(Map json) => _ReferralLog(
  createTime: json['createTime'] as String? ?? '',
  integral: (json['integral'] as num?)?.toInt() ?? 0,
  inviteeEmail: json['inviteeEmail'] as String? ?? '',
);

Map<String, dynamic> _$ReferralLogToJson(_ReferralLog instance) =>
    <String, dynamic>{
      'createTime': instance.createTime,
      'integral': instance.integral,
      'inviteeEmail': instance.inviteeEmail,
    };

_EventItem _$EventItemFromJson(Map json) => _EventItem(
  title: json['title'] as String,
  desc: json['desc'] as String,
  image: json['image'] as String,
  imageWidth: json['imageWidth'] as String? ?? '',
  link: json['link'] as String,
  span: (json['span'] as num?)?.toInt() ?? 1,
  sort: (json['sort'] as num?)?.toInt() ?? 0,
  eventIds:
      (json['eventIds'] as List<dynamic>?)
          ?.map((e) => (e as num).toInt())
          .toList() ??
      const [],
  eventType: json['category'] as String? ?? '',
  isNative: json['isNative'] as bool? ?? false,
  blendMode: json['blendMode'] as bool? ?? false,
);

Map<String, dynamic> _$EventItemToJson(_EventItem instance) =>
    <String, dynamic>{
      'title': instance.title,
      'desc': instance.desc,
      'image': instance.image,
      'imageWidth': instance.imageWidth,
      'link': instance.link,
      'span': instance.span,
      'sort': instance.sort,
      'eventIds': instance.eventIds,
      'category': instance.eventType,
      'isNative': instance.isNative,
      'blendMode': instance.blendMode,
    };

_Config _$ConfigFromJson(Map json) => _Config(
  chainTokenFilters: (json['chainTokenFilters'] as Map).map(
    (k, e) => MapEntry(
      k as String,
      (e as List<dynamic>).map((e) => e as String).toList(),
    ),
  ),
  chainFilters: (json['chainFilters'] as List<dynamic>)
      .map((e) => (e as num).toInt())
      .toList(),
  ethccEventIds:
      (json['ethccEventIds'] as List<dynamic>?)
          ?.map((e) => (e as num).toInt())
          .toList() ??
      const [],
  defaultMode:
      $enumDecodeNullable(_$ProfileModeEnumMap, json['defaultMode']) ??
      ProfileMode.ETHCC,
  nfc: json['nfc'] as bool? ?? false,
  activationGuide: json['activationGuide'] as bool? ?? false,
  okx: json['okx'] as bool? ?? false,
  events: (json['events'] as List<dynamic>)
      .map((e) => EventItem.fromJson(Map<String, dynamic>.from(e as Map)))
      .toList(),
);

Map<String, dynamic> _$ConfigToJson(_Config instance) => <String, dynamic>{
  'chainTokenFilters': instance.chainTokenFilters,
  'chainFilters': instance.chainFilters,
  'ethccEventIds': instance.ethccEventIds,
  'defaultMode': _$ProfileModeEnumMap[instance.defaultMode]!,
  'nfc': instance.nfc,
  'activationGuide': instance.activationGuide,
  'okx': instance.okx,
  'events': instance.events.map((e) => e.toJson()).toList(),
};

const _$ProfileModeEnumMap = {
  ProfileMode.DEFAULT: 'DEFAULT',
  ProfileMode.ETHCC: 'ETHCC',
  ProfileMode.EMPTY: '',
};
