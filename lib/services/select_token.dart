import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:me_extensions/me_extensions.dart';
import 'package:me_ui/me_ui.dart' show MEImage, RippleTap, Gap;

import '/models/business.dart';
import '/provider/chain.dart' show networkProvider;
import '/provider/token.dart' show tokenBalanceStreamProvider;
import '/routes/card3_routes.dart' show Routes;
import '/ui/widgets/toast.dart' show Card3ToastUtil;

Future<void> showTokenSelection(
  BuildContext context,
  List<IToken> tokens,
  void Function(IToken) callback,
) async {
  if (tokens.isEmpty) {
    Card3ToastUtil.showToast(message: 'No available tokens');
    return;
  }

  final selectedToken = await showModalBottomSheet<IToken>(
    context: context,
    builder: (context) => Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        spacing: 16.0,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Select Token',
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                ),
              ),
              IconButton(
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
                icon: const Icon(Icons.close, color: Colors.black, size: 28),
                onPressed: () => Navigator.pop(context),
              ),
            ],
          ),
          Expanded(
            child: CustomScrollView(
              slivers: [
                DecoratedSliver(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(16),
                    color: context.theme.cardColor,
                  ),
                  sliver: SliverList.separated(
                    separatorBuilder: (_, _) => Gap.v(6.0, color: context.theme.scaffoldBackgroundColor),
                    itemCount: tokens.length,
                    itemBuilder: (context, index) {
                      final token = tokens[index];
                      return ProviderScope(
                        overrides: [_tokenItemProvider.overrideWithValue(token)],
                        child: const _TokenItem(),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    ),
  );

  if (selectedToken is IToken) {
    callback(selectedToken);
  }
}

final _tokenItemProvider = Provider.autoDispose<IToken>(
  (ref) => throw UnimplementedError(),
);

class _TokenItem extends ConsumerWidget {
  const _TokenItem();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final token = ref.watch(_tokenItemProvider);
    final network = ref.watch(networkProvider);
    final tokenBalance = ref.watch(
      tokenBalanceStreamProvider(network: network, address: token.address),
    );
    final price = Decimal.parse('${token.priceUsd}');
    final valueUsd = price * (tokenBalance.valueOrNull?.realBalance ?? token.realBalance);
    return RippleTap(
      onTap: () => Navigator.of(context).pushNamed(
        Routes.walletSend.name,
        arguments: Routes.walletSend.d(token: token),
      ),
      padding: const EdgeInsets.all(16.0),
      child: Row(
        spacing: 12.0,
        children: [
          // 代币图标
          if (token.logo.isNotEmpty)
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
              ),
              child: MEImage(
                token.logo,
                clipOval: true,
                fit: BoxFit.cover,
                alternativeSVG: true,
              ),
            )
          else
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: Colors.deepPurple[200],
                borderRadius: BorderRadius.circular(20),
              ),
              child: Center(
                child: Text(
                  token.symbol.substring(0, 1).toUpperCase(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                  ),
                ),
              ),
            ),

          // 代币名称和符号
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  token.symbol,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),

          // 代币数量及价值
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                tokenBalance.valueOrNull?.realBalance.toNumerical(fractionDigits: 4) ??
                    token.realBalance.toNumerical(fractionDigits: 4),
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text.rich(
                TextSpan(
                  children: [
                    const TextSpan(text: r'$'),
                    TextSpan(text: valueUsd.toNumerical()),
                  ],
                ),
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[400],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
