import 'package:flutter_riverpod/flutter_riverpod.dart';

import '/internals/box.dart' show Boxes, BoxKeys, BoxService;
import '/models/business.dart';
import '/services/chain_manager.dart';
import 'settings.dart' show settingsProvider;

// ChainManager 单例提供者 - 改为autoDispose以支持用户切换
final chainManagerProvider = Provider.autoDispose<ChainManager>((ref) {
  // 确保在provider销毁时重置单例实例
  ref.onDispose(() {
    ChainManager.resetInstance();
  });
  return ChainManager.instance;
});

// 当前钱包地址提供者，依赖于当前网络
final walletAddressProvider = Provider.autoDispose<String?>((ref) {
  ref.watch(networkProvider);
  final chainManager = ref.watch(chainManagerProvider);
  return chainManager.walletAddress;
});

final networkProvider = Provider<Network>(
  (ref) => ref.watch(settingsProvider).selectedNetwork,
  dependencies: [settingsProvider],
);
final networksProvider = Provider<List<Network>>(
  (ref) {
    final stream = BoxService.watch(box: Boxes.settings, key: BoxKeys.networks);
    final sub = stream.listen((event) {
      ref.invalidateSelf();
    });
    ref.onDispose(() {
      sub.cancel();
    });
    return BoxService.getNetworksFromLocal();
  },
  dependencies: [settingsProvider],
);
final networkByNameProvider = Provider.family<Network, String>(
  (_, name) => BoxService.getNetworkByName(name),
);
final networkByNameOrNullProvider = Provider.family<Network?, String?>(
  (_, name) => BoxService.getNetworkByNameOrNull(name),
);
final networkByIdOKXProvider = Provider.family<Network, String>(
  (_, id) => BoxService.getNetworkByIdOKX(id),
);
