import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:me_l10n/me_l10n.dart';
import 'package:me_utils/me_utils.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '/constants/release.dart';
import '/internals/box.dart';
import '/models/business.dart' show Network;

part 'settings.g.dart';

@riverpod
class LocalCache extends _$LocalCache {
  @override
  FutureOr<String> build() {
    return CacheManager.getFormatCacheSize();
  }

  Future<void> clear() {
    return Future.wait<void>([
      BoxCaches.clear(),
      CacheManager.clearCache().then((_) async {
        state = await AsyncValue.guard(CacheManager.getFormatCacheSize);
      }),
    ]);
  }
}

final settingsEnvTapCount = StateProvider.autoDispose<int>((_) => 0);
final selectedIndexProvider = StateProvider<int>((ref) => 0);

Future<void> settingsEnvOnTap(BuildContext context, WidgetRef ref) async {
  // if (!Release.sealed) {
  //   meNavigator.pushNamed(Routes.settingsEnv.name);
  //   return;
  // }
  final tapped = ref.read(settingsEnvTapCount);
  if (tapped == 10) {
    final tec = TextEditingController();
    await showDialog(
      context: context,
      builder: (context) => AlertDialog.adaptive(
        content: TextField(controller: tec),
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.of(context).maybePop(),
            child: Text(
              context.l10nME.confirmButton,
            ),
          ),
        ],
      ),
    );
    if (tec.text == Release.commitRef.split('').reversed.join('')) {
      // meNavigator.pushNamed(Routes.settingsEnv.name);
    }
    return;
  }
  ref.read(settingsEnvTapCount.notifier).state++;
}

final settingsProvider = ChangeNotifierProvider<Settings>(Settings.new);

class Settings extends ChangeNotifier {
  Settings(this.ref);

  final Ref ref;

  Future<void> _notifyThen(Future<void> Function() run) {
    return run().then((_) {
      notifyListeners();
    });
  }

  Network get selectedNetwork {
    return BoxService.getNetworkSelected();
  }

  set selectedNetwork(Network value) {
    _notifyThen(
      () => BoxService.updateSelectedNetwork(value),
    );
  }

  bool get autoParseFromClipboard {
    final value = BoxService.getSetting('auto-parse-from-clipboard');
    return value == true;
  }

  set autoParseFromClipboard(bool value) {
    _notifyThen(
      () => BoxService.updateSetting('auto-parse-from-clipboard', value),
    );
  }
}
