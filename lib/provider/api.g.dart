// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'api.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_Rep<T> _$RepFromJson<T>(Map json, T Function(Object? json) fromJsonT) =>
    _Rep<T>(
      code: json['code'] as Object,
      message: apiFromJsonMessage(json, 'message') as String? ?? '',
      data: fromJsonT(json['data']),
    );

Map<String, dynamic> _$RepToJson<T>(
  _Rep<T> instance,
  Object? Function(T value) toJsonT,
) => <String, dynamic>{
  'code': instance.code,
  'message': instance.message,
  'data': toJsonT(instance.data),
};

_ListRep<T> _$ListRepFromJson<T>(
  Map json,
  T Function(Object? json) fromJsonT,
) => _ListRep<T>(
  code: json['code'] as Object,
  message: apiFromJsonMessage(json, 'message') as String? ?? '',
  list: (json['data'] as List<dynamic>?)?.map(fromJsonT).toList() ?? const [],
);

Map<String, dynamic> _$ListRepToJson<T>(
  _ListRep<T> instance,
  Object? Function(T value) toJsonT,
) => <String, dynamic>{
  'code': instance.code,
  'message': instance.message,
  'data': instance.list.map(toJsonT).toList(),
};

_Paged<T> _$PagedFromJson<T>(Map json, T Function(Object? json) fromJsonT) =>
    _Paged<T>(
      page: (json['current'] as num).toInt(),
      size: (json['size'] as num).toInt(),
      pages: (json['pages'] as num).toInt(),
      total: (json['total'] as num?)?.toInt() ?? 0,
      list:
          (json['records'] as List<dynamic>?)?.map(fromJsonT).toList() ??
          const [],
    );

Map<String, dynamic> _$PagedToJson<T>(
  _Paged<T> instance,
  Object? Function(T value) toJsonT,
) => <String, dynamic>{
  'current': instance.page,
  'size': instance.size,
  'pages': instance.pages,
  'total': instance.total,
  'records': instance.list.map(toJsonT).toList(),
};

_ApiException _$ApiExceptionFromJson(Map json) => _ApiException(
  code: json['code'] as Object,
  message: json['message'] as String?,
  data: json['data'],
);

Map<String, dynamic> _$ApiExceptionToJson(_ApiException instance) =>
    <String, dynamic>{
      'code': instance.code,
      'message': instance.message,
      'data': instance.data,
    };
