import 'dart:io' as io;

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:me_extensions/me_extensions.dart';
import 'package:me_utils/me_utils.dart' show LogUtil;
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '/api/okx.dart' show WalletPortfolioOKX2;
import '/api/solana.dart' as sol;
import '/constants/constants.dart' show tokenSOLAddress;
import '/internals/box.dart' show BoxCaches;
import '/internals/methods.dart' show handleExceptions;
import '/models/business.dart';
import 'api.dart' show apiOKXProvider;
import 'chain.dart';

part 'token.g.dart';

final balanceDiffProvider = Provider<DateTime>(
  (_) {
    final date = DateTime.now();
    LogUtil.dd(
      () => 'Storing balance diff provider: $date',
      tag: 'balanceDiffProvider',
      tagWithTrace: false,
    );
    return date;
  },
);

void refreshTokenBalances(dynamic ref) {
  ref.invalidate(balanceDiffProvider);
}

Future<void> writeWalletPortfolioOKXCache(
  String? address,
  WalletPortfolioOKX2? value,
) async {
  if (address == null || address.isEmpty) {
    return;
  }
  const cachePrefix = 'wallet_portfolio_okx:v0';
  final cacheKey = '$cachePrefix:$address';
  if (value == null) {
    await BoxCaches.delete(cacheKey);
  } else {
    await BoxCaches.put(cacheKey, value.toJson().serialize());
  }
}

@riverpod
Future<WalletPortfolioOKX2> fetchRawWalletPortfolioOKX(
  Ref ref, {
  required Network? network,
  required String? address,
  bool writeToCache = true,
}) async {
  if (address == null || address.isEmpty || network == null || network.chainIndexOKX.isEmpty) {
    return WalletPortfolioOKX2.empty;
  }
  final (summary, tokens) = await ref
      .read(apiOKXProvider)
      .getWalletProfile(
        address: address,
        chainIdOKX: network.chainIndexOKX,
      );
  final result = WalletPortfolioOKX2(
    summary: summary,
    tokens: tokens,
  );
  if (writeToCache) {
    await writeWalletPortfolioOKXCache(address, result);
  }
  return result;
}

@riverpod
Stream<WalletPortfolioOKX2> fetchWalletPortfolioOKX(
  Ref ref, {
  required Network? network,
}) async* {
  ref.watch(balanceDiffProvider);
  if (network == null) {
    yield WalletPortfolioOKX2.empty;
    return;
  }
  final address = ref.watch(walletAddressProvider);
  if (address == null || address.isEmpty) {
    yield WalletPortfolioOKX2.empty;
    return;
  }
  const cachePrefix = 'wallet_portfolio_okx:v0';
  final cacheKey = '$cachePrefix:$address';
  final cacheData = BoxCaches.get(cacheKey) as String?;
  if (cacheData != null) {
    try {
      final cache = WalletPortfolioOKX2.fromJson(cacheData.deserialize());
      yield cache;
    } catch (e, s) {
      handleExceptions(error: e, stackTrace: s);
    }
  }
  final result = await ref.watch(
    fetchRawWalletPortfolioOKXProvider(network: network, address: address).future,
  );
  yield result;
}

@riverpod
Decimal? fetchSOLBalanceFromPortfolioOKX(Ref ref) {
  final network = ref.watch(networkProvider);
  final portfolio = ref.watch(fetchWalletPortfolioOKXProvider(network: network)).valueOrNull;
  if (portfolio == null) {
    return null;
  }
  final balance = portfolio.tokenByAddress(tokenSOLAddress)?.realBalance;
  return balance ?? Decimal.zero;
}

final tokenBalancePatch = <(Network, String), TokenAmount>{};

@riverpod
Stream<TokenAmount?> tokenBalanceStream(
  Ref ref, {
  required Network? network,
  required String? address,
}) async* {
  if (network == null || address == null) {
    yield null;
    return;
  }

  // Solana only.
  if (network.name != 'solana') {
    yield null;
    return;
  }

  final walletAddress = ref.watch(walletAddressProvider);
  if (walletAddress == null) {
    yield null;
    return;
  }

  // Handle only base58 address.
  if (!sol.Ed25519HDPublicKey.isValidFromBase58(address)) {
    yield null;
    return;
  }

  bool end = false;
  ref.onDispose(() {
    end = true;
  });

  final owner = sol.Ed25519HDPublicKey.fromBase58(walletAddress);
  final mint = sol.Ed25519HDPublicKey.fromBase58(address);

  TokenAmount? lastAmount, lastPatch;

  final rpc = ref.watch(sol.solanaClientProvider);
  int rateLimitDelaySeconds = 1;
  while (!end) {
    final patch = tokenBalancePatch[(network, address)];
    if (patch != lastPatch) {
      lastPatch = patch;
      yield patch;
    }
    try {
      TokenAmount? amount;
      try {
        if (address == tokenSOLAddress) {
          final result = await rpc.rpcClient.getBalance(walletAddress);
          amount = TokenAmount(
            amount: Decimal.fromInt(result.value),
            decimals: sol.solDecimalPlaces,
          );
        } else {
          final result = await rpc.getTokenBalance(owner: owner, mint: mint);
          amount = TokenAmount(
            amount: Decimal.parse(result.amount),
            decimals: result.decimals,
          );
        }
        // Reset the rate limited delay.
        rateLimitDelaySeconds = 1;
      } on sol.JsonRpcException catch (e) {
        if (e.message.contains('could not find account')) {
          amount = switch (lastAmount) {
            final last? => TokenAmount(
              amount: Decimal.zero,
              decimals: last.decimals,
            ),
            _ => null,
          };
        } else {
          rethrow;
        }
      } on sol.HttpException catch (e) {
        // Put extra 2 seconds delay for the next request.
        if (e.toString().contains(io.HttpStatus.tooManyRequests.toString())) {
          rateLimitDelaySeconds += 2;
          LogUtil.i(
            'Rate limited, wait $rateLimitDelaySeconds seconds before next.',
            tag: 'tokenBalanceStream',
          );
        } else {
          rethrow;
        }
      }
      if (amount != null && lastAmount?.amount != amount.amount) {
        tokenBalancePatch.remove((network, address));
        lastAmount = amount;
        LogUtil.d(
          '$address balance: ${amount.realBalance}',
          tag: 'tokenBalanceStream',
        );
        yield amount;
      }
    } catch (e, s) {
      handleExceptions(error: e, stackTrace: s);
    }
    await Future.delayed(Duration(seconds: rateLimitDelaySeconds));
  }
  LogUtil.i('$address balance end', tag: 'tokenBalanceStream');
}
