// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'card.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$fetchMyCardsHash() => r'3593954662c42ce5345e873b1a8be857f2f3b645';

/// See also [fetchMyCards].
@ProviderFor(fetchMyCards)
final fetchMyCardsProvider = AutoDisposeFutureProvider<List<CardInfo>>.internal(
  fetchMyCards,
  name: r'fetchMyCardsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$fetchMyCardsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FetchMyCardsRef = AutoDisposeFutureProviderRef<List<CardInfo>>;
String _$fetchPublicCardHash() => r'ccef701e224652f5c0930a1fbda73bf65508de75';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [fetchPublicCard].
@ProviderFor(fetchPublicCard)
const fetchPublicCardProvider = FetchPublicCardFamily();

/// See also [fetchPublicCard].
class FetchPublicCardFamily extends Family<AsyncValue<CardInfo>> {
  /// See also [fetchPublicCard].
  const FetchPublicCardFamily();

  /// See also [fetchPublicCard].
  FetchPublicCardProvider call({required String code}) {
    return FetchPublicCardProvider(code: code);
  }

  @override
  FetchPublicCardProvider getProviderOverride(
    covariant FetchPublicCardProvider provider,
  ) {
    return call(code: provider.code);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'fetchPublicCardProvider';
}

/// See also [fetchPublicCard].
class FetchPublicCardProvider extends AutoDisposeFutureProvider<CardInfo> {
  /// See also [fetchPublicCard].
  FetchPublicCardProvider({required String code})
    : this._internal(
        (ref) => fetchPublicCard(ref as FetchPublicCardRef, code: code),
        from: fetchPublicCardProvider,
        name: r'fetchPublicCardProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$fetchPublicCardHash,
        dependencies: FetchPublicCardFamily._dependencies,
        allTransitiveDependencies:
            FetchPublicCardFamily._allTransitiveDependencies,
        code: code,
      );

  FetchPublicCardProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.code,
  }) : super.internal();

  final String code;

  @override
  Override overrideWith(
    FutureOr<CardInfo> Function(FetchPublicCardRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: FetchPublicCardProvider._internal(
        (ref) => create(ref as FetchPublicCardRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        code: code,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<CardInfo> createElement() {
    return _FetchPublicCardProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FetchPublicCardProvider && other.code == code;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, code.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin FetchPublicCardRef on AutoDisposeFutureProviderRef<CardInfo> {
  /// The parameter `code` of this provider.
  String get code;
}

class _FetchPublicCardProviderElement
    extends AutoDisposeFutureProviderElement<CardInfo>
    with FetchPublicCardRef {
  _FetchPublicCardProviderElement(super.provider);

  @override
  String get code => (origin as FetchPublicCardProvider).code;
}

String _$fetchPublicProfileHash() =>
    r'82d1430b0818ebea615a04adecc5de0cec239c71';

/// See also [fetchPublicProfile].
@ProviderFor(fetchPublicProfile)
const fetchPublicProfileProvider = FetchPublicProfileFamily();

/// See also [fetchPublicProfile].
class FetchPublicProfileFamily extends Family<AsyncValue<UserInfo>> {
  /// See also [fetchPublicProfile].
  const FetchPublicProfileFamily();

  /// See also [fetchPublicProfile].
  FetchPublicProfileProvider call({required String code}) {
    return FetchPublicProfileProvider(code: code);
  }

  @override
  FetchPublicProfileProvider getProviderOverride(
    covariant FetchPublicProfileProvider provider,
  ) {
    return call(code: provider.code);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'fetchPublicProfileProvider';
}

/// See also [fetchPublicProfile].
class FetchPublicProfileProvider extends AutoDisposeFutureProvider<UserInfo> {
  /// See also [fetchPublicProfile].
  FetchPublicProfileProvider({required String code})
    : this._internal(
        (ref) => fetchPublicProfile(ref as FetchPublicProfileRef, code: code),
        from: fetchPublicProfileProvider,
        name: r'fetchPublicProfileProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$fetchPublicProfileHash,
        dependencies: FetchPublicProfileFamily._dependencies,
        allTransitiveDependencies:
            FetchPublicProfileFamily._allTransitiveDependencies,
        code: code,
      );

  FetchPublicProfileProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.code,
  }) : super.internal();

  final String code;

  @override
  Override overrideWith(
    FutureOr<UserInfo> Function(FetchPublicProfileRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: FetchPublicProfileProvider._internal(
        (ref) => create(ref as FetchPublicProfileRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        code: code,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<UserInfo> createElement() {
    return _FetchPublicProfileProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FetchPublicProfileProvider && other.code == code;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, code.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin FetchPublicProfileRef on AutoDisposeFutureProviderRef<UserInfo> {
  /// The parameter `code` of this provider.
  String get code;
}

class _FetchPublicProfileProviderElement
    extends AutoDisposeFutureProviderElement<UserInfo>
    with FetchPublicProfileRef {
  _FetchPublicProfileProviderElement(super.provider);

  @override
  String get code => (origin as FetchPublicProfileProvider).code;
}

String _$fetchSocialsHash() => r'6bea14e2b1e45e754b9c780f023986ade7714839';

/// See also [fetchSocials].
@ProviderFor(fetchSocials)
const fetchSocialsProvider = FetchSocialsFamily();

/// See also [fetchSocials].
class FetchSocialsFamily extends Family<AsyncValue<List<Social>>> {
  /// See also [fetchSocials].
  const FetchSocialsFamily();

  /// See also [fetchSocials].
  FetchSocialsProvider call({String? code}) {
    return FetchSocialsProvider(code: code);
  }

  @override
  FetchSocialsProvider getProviderOverride(
    covariant FetchSocialsProvider provider,
  ) {
    return call(code: provider.code);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'fetchSocialsProvider';
}

/// See also [fetchSocials].
class FetchSocialsProvider extends AutoDisposeFutureProvider<List<Social>> {
  /// See also [fetchSocials].
  FetchSocialsProvider({String? code})
    : this._internal(
        (ref) => fetchSocials(ref as FetchSocialsRef, code: code),
        from: fetchSocialsProvider,
        name: r'fetchSocialsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$fetchSocialsHash,
        dependencies: FetchSocialsFamily._dependencies,
        allTransitiveDependencies:
            FetchSocialsFamily._allTransitiveDependencies,
        code: code,
      );

  FetchSocialsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.code,
  }) : super.internal();

  final String? code;

  @override
  Override overrideWith(
    FutureOr<List<Social>> Function(FetchSocialsRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: FetchSocialsProvider._internal(
        (ref) => create(ref as FetchSocialsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        code: code,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<Social>> createElement() {
    return _FetchSocialsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FetchSocialsProvider && other.code == code;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, code.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin FetchSocialsRef on AutoDisposeFutureProviderRef<List<Social>> {
  /// The parameter `code` of this provider.
  String? get code;
}

class _FetchSocialsProviderElement
    extends AutoDisposeFutureProviderElement<List<Social>>
    with FetchSocialsRef {
  _FetchSocialsProviderElement(super.provider);

  @override
  String? get code => (origin as FetchSocialsProvider).code;
}

String _$fetchEthccTopicsHash() => r'46e1fdac4bbb31ea1e85124feeb5602cdc0ffd59';

/// See also [fetchEthccTopics].
@ProviderFor(fetchEthccTopics)
final fetchEthccTopicsProvider =
    AutoDisposeFutureProvider<List<String>>.internal(
      fetchEthccTopics,
      name: r'fetchEthccTopicsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$fetchEthccTopicsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FetchEthccTopicsRef = AutoDisposeFutureProviderRef<List<String>>;
String _$fetchEthccRolesHash() => r'64def44aca6c8520c3df3945b2da689092ca810d';

/// See also [fetchEthccRoles].
@ProviderFor(fetchEthccRoles)
final fetchEthccRolesProvider =
    AutoDisposeFutureProvider<List<String>>.internal(
      fetchEthccRoles,
      name: r'fetchEthccRolesProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$fetchEthccRolesHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FetchEthccRolesRef = AutoDisposeFutureProviderRef<List<String>>;
String _$fetchEthccProfileHash() => r'70a0e5901887862fceaa0e89d59ae4b207ebf062';

/// See also [fetchEthccProfile].
@ProviderFor(fetchEthccProfile)
const fetchEthccProfileProvider = FetchEthccProfileFamily();

/// See also [fetchEthccProfile].
class FetchEthccProfileFamily extends Family<AsyncValue<EthccProfile?>> {
  /// See also [fetchEthccProfile].
  const FetchEthccProfileFamily();

  /// See also [fetchEthccProfile].
  FetchEthccProfileProvider call({String? code}) {
    return FetchEthccProfileProvider(code: code);
  }

  @override
  FetchEthccProfileProvider getProviderOverride(
    covariant FetchEthccProfileProvider provider,
  ) {
    return call(code: provider.code);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'fetchEthccProfileProvider';
}

/// See also [fetchEthccProfile].
class FetchEthccProfileProvider
    extends AutoDisposeFutureProvider<EthccProfile?> {
  /// See also [fetchEthccProfile].
  FetchEthccProfileProvider({String? code})
    : this._internal(
        (ref) => fetchEthccProfile(ref as FetchEthccProfileRef, code: code),
        from: fetchEthccProfileProvider,
        name: r'fetchEthccProfileProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$fetchEthccProfileHash,
        dependencies: FetchEthccProfileFamily._dependencies,
        allTransitiveDependencies:
            FetchEthccProfileFamily._allTransitiveDependencies,
        code: code,
      );

  FetchEthccProfileProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.code,
  }) : super.internal();

  final String? code;

  @override
  Override overrideWith(
    FutureOr<EthccProfile?> Function(FetchEthccProfileRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: FetchEthccProfileProvider._internal(
        (ref) => create(ref as FetchEthccProfileRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        code: code,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<EthccProfile?> createElement() {
    return _FetchEthccProfileProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FetchEthccProfileProvider && other.code == code;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, code.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin FetchEthccProfileRef on AutoDisposeFutureProviderRef<EthccProfile?> {
  /// The parameter `code` of this provider.
  String? get code;
}

class _FetchEthccProfileProviderElement
    extends AutoDisposeFutureProviderElement<EthccProfile?>
    with FetchEthccProfileRef {
  _FetchEthccProfileProviderElement(super.provider);

  @override
  String? get code => (origin as FetchEthccProfileProvider).code;
}

String _$fetchGitHubContributionsHash() =>
    r'30d4ff3e8210af34eb904b303e7baf2fd3e425fb';

/// See also [fetchGitHubContributions].
@ProviderFor(fetchGitHubContributions)
const fetchGitHubContributionsProvider = FetchGitHubContributionsFamily();

/// See also [fetchGitHubContributions].
class FetchGitHubContributionsFamily
    extends Family<AsyncValue<FrontEndGitHubContributionCollection?>> {
  /// See also [fetchGitHubContributions].
  const FetchGitHubContributionsFamily();

  /// See also [fetchGitHubContributions].
  FetchGitHubContributionsProvider call({required String handle}) {
    return FetchGitHubContributionsProvider(handle: handle);
  }

  @override
  FetchGitHubContributionsProvider getProviderOverride(
    covariant FetchGitHubContributionsProvider provider,
  ) {
    return call(handle: provider.handle);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'fetchGitHubContributionsProvider';
}

/// See also [fetchGitHubContributions].
class FetchGitHubContributionsProvider
    extends AutoDisposeFutureProvider<FrontEndGitHubContributionCollection?> {
  /// See also [fetchGitHubContributions].
  FetchGitHubContributionsProvider({required String handle})
    : this._internal(
        (ref) => fetchGitHubContributions(
          ref as FetchGitHubContributionsRef,
          handle: handle,
        ),
        from: fetchGitHubContributionsProvider,
        name: r'fetchGitHubContributionsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$fetchGitHubContributionsHash,
        dependencies: FetchGitHubContributionsFamily._dependencies,
        allTransitiveDependencies:
            FetchGitHubContributionsFamily._allTransitiveDependencies,
        handle: handle,
      );

  FetchGitHubContributionsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.handle,
  }) : super.internal();

  final String handle;

  @override
  Override overrideWith(
    FutureOr<FrontEndGitHubContributionCollection?> Function(
      FetchGitHubContributionsRef provider,
    )
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: FetchGitHubContributionsProvider._internal(
        (ref) => create(ref as FetchGitHubContributionsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        handle: handle,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<FrontEndGitHubContributionCollection?>
  createElement() {
    return _FetchGitHubContributionsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FetchGitHubContributionsProvider && other.handle == handle;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, handle.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin FetchGitHubContributionsRef
    on AutoDisposeFutureProviderRef<FrontEndGitHubContributionCollection?> {
  /// The parameter `handle` of this provider.
  String get handle;
}

class _FetchGitHubContributionsProviderElement
    extends
        AutoDisposeFutureProviderElement<FrontEndGitHubContributionCollection?>
    with FetchGitHubContributionsRef {
  _FetchGitHubContributionsProviderElement(super.provider);

  @override
  String get handle => (origin as FetchGitHubContributionsProvider).handle;
}

String _$validateETHCCProfileHash() =>
    r'b65f764f44d52e62d1a8c88bbaf6e182c6b23428';

/// See also [validateETHCCProfile].
@ProviderFor(validateETHCCProfile)
const validateETHCCProfileProvider = ValidateETHCCProfileFamily();

/// See also [validateETHCCProfile].
class ValidateETHCCProfileFamily extends Family<bool?> {
  /// See also [validateETHCCProfile].
  const ValidateETHCCProfileFamily();

  /// See also [validateETHCCProfile].
  ValidateETHCCProfileProvider call({required bool validateProfile}) {
    return ValidateETHCCProfileProvider(validateProfile: validateProfile);
  }

  @override
  ValidateETHCCProfileProvider getProviderOverride(
    covariant ValidateETHCCProfileProvider provider,
  ) {
    return call(validateProfile: provider.validateProfile);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'validateETHCCProfileProvider';
}

/// See also [validateETHCCProfile].
class ValidateETHCCProfileProvider extends AutoDisposeProvider<bool?> {
  /// See also [validateETHCCProfile].
  ValidateETHCCProfileProvider({required bool validateProfile})
    : this._internal(
        (ref) => validateETHCCProfile(
          ref as ValidateETHCCProfileRef,
          validateProfile: validateProfile,
        ),
        from: validateETHCCProfileProvider,
        name: r'validateETHCCProfileProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$validateETHCCProfileHash,
        dependencies: ValidateETHCCProfileFamily._dependencies,
        allTransitiveDependencies:
            ValidateETHCCProfileFamily._allTransitiveDependencies,
        validateProfile: validateProfile,
      );

  ValidateETHCCProfileProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.validateProfile,
  }) : super.internal();

  final bool validateProfile;

  @override
  Override overrideWith(
    bool? Function(ValidateETHCCProfileRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: ValidateETHCCProfileProvider._internal(
        (ref) => create(ref as ValidateETHCCProfileRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        validateProfile: validateProfile,
      ),
    );
  }

  @override
  AutoDisposeProviderElement<bool?> createElement() {
    return _ValidateETHCCProfileProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ValidateETHCCProfileProvider &&
        other.validateProfile == validateProfile;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, validateProfile.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin ValidateETHCCProfileRef on AutoDisposeProviderRef<bool?> {
  /// The parameter `validateProfile` of this provider.
  bool get validateProfile;
}

class _ValidateETHCCProfileProviderElement
    extends AutoDisposeProviderElement<bool?>
    with ValidateETHCCProfileRef {
  _ValidateETHCCProfileProviderElement(super.provider);

  @override
  bool get validateProfile =>
      (origin as ValidateETHCCProfileProvider).validateProfile;
}

String _$validateETHCCProfileByCodeHash() =>
    r'6ccbc63b412bf8d3f1032b95ecf40316fb3bebce';

/// See also [validateETHCCProfileByCode].
@ProviderFor(validateETHCCProfileByCode)
const validateETHCCProfileByCodeProvider = ValidateETHCCProfileByCodeFamily();

/// See also [validateETHCCProfileByCode].
class ValidateETHCCProfileByCodeFamily extends Family<AsyncValue<bool>> {
  /// See also [validateETHCCProfileByCode].
  const ValidateETHCCProfileByCodeFamily();

  /// See also [validateETHCCProfileByCode].
  ValidateETHCCProfileByCodeProvider call({required String code}) {
    return ValidateETHCCProfileByCodeProvider(code: code);
  }

  @override
  ValidateETHCCProfileByCodeProvider getProviderOverride(
    covariant ValidateETHCCProfileByCodeProvider provider,
  ) {
    return call(code: provider.code);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'validateETHCCProfileByCodeProvider';
}

/// See also [validateETHCCProfileByCode].
class ValidateETHCCProfileByCodeProvider
    extends AutoDisposeFutureProvider<bool> {
  /// See also [validateETHCCProfileByCode].
  ValidateETHCCProfileByCodeProvider({required String code})
    : this._internal(
        (ref) => validateETHCCProfileByCode(
          ref as ValidateETHCCProfileByCodeRef,
          code: code,
        ),
        from: validateETHCCProfileByCodeProvider,
        name: r'validateETHCCProfileByCodeProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$validateETHCCProfileByCodeHash,
        dependencies: ValidateETHCCProfileByCodeFamily._dependencies,
        allTransitiveDependencies:
            ValidateETHCCProfileByCodeFamily._allTransitiveDependencies,
        code: code,
      );

  ValidateETHCCProfileByCodeProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.code,
  }) : super.internal();

  final String code;

  @override
  Override overrideWith(
    FutureOr<bool> Function(ValidateETHCCProfileByCodeRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: ValidateETHCCProfileByCodeProvider._internal(
        (ref) => create(ref as ValidateETHCCProfileByCodeRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        code: code,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<bool> createElement() {
    return _ValidateETHCCProfileByCodeProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ValidateETHCCProfileByCodeProvider && other.code == code;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, code.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin ValidateETHCCProfileByCodeRef on AutoDisposeFutureProviderRef<bool> {
  /// The parameter `code` of this provider.
  String get code;
}

class _ValidateETHCCProfileByCodeProviderElement
    extends AutoDisposeFutureProviderElement<bool>
    with ValidateETHCCProfileByCodeRef {
  _ValidateETHCCProfileByCodeProviderElement(super.provider);

  @override
  String get code => (origin as ValidateETHCCProfileByCodeProvider).code;
}

String _$validateETHCCProfileInnerHash() =>
    r'35f5420fcbadcea40e1adcc7de5296e8a84dbd89';

/// See also [_validateETHCCProfileInner].
@ProviderFor(_validateETHCCProfileInner)
const _validateETHCCProfileInnerProvider = _ValidateETHCCProfileInnerFamily();

/// See also [_validateETHCCProfileInner].
class _ValidateETHCCProfileInnerFamily extends Family<bool?> {
  /// See also [_validateETHCCProfileInner].
  const _ValidateETHCCProfileInnerFamily();

  /// See also [_validateETHCCProfileInner].
  _ValidateETHCCProfileInnerProvider call({
    required List<CardInfo> cards,
    required UserInfo userInfo,
    required bool validateProfile,
  }) {
    return _ValidateETHCCProfileInnerProvider(
      cards: cards,
      userInfo: userInfo,
      validateProfile: validateProfile,
    );
  }

  @override
  _ValidateETHCCProfileInnerProvider getProviderOverride(
    covariant _ValidateETHCCProfileInnerProvider provider,
  ) {
    return call(
      cards: provider.cards,
      userInfo: provider.userInfo,
      validateProfile: provider.validateProfile,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'_validateETHCCProfileInnerProvider';
}

/// See also [_validateETHCCProfileInner].
class _ValidateETHCCProfileInnerProvider extends AutoDisposeProvider<bool?> {
  /// See also [_validateETHCCProfileInner].
  _ValidateETHCCProfileInnerProvider({
    required List<CardInfo> cards,
    required UserInfo userInfo,
    required bool validateProfile,
  }) : this._internal(
         (ref) => _validateETHCCProfileInner(
           ref as _ValidateETHCCProfileInnerRef,
           cards: cards,
           userInfo: userInfo,
           validateProfile: validateProfile,
         ),
         from: _validateETHCCProfileInnerProvider,
         name: r'_validateETHCCProfileInnerProvider',
         debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
             ? null
             : _$validateETHCCProfileInnerHash,
         dependencies: _ValidateETHCCProfileInnerFamily._dependencies,
         allTransitiveDependencies:
             _ValidateETHCCProfileInnerFamily._allTransitiveDependencies,
         cards: cards,
         userInfo: userInfo,
         validateProfile: validateProfile,
       );

  _ValidateETHCCProfileInnerProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.cards,
    required this.userInfo,
    required this.validateProfile,
  }) : super.internal();

  final List<CardInfo> cards;
  final UserInfo userInfo;
  final bool validateProfile;

  @override
  Override overrideWith(
    bool? Function(_ValidateETHCCProfileInnerRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: _ValidateETHCCProfileInnerProvider._internal(
        (ref) => create(ref as _ValidateETHCCProfileInnerRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        cards: cards,
        userInfo: userInfo,
        validateProfile: validateProfile,
      ),
    );
  }

  @override
  AutoDisposeProviderElement<bool?> createElement() {
    return _ValidateETHCCProfileInnerProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is _ValidateETHCCProfileInnerProvider &&
        other.cards == cards &&
        other.userInfo == userInfo &&
        other.validateProfile == validateProfile;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, cards.hashCode);
    hash = _SystemHash.combine(hash, userInfo.hashCode);
    hash = _SystemHash.combine(hash, validateProfile.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin _ValidateETHCCProfileInnerRef on AutoDisposeProviderRef<bool?> {
  /// The parameter `cards` of this provider.
  List<CardInfo> get cards;

  /// The parameter `userInfo` of this provider.
  UserInfo get userInfo;

  /// The parameter `validateProfile` of this provider.
  bool get validateProfile;
}

class _ValidateETHCCProfileInnerProviderElement
    extends AutoDisposeProviderElement<bool?>
    with _ValidateETHCCProfileInnerRef {
  _ValidateETHCCProfileInnerProviderElement(super.provider);

  @override
  List<CardInfo> get cards =>
      (origin as _ValidateETHCCProfileInnerProvider).cards;
  @override
  UserInfo get userInfo =>
      (origin as _ValidateETHCCProfileInnerProvider).userInfo;
  @override
  bool get validateProfile =>
      (origin as _ValidateETHCCProfileInnerProvider).validateProfile;
}

String _$isActiveGuideHash() => r'6be22d3f5c36f82ba9dbbbc587f5e9b64123140a';

/// See also [_isActiveGuide].
@ProviderFor(_isActiveGuide)
final _isActiveGuideProvider = AutoDisposeProvider<bool?>.internal(
  _isActiveGuide,
  name: r'_isActiveGuideProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$isActiveGuideHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef _IsActiveGuideRef = AutoDisposeProviderRef<bool?>;
String _$watchIsActiveGuideHash() =>
    r'd2278391477a6373c7763358c4461633b3acc94e';

/// See also [watchIsActiveGuide].
@ProviderFor(watchIsActiveGuide)
final watchIsActiveGuideProvider = AutoDisposeProvider<bool?>.internal(
  watchIsActiveGuide,
  name: r'watchIsActiveGuideProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$watchIsActiveGuideHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef WatchIsActiveGuideRef = AutoDisposeProviderRef<bool?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
