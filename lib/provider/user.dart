import 'dart:async';
import 'dart:convert';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '/constants/envs.dart' as envs show EnvOverrides;
import '/extensions/riverpod_extension.dart';
import '/internals/box.dart' show BoxService;
import '/internals/methods.dart';
import '/models/user.dart' show UserInfo, UserRelation, UserFromRelation, UserFromRelationType;
import 'api.dart' show ApiException, Paged, apiServiceProvider;

part 'user.g.dart';

@riverpod
class PersistentTokenRepo extends _$PersistentTokenRepo {
  /// Overrides runtime token.
  String? get _override => envs.EnvOverrides.jwtOverrode;

  Timer? _refreshTimer;

  @override
  String? build() {
    if (_override != null) {
      return _override;
    }
    if (!_checkRefreshTimer()) {
      return null;
    }
    return _latest;
  }

  String? get _latest => _override ?? BoxService.getToken();

  /// Returns whether the token is valid.
  bool _checkRefreshTimer() {
    final token = _latest;
    _refreshTimer?.cancel();
    if (token == null || token.isEmpty) {
      return false;
    }
    try {
      final decoded = json.decode(
        utf8.decode(
          base64Url.decode(base64Url.normalize(token.split('.')[1])),
        ),
      );
      final expired = DateTime.fromMillisecondsSinceEpoch(
        (decoded['exp'] as int) * 1000,
      );
      final difference = expired.difference(DateTime.now());
      if (difference >= const Duration(minutes: 1)) {
        _refreshTimer = Timer(difference ~/ 2, refresh);
      }
    } catch (e, s) {
      handleExceptions(error: e, stackTrace: s);
    }
    return true;
  }

  Future<bool> refresh() async {
    if (_override != null) {
      return true;
    }
    if (!_checkRefreshTimer()) {
      return false;
    }
    String newToken;
    try {
      newToken = await ref.read(apiServiceProvider).getRefreshJWT();
      await update(newToken);
      return true;
    } on ApiException {
      return false;
    }
  }

  Future<void> update(String token) async {
    if (_override != null) {
      return;
    }
    state = await BoxService.updateToken(token);
    _checkRefreshTimer();
  }
}

@riverpod
final class UserRepo extends _$UserRepo {
  @override
  UserInfo? build() {
    return BoxService.getUserInfo();
  }

  void refresh() {
    state = BoxService.getUserInfo();
  }

  Future<void> update(UserInfo user) async {
    await BoxService.updateUser(user);
    refresh();
  }

  Future<void> reset() async {
    await BoxService.clearUserBox();
    refresh();
  }
}

@riverpod
Future<UserInfo> fetchUserInfo(
  Ref ref, {
  String? code,
}) {
  if (code != null) {
    return ref.read(apiServiceProvider).getPublicProfile(code: code);
  } else {
    return ref.read(apiServiceProvider).getSelfUserInfo();
  }
}

@riverpod
Future<(UserRelation relation, String referralCode)?> fetchUserRelation(
  Ref ref, {
  required String? code,
}) async {
  final userCode = ref.watch(userRepoProvider)?.referralCode;
  code = code?.trim();
  if (code == null || code.isEmpty || code == userCode) {
    return null;
  }
  final user = await ref.read(fetchUserInfoProvider(code: code).future);
  final referralCode = user.referralCode;
  final relation = await ref.read(apiServiceProvider).getUserRelation(referralCode: referralCode);
  return (relation, referralCode);
}

@riverpod
Future<Paged<UserFromRelation>> fetchUsersFromRelation(
  Ref ref, {
  required UserFromRelationType type,
  required int page,
}) {
  final ct = ref.cancelToken();
  ref.onDispose(() {
    ct.cancel();
  });
  switch (type) {
    case UserFromRelationType.following:
      return ref.read(apiServiceProvider).getFollowingList(page: page, cancelToken: ct);
    case UserFromRelationType.follower:
      return ref.read(apiServiceProvider).getFollowerList(page: page, cancelToken: ct);
  }
}
