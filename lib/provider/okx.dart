import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '/api/okx.dart';
import '/provider/api.dart' show apiOKXProvider;
import '/provider/token.dart' show balanceDiffProvider;

part 'okx.g.dart';

@riverpod
Future<(OKXWalletProfileSummary summary, List<OKXWalletProfileToken> tokens)> fetchOKXWalletProfile(
  Ref ref, {
  required String chainIdOKX,
  required String address,
  bool writeToCache = true,
}) {
  ref.watch(balanceDiffProvider);
  return ref.read(apiOKXProvider).getWalletProfile(address: address, chainIdOKX: chainIdOKX);
}
