// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'business.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$fetchNetworksNewHash() => r'b69094d34f0d0d1d77ae4d4184fdc0c1a89104b4';

/// See also [fetchNetworksNew].
@ProviderFor(fetchNetworksNew)
final fetchNetworksNewProvider =
    AutoDisposeFutureProvider<List<Network>>.internal(
      fetchNetworksNew,
      name: r'fetchNetworksNewProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$fetchNetworksNewHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FetchNetworksNewRef = AutoDisposeFutureProviderRef<List<Network>>;
String _$fetchMessagesHash() => r'9811978e8004d3957b0beb3f198fad270f44a303';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [fetchMessages].
@ProviderFor(fetchMessages)
const fetchMessagesProvider = FetchMessagesFamily();

/// See also [fetchMessages].
class FetchMessagesFamily extends Family<AsyncValue<Paged<Message>>> {
  /// See also [fetchMessages].
  const FetchMessagesFamily();

  /// See also [fetchMessages].
  FetchMessagesProvider call({required int page}) {
    return FetchMessagesProvider(page: page);
  }

  @override
  FetchMessagesProvider getProviderOverride(
    covariant FetchMessagesProvider provider,
  ) {
    return call(page: provider.page);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'fetchMessagesProvider';
}

/// See also [fetchMessages].
class FetchMessagesProvider extends AutoDisposeFutureProvider<Paged<Message>> {
  /// See also [fetchMessages].
  FetchMessagesProvider({required int page})
    : this._internal(
        (ref) => fetchMessages(ref as FetchMessagesRef, page: page),
        from: fetchMessagesProvider,
        name: r'fetchMessagesProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$fetchMessagesHash,
        dependencies: FetchMessagesFamily._dependencies,
        allTransitiveDependencies:
            FetchMessagesFamily._allTransitiveDependencies,
        page: page,
      );

  FetchMessagesProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.page,
  }) : super.internal();

  final int page;

  @override
  Override overrideWith(
    FutureOr<Paged<Message>> Function(FetchMessagesRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: FetchMessagesProvider._internal(
        (ref) => create(ref as FetchMessagesRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        page: page,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<Paged<Message>> createElement() {
    return _FetchMessagesProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FetchMessagesProvider && other.page == page;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, page.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin FetchMessagesRef on AutoDisposeFutureProviderRef<Paged<Message>> {
  /// The parameter `page` of this provider.
  int get page;
}

class _FetchMessagesProviderElement
    extends AutoDisposeFutureProviderElement<Paged<Message>>
    with FetchMessagesRef {
  _FetchMessagesProviderElement(super.provider);

  @override
  int get page => (origin as FetchMessagesProvider).page;
}

String _$fetchPointsHash() => r'8f6e5027c652fa5e2a2cc55686572b73c48e1307';

/// See also [fetchPoints].
@ProviderFor(fetchPoints)
const fetchPointsProvider = FetchPointsFamily();

/// See also [fetchPoints].
class FetchPointsFamily extends Family<AsyncValue<Paged<Point>>> {
  /// See also [fetchPoints].
  const FetchPointsFamily();

  /// See also [fetchPoints].
  FetchPointsProvider call({required int pageNum, required int pageSize}) {
    return FetchPointsProvider(pageNum: pageNum, pageSize: pageSize);
  }

  @override
  FetchPointsProvider getProviderOverride(
    covariant FetchPointsProvider provider,
  ) {
    return call(pageNum: provider.pageNum, pageSize: provider.pageSize);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'fetchPointsProvider';
}

/// See also [fetchPoints].
class FetchPointsProvider extends AutoDisposeFutureProvider<Paged<Point>> {
  /// See also [fetchPoints].
  FetchPointsProvider({required int pageNum, required int pageSize})
    : this._internal(
        (ref) => fetchPoints(
          ref as FetchPointsRef,
          pageNum: pageNum,
          pageSize: pageSize,
        ),
        from: fetchPointsProvider,
        name: r'fetchPointsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$fetchPointsHash,
        dependencies: FetchPointsFamily._dependencies,
        allTransitiveDependencies: FetchPointsFamily._allTransitiveDependencies,
        pageNum: pageNum,
        pageSize: pageSize,
      );

  FetchPointsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.pageNum,
    required this.pageSize,
  }) : super.internal();

  final int pageNum;
  final int pageSize;

  @override
  Override overrideWith(
    FutureOr<Paged<Point>> Function(FetchPointsRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: FetchPointsProvider._internal(
        (ref) => create(ref as FetchPointsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        pageNum: pageNum,
        pageSize: pageSize,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<Paged<Point>> createElement() {
    return _FetchPointsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FetchPointsProvider &&
        other.pageNum == pageNum &&
        other.pageSize == pageSize;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, pageNum.hashCode);
    hash = _SystemHash.combine(hash, pageSize.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin FetchPointsRef on AutoDisposeFutureProviderRef<Paged<Point>> {
  /// The parameter `pageNum` of this provider.
  int get pageNum;

  /// The parameter `pageSize` of this provider.
  int get pageSize;
}

class _FetchPointsProviderElement
    extends AutoDisposeFutureProviderElement<Paged<Point>>
    with FetchPointsRef {
  _FetchPointsProviderElement(super.provider);

  @override
  int get pageNum => (origin as FetchPointsProvider).pageNum;
  @override
  int get pageSize => (origin as FetchPointsProvider).pageSize;
}

String _$fetchTickersPriceHash() => r'db40f43b6d283d4b1c98b488c56d0a51e2db2011';

/// See also [fetchTickersPrice].
@ProviderFor(fetchTickersPrice)
const fetchTickersPriceProvider = FetchTickersPriceFamily();

/// See also [fetchTickersPrice].
class FetchTickersPriceFamily extends Family<AsyncValue<AstroxTicker>> {
  /// See also [fetchTickersPrice].
  const FetchTickersPriceFamily();

  /// See also [fetchTickersPrice].
  FetchTickersPriceProvider call({required String symbol}) {
    return FetchTickersPriceProvider(symbol: symbol);
  }

  @override
  FetchTickersPriceProvider getProviderOverride(
    covariant FetchTickersPriceProvider provider,
  ) {
    return call(symbol: provider.symbol);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'fetchTickersPriceProvider';
}

/// See also [fetchTickersPrice].
class FetchTickersPriceProvider
    extends AutoDisposeFutureProvider<AstroxTicker> {
  /// See also [fetchTickersPrice].
  FetchTickersPriceProvider({required String symbol})
    : this._internal(
        (ref) => fetchTickersPrice(ref as FetchTickersPriceRef, symbol: symbol),
        from: fetchTickersPriceProvider,
        name: r'fetchTickersPriceProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$fetchTickersPriceHash,
        dependencies: FetchTickersPriceFamily._dependencies,
        allTransitiveDependencies:
            FetchTickersPriceFamily._allTransitiveDependencies,
        symbol: symbol,
      );

  FetchTickersPriceProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.symbol,
  }) : super.internal();

  final String symbol;

  @override
  Override overrideWith(
    FutureOr<AstroxTicker> Function(FetchTickersPriceRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: FetchTickersPriceProvider._internal(
        (ref) => create(ref as FetchTickersPriceRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        symbol: symbol,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<AstroxTicker> createElement() {
    return _FetchTickersPriceProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FetchTickersPriceProvider && other.symbol == symbol;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, symbol.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin FetchTickersPriceRef on AutoDisposeFutureProviderRef<AstroxTicker> {
  /// The parameter `symbol` of this provider.
  String get symbol;
}

class _FetchTickersPriceProviderElement
    extends AutoDisposeFutureProviderElement<AstroxTicker>
    with FetchTickersPriceRef {
  _FetchTickersPriceProviderElement(super.provider);

  @override
  String get symbol => (origin as FetchTickersPriceProvider).symbol;
}

String _$configStateHash() => r'e63a632f10abbf482bdfeabd61a8dbc235d37da6';

/// See also [ConfigState].
@ProviderFor(ConfigState)
final configStateProvider = NotifierProvider<ConfigState, Config>.internal(
  ConfigState.new,
  name: r'configStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$configStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ConfigState = Notifier<Config>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
