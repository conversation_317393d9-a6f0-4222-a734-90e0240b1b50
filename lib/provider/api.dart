import 'dart:io' as io;

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:me_constants/me_constants.dart' show globalJsonEncoder;
import 'package:me_utils/me_utils.dart' show DeviceUtil, LogDeviceInfoExtension, LogUtil, PackageUtil;

import '../api/astrox.dart' show AstroxApi;
import '../api/astrox_gw.dart' show AstroxGwApi;
import '../api/front_end.dart' show FrontEndApi;
import '../api/okx.dart' show OKXApi;
import '../api/okx_old.dart' show OkxOldApi;
import '../api/service.dart' show ApiServiceInterceptor, ServiceApi;
import '../api/solana.dart' show SolanaApi;
import '../constants/envs.dart';
import '../constants/release.dart';
import '../internals/box.dart';
import '../internals/methods.dart' show handleExceptions, isNetworkError;

export 'package:dio/dio.dart' show BaseOptions, CancelToken, Options, QueuedInterceptor, QueuedInterceptorsWrapper;
export 'package:me_misc/me_misc.dart' show retryWith, MERetryExtension, MERetryFunctionExtension;

export '../api/astrox.dart' show AstroxApi;
export '../api/front_end.dart' show FrontEndApi;
export '../api/service.dart' show ServiceApi;
export '../api/solana.dart' show SolanaApi;

part 'api.freezed.dart';

part 'api.g.dart';

typedef Http = Dio;
typedef HttpException = DioException;
typedef HttpExceptionType = DioExceptionType;

const httpExtraKeySucceedCode = 'api-succeed-code';

final httpProvider = Provider<Http>((ref) {
  HttpException.readableStringBuilder = (e) {
    final buffer = StringBuffer('${e.type}');
    if (e.error != null) {
      buffer.writeln();
      buffer.write('Error: ${e.error}');
    }
    return buffer.toString();
  };
  final instance = Http(
    BaseOptions(
      connectTimeout: const Duration(seconds: 15),
      receiveTimeout: const Duration(seconds: 15),
      sendTimeout: const Duration(seconds: 45),
    ),
  );
  instance.interceptors.addAll([
    _LogInterceptor(),
    _AuthInterceptor(),
    _ApiCodeInterceptor(),
    ApiServiceInterceptor(),
  ]);
  return instance;
});

class _LogInterceptor extends QueuedInterceptor {
  static const _tag = '🛜 HttpLog';

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    try {
      LogUtil.dd(
        () {
          try {
            final sb = StringBuffer(
              '====>\n${options.method} ${options.uri}\n',
            );
            if (options.queryParameters.isNotEmpty) {
              sb.writeln(globalJsonEncoder.convert(options.queryParameters));
            }
            final data = options.data;
            if (data is Map || data is List) {
              sb.write(globalJsonEncoder.convert(data));
            } else if (data != null) {
              sb.write(data.toString());
            }
            return sb.toString();
          } catch (e, s) {
            handleExceptions(error: e, stackTrace: s);
            return null;
          }
        },
        tag: _tag,
        tagWithTrace: false,
      );
    } catch (e, s) {
      LogUtil.e(e, stackTrace: s, tag: _tag);
    }
    handler.next(options);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    try {
      final options = response.requestOptions;
      final data = response.data;
      LogUtil.dd(
        () {
          final sb = StringBuffer(
            '<====\n'
            '${response.statusCode} ${options.method} '
            '${response.requestOptions.uri}\n',
          );
          if (data is Map || data is List) {
            sb.write(globalJsonEncoder.convert(data));
          } else if (data != null) {
            sb.write(data.toString());
          }
          return sb.toString();
        },
        tag: _tag,
        tagWithTrace: false,
      );
      if (data is String && data.isEmpty) {
        if (response.requestOptions.headers[Headers.contentTypeHeader] case final String? contentType
            when contentType == Headers.jsonContentType) {
          response.data = <String, dynamic>{};
        } else {
          response.data = null;
        }
      }
    } catch (e, s) {
      LogUtil.e(e, stackTrace: s, tag: _tag);
    }
    handler.next(response);
  }

  @override
  void onError(DioException e, ErrorInterceptorHandler handler) {
    if (e.type == DioExceptionType.badResponse) {
      final code = e.response?.statusCode ?? 0;
      if (code >= io.HttpStatus.multipleChoices && code <= io.HttpStatus.permanentRedirect) {
        handler.next(e);
        return;
      }
    }
    if (e.type == DioExceptionType.cancel) {
      LogUtil.w(
        '\n${e.response?.realUri ?? e.requestOptions.uri}\n$e',
        stackTrace: e.stackTrace,
        tag: _tag,
      );
      handler.next(e);
      return;
    }
    // Escape underlying exceptions.
    if (isNetworkError(e.error)) {
      handler.next(e);
      return;
    }
    try {
      LogUtil.e(
        () {
          final res = e.response;
          final options = res?.requestOptions ?? e.requestOptions;
          final sb = StringBuffer();
          sb.writeln('Network exceptions <${res?.statusCode}> ');
          sb.writeln('  [U]: ${options.method.toUpperCase()} ${options.uri}');
          if (res?.realUri case final uri? when uri != options.uri) {
            sb.writeln('  [Ur]: ${res?.realUri}');
          }
          sb.writeln('  [E]: <${e.type.name}> ${e.error}');
          // Censors authorization when sealed.
          if (options.headers.isNotEmpty && isSealed) {
            final censored = Map.from(options.headers);
            if (censored[io.HttpHeaders.authorizationHeader] case final String v when v.isNotEmpty) {
              censored[io.HttpHeaders.authorizationHeader] = '********';
            }
            sb.writeln('  [H]: $censored');
          }
          if (options.data != null) {
            sb.writeln('  [Request]: ${options.data}');
          }
          if (res?.data != null) {
            sb.writeln('  [Response]: ${res?.data}');
          }
          return sb.toString();
        }(),
        tag: _tag,
        tagWithTrace: false,
      );
    } catch (e, s) {
      LogUtil.e(e, stackTrace: s, tag: _tag);
    }
    handler.next(e);
  }
}

class _AuthInterceptor extends QueuedInterceptor {
  static const _tag = '🔑 AuthInterceptor';

  void _handleAstroX(RequestOptions options) {
    final now = DateTime.now();
    options.headers.addAll(
      <String, dynamic>{
        'API-Authorization': envApiKeyAstroxOp,
        'OS-Version': DeviceUtil.osVersion,
        'OS-Name': DeviceUtil.osName,
        'Device-Model': DeviceUtil.iOSInfo?.forLog ?? DeviceUtil.androidInfo?.forLog,
        'Client-UUID': DeviceUtil.deviceUuid,
        'Client-Build': PackageUtil.buildString,
        'Client-Channel': Release.channel,
        'Client-Timezone': now.timeZoneName,
        'Client-TimezoneOffset': now.timeZoneOffset.inMilliseconds.toString(),
      }..removeWhere((key, value) => value == null || value.isEmpty),
    );
    options.extra[httpExtraKeySucceedCode] = null;
  }

  void _onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    final url = options.uri.toString();
    if (url.startsWith(envApiUrlAstroxOp)) {
      _handleAstroX(options);
      handler.next(options);
      return;
    }
    final allowedUrl = [envApiUrlService];
    // Skip when the request is not an allowed api request.
    if (!allowedUrl.any((e) => url.startsWith(e))) {
      handler.next(options);
      return;
    }
    // Skip when a valid authorization header is provided.
    if (options.headers[io.HttpHeaders.authorizationHeader] != null) {
      handler.next(options);
      return;
    }
    final token = EnvOverrides.jwtOverrode ?? BoxService.getToken();
    if (token == null || token.isEmpty) {
      handler.next(options);
      return;
    }
    LogUtil.d(
      'API token: $token',
      enabled: kDebugMode,
      tag: _tag,
      tagWithTrace: false,
    );
    options.headers[io.HttpHeaders.authorizationHeader] = 'Bearer $token';
    handler.next(options);
  }

  @override
  void onRequest(options, handler) {
    try {
      _onRequest(options, handler);
    } catch (e, s) {
      LogUtil.e(e, stackTrace: s, tag: _tag);
    }
  }
}

class _ApiCodeInterceptor extends QueuedInterceptor {
  static const _tag = '🅰️ ApiCodeInterceptor';

  @override
  void onResponse(response, handler) {
    try {
      final options = response.requestOptions;

      // Skip when the response is not JSON or does not contain "code".
      final json = response.data;
      if (json is! Map<String, dynamic> || !json.containsKey('code')) {
        return;
      }

      final Object? succeedCode;
      if (options.extra.containsKey(httpExtraKeySucceedCode)) {
        succeedCode = options.extra[httpExtraKeySucceedCode];
      } else {
        succeedCode = Rep.defaultSucceedCode;
      }
      // Ignore when the succeed code is defined but null.
      if (succeedCode == null) {
        return;
      }

      final Object code = json['code'];
      // Skip when succeed or the code has already defined.
      if (code == succeedCode) {
        return;
      }

      // Fill keys.
      json.putIfAbsent('message', () => null);
      json.putIfAbsent('msg', () => null);
      json.putIfAbsent('data', () => null);

      final {
        'message': String? message,
        'msg': String? msg,
        'data': Object? d,
      } = json;
      final buffer = StringBuffer('Service API exception:\n');
      buffer.writeln(
        '  [E] code: $code, message: ${message ?? msg}, data: $json',
      );
      buffer.writeln(
        '  [U] (${response.statusCode}) ${options.method.toUpperCase()} '
        '${response.realUri}',
      );
      if (options.queryParameters case final q when q.isNotEmpty) {
        buffer.writeln('  [Q] $q');
      }
      if (options.data case final d?) {
        buffer.writeln('  [D] $d');
      }
      if (options.headers[io.HttpHeaders.authorizationHeader] case String authorization when authorization.isNotEmpty) {
        // Censors authorization when sealed.
        if (isSealed) {
          authorization = '********';
        }
        buffer.writeln('  [A] $authorization');
      }
      LogUtil.e(
        buffer.toString(),
        // ignore: invalid_use_of_internal_member
        stackTrace: options.sourceStackTrace,
        tag: _tag,
        tagWithTrace: false,
        report: kReleaseMode,
      );
    } catch (e, s) {
      LogUtil.e(e, stackTrace: s, tag: _tag, tagWithTrace: false);
    } finally {
      handler.next(response);
    }
  }
}

typedef ObjectFactory<T> = T Function(Object? v);

@Freezed(genericArgumentFactories: true, fromJson: true, toJson: true)
sealed class Rep<T> with _$Rep<T> {
  const factory Rep({
    @JsonKey(name: 'code') required Object code,
    @JsonKey(name: 'message', readValue: apiFromJsonMessage) @Default('') String message,
    @JsonKey(name: 'data') required T data,
    @JsonKey(includeFromJson: false, includeToJson: false) @Default(Rep.defaultSucceedCode) Object succeedCode,
  }) = _Rep<T>;

  const Rep._();

  factory Rep.fromJson(
    Map<String, Object?> json,
    ObjectFactory<T> factory, {
    Object succeedCode = Rep.defaultSucceedCode,
    bool throwWhenNotSucceed = true,
  }) {
    if (throwWhenNotSucceed) {
      apiThrowWhenNotSucceed(json, succeedCode);
    }
    return _$RepFromJson(json, factory).copyWith(succeedCode: succeedCode);
  }

  static const defaultSucceedCode = 200;

  bool get succeed => succeedCode == code;
}

@Freezed(genericArgumentFactories: true, fromJson: true, toJson: true)
sealed class ListRep<T> with _$ListRep<T> {
  const factory ListRep({
    @JsonKey(name: 'code') required Object code,
    @JsonKey(name: 'message', readValue: apiFromJsonMessage) @Default('') String message,
    @JsonKey(name: 'data') @Default([]) List<T> list,
    @JsonKey(includeFromJson: false, includeToJson: false) @Default(Rep.defaultSucceedCode) Object succeedCode,
  }) = _ListRep<T>;

  const ListRep._();

  factory ListRep.fromJson(
    Map<String, Object?> json,
    ObjectFactory<T> factory, {
    Object succeedCode = Rep.defaultSucceedCode,
  }) {
    apiThrowWhenNotSucceed(json, succeedCode);
    return _$ListRepFromJson(json, factory).copyWith(succeedCode: succeedCode);
  }

  factory ListRep.empty({Object succeedCode = Rep.defaultSucceedCode}) {
    return ListRep<T>(code: succeedCode, message: '', list: []);
  }

  bool get succeed => succeedCode == code;
}

@Freezed(genericArgumentFactories: true)
sealed class Paged<T> with _$Paged<T> {
  const factory Paged({
    @JsonKey(name: 'current') required int page,
    @JsonKey(name: 'size') required int size,
    @JsonKey(name: 'pages') required int pages,
    @JsonKey(name: 'total') @Default(0) int total,
    @JsonKey(name: 'records') @Default([]) List<T> list,
  }) = _Paged<T>;

  factory Paged.fromJson(Map<String, Object?> json, ObjectFactory<T> factory) => _$PagedFromJson(json, factory);

  factory Paged.empty({int page = 1}) {
    return Paged<T>(page: page, size: 0, pages: 0, total: 0, list: []);
  }
}

String apiFromJsonMessage(Map json, String key) {
  final allowedKeys = {key, 'message', 'msg'};
  for (final key in allowedKeys) {
    if (json[key] case final String message when message.isNotEmpty) {
      return message;
    }
  }
  return '';
}

void apiThrowWhenNotSucceed(Map json, Object succeedCode) {
  if (json['success'] == true || json['code'] == succeedCode) {
    return;
  }
  throw ApiException(
    code: json['code'] ?? -1,
    message: json['message'] ?? json['msg'],
    data: json['data'],
  );
}

@freezed
sealed class ApiException with _$ApiException {
  const factory ApiException({
    // Typically `String` or `int`.
    required Object code,
    String? message,
    Object? data,
  }) = _ApiException;

  factory ApiException.fromJson(Map<String, dynamic> json) => _$ApiExceptionFromJson(json);
}

final apiAstroxProvider = Provider<AstroxApi>(AstroxApi.new);
final apiAstroxGwProvider = Provider<AstroxGwApi>(AstroxGwApi.new);
final apiFrontEndProvider = Provider<FrontEndApi>(FrontEndApi.new);
final apiOKXProvider = Provider<OKXApi>(OKXApi.new);
final apiOkxOldProvider = Provider<OkxOldApi>(OkxOldApi.new);
final apiServiceProvider = Provider<ServiceApi>(ServiceApi.new);
final apiSolanaProvider = Provider<SolanaApi>(SolanaApi.new);
