// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'token.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$fetchRawWalletPortfolioOKXHash() =>
    r'dafa29d14e74a247e2e0dcb013d7d48a6c438c6e';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [fetchRawWalletPortfolioOKX].
@ProviderFor(fetchRawWalletPortfolioOKX)
const fetchRawWalletPortfolioOKXProvider = FetchRawWalletPortfolioOKXFamily();

/// See also [fetchRawWalletPortfolioOKX].
class FetchRawWalletPortfolioOKXFamily
    extends Family<AsyncValue<WalletPortfolioOKX2>> {
  /// See also [fetchRawWalletPortfolioOKX].
  const FetchRawWalletPortfolioOKXFamily();

  /// See also [fetchRawWalletPortfolioOKX].
  FetchRawWalletPortfolioOKXProvider call({
    required Network? network,
    required String? address,
    bool writeToCache = true,
  }) {
    return FetchRawWalletPortfolioOKXProvider(
      network: network,
      address: address,
      writeToCache: writeToCache,
    );
  }

  @override
  FetchRawWalletPortfolioOKXProvider getProviderOverride(
    covariant FetchRawWalletPortfolioOKXProvider provider,
  ) {
    return call(
      network: provider.network,
      address: provider.address,
      writeToCache: provider.writeToCache,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'fetchRawWalletPortfolioOKXProvider';
}

/// See also [fetchRawWalletPortfolioOKX].
class FetchRawWalletPortfolioOKXProvider
    extends AutoDisposeFutureProvider<WalletPortfolioOKX2> {
  /// See also [fetchRawWalletPortfolioOKX].
  FetchRawWalletPortfolioOKXProvider({
    required Network? network,
    required String? address,
    bool writeToCache = true,
  }) : this._internal(
         (ref) => fetchRawWalletPortfolioOKX(
           ref as FetchRawWalletPortfolioOKXRef,
           network: network,
           address: address,
           writeToCache: writeToCache,
         ),
         from: fetchRawWalletPortfolioOKXProvider,
         name: r'fetchRawWalletPortfolioOKXProvider',
         debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
             ? null
             : _$fetchRawWalletPortfolioOKXHash,
         dependencies: FetchRawWalletPortfolioOKXFamily._dependencies,
         allTransitiveDependencies:
             FetchRawWalletPortfolioOKXFamily._allTransitiveDependencies,
         network: network,
         address: address,
         writeToCache: writeToCache,
       );

  FetchRawWalletPortfolioOKXProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.network,
    required this.address,
    required this.writeToCache,
  }) : super.internal();

  final Network? network;
  final String? address;
  final bool writeToCache;

  @override
  Override overrideWith(
    FutureOr<WalletPortfolioOKX2> Function(
      FetchRawWalletPortfolioOKXRef provider,
    )
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: FetchRawWalletPortfolioOKXProvider._internal(
        (ref) => create(ref as FetchRawWalletPortfolioOKXRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        network: network,
        address: address,
        writeToCache: writeToCache,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<WalletPortfolioOKX2> createElement() {
    return _FetchRawWalletPortfolioOKXProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FetchRawWalletPortfolioOKXProvider &&
        other.network == network &&
        other.address == address &&
        other.writeToCache == writeToCache;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, network.hashCode);
    hash = _SystemHash.combine(hash, address.hashCode);
    hash = _SystemHash.combine(hash, writeToCache.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin FetchRawWalletPortfolioOKXRef
    on AutoDisposeFutureProviderRef<WalletPortfolioOKX2> {
  /// The parameter `network` of this provider.
  Network? get network;

  /// The parameter `address` of this provider.
  String? get address;

  /// The parameter `writeToCache` of this provider.
  bool get writeToCache;
}

class _FetchRawWalletPortfolioOKXProviderElement
    extends AutoDisposeFutureProviderElement<WalletPortfolioOKX2>
    with FetchRawWalletPortfolioOKXRef {
  _FetchRawWalletPortfolioOKXProviderElement(super.provider);

  @override
  Network? get network =>
      (origin as FetchRawWalletPortfolioOKXProvider).network;
  @override
  String? get address => (origin as FetchRawWalletPortfolioOKXProvider).address;
  @override
  bool get writeToCache =>
      (origin as FetchRawWalletPortfolioOKXProvider).writeToCache;
}

String _$fetchWalletPortfolioOKXHash() =>
    r'0dc1cfaee01177e224778809e1b5f54949039a66';

/// See also [fetchWalletPortfolioOKX].
@ProviderFor(fetchWalletPortfolioOKX)
const fetchWalletPortfolioOKXProvider = FetchWalletPortfolioOKXFamily();

/// See also [fetchWalletPortfolioOKX].
class FetchWalletPortfolioOKXFamily
    extends Family<AsyncValue<WalletPortfolioOKX2>> {
  /// See also [fetchWalletPortfolioOKX].
  const FetchWalletPortfolioOKXFamily();

  /// See also [fetchWalletPortfolioOKX].
  FetchWalletPortfolioOKXProvider call({required Network? network}) {
    return FetchWalletPortfolioOKXProvider(network: network);
  }

  @override
  FetchWalletPortfolioOKXProvider getProviderOverride(
    covariant FetchWalletPortfolioOKXProvider provider,
  ) {
    return call(network: provider.network);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'fetchWalletPortfolioOKXProvider';
}

/// See also [fetchWalletPortfolioOKX].
class FetchWalletPortfolioOKXProvider
    extends AutoDisposeStreamProvider<WalletPortfolioOKX2> {
  /// See also [fetchWalletPortfolioOKX].
  FetchWalletPortfolioOKXProvider({required Network? network})
    : this._internal(
        (ref) => fetchWalletPortfolioOKX(
          ref as FetchWalletPortfolioOKXRef,
          network: network,
        ),
        from: fetchWalletPortfolioOKXProvider,
        name: r'fetchWalletPortfolioOKXProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$fetchWalletPortfolioOKXHash,
        dependencies: FetchWalletPortfolioOKXFamily._dependencies,
        allTransitiveDependencies:
            FetchWalletPortfolioOKXFamily._allTransitiveDependencies,
        network: network,
      );

  FetchWalletPortfolioOKXProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.network,
  }) : super.internal();

  final Network? network;

  @override
  Override overrideWith(
    Stream<WalletPortfolioOKX2> Function(FetchWalletPortfolioOKXRef provider)
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: FetchWalletPortfolioOKXProvider._internal(
        (ref) => create(ref as FetchWalletPortfolioOKXRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        network: network,
      ),
    );
  }

  @override
  AutoDisposeStreamProviderElement<WalletPortfolioOKX2> createElement() {
    return _FetchWalletPortfolioOKXProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FetchWalletPortfolioOKXProvider && other.network == network;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, network.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin FetchWalletPortfolioOKXRef
    on AutoDisposeStreamProviderRef<WalletPortfolioOKX2> {
  /// The parameter `network` of this provider.
  Network? get network;
}

class _FetchWalletPortfolioOKXProviderElement
    extends AutoDisposeStreamProviderElement<WalletPortfolioOKX2>
    with FetchWalletPortfolioOKXRef {
  _FetchWalletPortfolioOKXProviderElement(super.provider);

  @override
  Network? get network => (origin as FetchWalletPortfolioOKXProvider).network;
}

String _$fetchSOLBalanceFromPortfolioOKXHash() =>
    r'b306eb4b1d75b2b6c49af646a25990154062c953';

/// See also [fetchSOLBalanceFromPortfolioOKX].
@ProviderFor(fetchSOLBalanceFromPortfolioOKX)
final fetchSOLBalanceFromPortfolioOKXProvider =
    AutoDisposeProvider<Decimal?>.internal(
      fetchSOLBalanceFromPortfolioOKX,
      name: r'fetchSOLBalanceFromPortfolioOKXProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$fetchSOLBalanceFromPortfolioOKXHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FetchSOLBalanceFromPortfolioOKXRef = AutoDisposeProviderRef<Decimal?>;
String _$tokenBalanceStreamHash() =>
    r'8e7f51668d09298630bf0bb484bbb28600abe18d';

/// See also [tokenBalanceStream].
@ProviderFor(tokenBalanceStream)
const tokenBalanceStreamProvider = TokenBalanceStreamFamily();

/// See also [tokenBalanceStream].
class TokenBalanceStreamFamily extends Family<AsyncValue<TokenAmount?>> {
  /// See also [tokenBalanceStream].
  const TokenBalanceStreamFamily();

  /// See also [tokenBalanceStream].
  TokenBalanceStreamProvider call({
    required Network? network,
    required String? address,
  }) {
    return TokenBalanceStreamProvider(network: network, address: address);
  }

  @override
  TokenBalanceStreamProvider getProviderOverride(
    covariant TokenBalanceStreamProvider provider,
  ) {
    return call(network: provider.network, address: provider.address);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'tokenBalanceStreamProvider';
}

/// See also [tokenBalanceStream].
class TokenBalanceStreamProvider
    extends AutoDisposeStreamProvider<TokenAmount?> {
  /// See also [tokenBalanceStream].
  TokenBalanceStreamProvider({
    required Network? network,
    required String? address,
  }) : this._internal(
         (ref) => tokenBalanceStream(
           ref as TokenBalanceStreamRef,
           network: network,
           address: address,
         ),
         from: tokenBalanceStreamProvider,
         name: r'tokenBalanceStreamProvider',
         debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
             ? null
             : _$tokenBalanceStreamHash,
         dependencies: TokenBalanceStreamFamily._dependencies,
         allTransitiveDependencies:
             TokenBalanceStreamFamily._allTransitiveDependencies,
         network: network,
         address: address,
       );

  TokenBalanceStreamProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.network,
    required this.address,
  }) : super.internal();

  final Network? network;
  final String? address;

  @override
  Override overrideWith(
    Stream<TokenAmount?> Function(TokenBalanceStreamRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: TokenBalanceStreamProvider._internal(
        (ref) => create(ref as TokenBalanceStreamRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        network: network,
        address: address,
      ),
    );
  }

  @override
  AutoDisposeStreamProviderElement<TokenAmount?> createElement() {
    return _TokenBalanceStreamProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is TokenBalanceStreamProvider &&
        other.network == network &&
        other.address == address;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, network.hashCode);
    hash = _SystemHash.combine(hash, address.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin TokenBalanceStreamRef on AutoDisposeStreamProviderRef<TokenAmount?> {
  /// The parameter `network` of this provider.
  Network? get network;

  /// The parameter `address` of this provider.
  String? get address;
}

class _TokenBalanceStreamProviderElement
    extends AutoDisposeStreamProviderElement<TokenAmount?>
    with TokenBalanceStreamRef {
  _TokenBalanceStreamProviderElement(super.provider);

  @override
  Network? get network => (origin as TokenBalanceStreamProvider).network;
  @override
  String? get address => (origin as TokenBalanceStreamProvider).address;
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
